import { iteratInfo, learnInfo, batchInfo } from '@/utils/conts';

// 默认配置值
export const DEFAULT_CONFIG = {
  iterationLevel: 50,
  learningValue: 50,
  batchValue: 50,
};

// 数值映射函数
export const iteratSetValueMap = (testSetStr: string) => {
  const index = iteratInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};

export const learnSetValueMap = (testSetStr: string) => {
  const index = learnInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};

export const batchSetValueMap = (testSetStr: string) => {
  const index = batchInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};

export const changeLearnValueMap = (num: number) => {
  switch (num) {
    case 0.00001:
      return 0;
    case 0.00005:
      return 25;
    case 0.0001:
      return 50;
    case 0.0005:
      return 75;
    case 0.001:
      return 100;
    default:
      return 0;
  }
};

// 获取初始训练设置
export const getInitialTrainingSettings = (
  iterationLevel?: string,
  learningValue?: string,
  batchValue?: string
) => ({
  iterationLevel: iterationLevel ? iteratSetValueMap(iterationLevel) : DEFAULT_CONFIG.iterationLevel,
  learningValue: learningValue ? learnSetValueMap(learningValue) : DEFAULT_CONFIG.learningValue,
  batchValue: batchValue ? batchSetValueMap(batchValue) : DEFAULT_CONFIG.batchValue,
});

// 重置为默认配置
export const resetToDefaultConfig = () => ({ ...DEFAULT_CONFIG });
