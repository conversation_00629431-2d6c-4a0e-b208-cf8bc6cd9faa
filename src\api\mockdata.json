{"children": [{"children": [{"children": [{"children": [{"childFileId": "b3cd6f5fa7ac0c3a882cc202c5958558", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试子压缩包/测试文件/文件夹1/测试文件3.txt", "children": [], "name": "测试文件3.txt", "status": "SUCCESS", "type": "FILE"}], "name": "文件夹1", "status": "SUCCESS", "type": "DIR"}, {"childFileId": "5b90fd58f2437bc1a86a8ab2d3e89eec", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试子压缩包/测试文件/文件夹1/测试文件1.txt", "children": [], "name": "测试文件1.txt", "status": "SUCCESS", "type": "FILE"}, {"childFileId": "cdc35e0f2adfe34b82182977b5950fb9", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试子压缩包/测试文件/文件夹1/测试文件2.txt", "children": [], "name": "测试文件2.txt", "status": "SUCCESS", "type": "FILE"}], "name": "测试文件", "status": "SUCCESS", "type": "DIR"}], "name": "测试子压缩包.zip", "status": "PARSING", "type": "ZIP"}, {"children": [{"children": [{"children": [{"childFileId": "0f91f4d3e1a256c43389d47c3dc5af42", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试文件/文件夹1/文件夹2/测试文件4.txt", "children": [], "name": "测试文件4.txt", "status": "SUCCESS", "type": "FILE"}], "name": "文件夹2", "status": "SUCCESS", "type": "DIR"}, {"childFileId": "39d251263e322ac110d573919d0f1e0f", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试文件/文件夹1/文件夹2/测试文件3.txt", "children": [], "name": "测试文件3.txt", "status": "SUCCESS", "type": "FILE"}], "name": "文件夹1", "status": "SUCCESS", "type": "DIR"}, {"childFileId": "dca9f37287064eeb001a5970d98ad767", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试文件/文件夹1/测试文件1.txt", "children": [], "name": "测试文件1.txt", "status": "SUCCESS", "type": "FILE"}, {"childFileId": "40f0c70752eb039bf86b3eb8375ebe54", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试文件/文件夹1/测试文件2.txt", "children": [], "name": "测试文件2.txt", "status": "SUCCESS", "type": "FILE"}], "name": "测试文件", "status": "SUCCESS", "type": "DIR"}, {"childFileId": "8fe8aa84062a11bac5ea536449afe21bA", "childFilePath": "http://*************:4005/static-pollux/upload_files/ab2fad7820cdf9a99a006664abc87dfd/raw/测试文件树生成/测试文件/测试文件.txt", "children": [], "name": "测试文件.txt", "status": "SUCCESS", "type": "FILE"}], "name": "测试文件树生成.zip", "status": "PARSING", "type": "ZIP"}