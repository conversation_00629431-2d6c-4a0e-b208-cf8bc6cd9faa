import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "antd";
import { useRef, useState } from "react";
import {
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons';
import html2canvas from 'html2canvas';
import avatar1 from '../assets/img/avatar-1.png';
import avatar2 from '../assets/img/avatar-2.png';
import avatar3 from '../assets/img/avatar-3.png';
import avatar4 from '../assets/img/avatar-4.png';
import avatar5 from '../assets/img/avatar-5.png';
import avatar6 from '../assets/img/avatar-6.png';
import avatar7 from '../assets/img/avatar-7.png';
import laptop from '../assets/img/laptop.svg';

interface EditAvatarModalProp {
  userName: string,
  visible: boolean;
  OnClose: () => void;
}

const EditAvatarModal: React.FC<EditAvatarModalProp> = ({
  userName,
  visible,
  OnClose,
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [avatar, setAvatar] = useState<string>(avatar1);
  const [scrollX, setScrollX] = useState(0);
  const images: string[] = [
    // "/avatar-1.png",
    // "/avatar-2.png",
    // "/avatar-3.png",
    // "/avatar-4.png",
    // "/avatar-5.png",
    // "/avatar-6.png",
    // "/avatar-7.png",
    avatar1,
    avatar2,
    avatar3,
    avatar4,
    avatar5,
    avatar6,
    avatar7,
  ];
  const handleScroll = (offset: number) => {
    setScrollX((prev) => {
      return prev + offset > 0
        ? 0
        : prev + offset <= -260
          ? -260
          : prev + offset;
    });
  };

  const onUpdateUserInfo = (file?: File) => {
    if (!file) {
    }
  };

  return (
    <Modal
      centered
      title="用户头像"
      width={"28%"}
     
      open={visible}
      onOk={OnClose}
      onCancel={OnClose}
      footer={[]}
    >
      <div
        style={{
          display: "inline-flex",
          alignItems: "center",
          paddingTop: "40px",
          width: "100%",
          justifyContent: "center",
          flexDirection: "column",
          gap: "24px",
        }}
      >
        <Avatar size={140} src={avatar} />
        <Button
          type="link"
          style={{ color: "#111111" }}
          onClick={() => {
            document.getElementById("uploadAvatar")?.click();
          }}
        >
          <img src={laptop} style={{ marginRight: "8px" }} />
          选择本地图片
        </Button>
        <input
          type="file"
          style={{ display: "none" }}
          id="uploadAvatar"
          accept="image/*"
          onChange={(event) => {
            const files = event.target.files;
            if (files && files[0]) {
              const imageFile = files[0];
              onUpdateUserInfo(imageFile);
              // setAvatar(imageFile);
              // 更新用户头像
            }
          }}
        ></input>
      </div>
      <div className="edit-avatar-list">
        {scrollX >= 0 ? null : (
          <Button
            className="edit-avatar-pre-btn"
            onClick={() => handleScroll(80)}
          >
            <LeftOutlined />
          </Button>
        )}
        <div>预设头像</div>
        <div
          style={{
            display: "inline-flex",
            gap: "12px",
            marginTop: "20px",
            transform: `translateX(${scrollX}px)`,
            transition: "transform 400ms ease-in-out 0s",
          }}
        >
          <div
            style={{
              borderRadius: "50%",
              width: "80px",
              height: "80px",
              overflow: "hidden",
              display: "flex",
              justifyContent: "space-evenly",
              alignItems: "center",
            }}
          >
            <Avatar
              ref={elementRef}
              size={83}
              style={{
                background:
                  "linear-gradient(313.78deg, #0FB698 16.15%, #113932 81.12%)",
                fontStyle: "normal",
                fontWeight: 900,
                fontSize: "16px",
                lineHeight: "19px",
                color: "#FFFFFF",
              }}
              onClick={async () => {
                if (elementRef.current) {
                  const canvas = await html2canvas(elementRef.current);
                  const base64Image = canvas.toDataURL();
                  console.log(base64Image);
                  setAvatar(base64Image);
                }
              }}
            >
              <label className="blackText" style={{ fontSize: 32 }}>{userName ? userName[0] : null}</label>
            </Avatar>
          </div>
          {images.map((image, index) => (
            <img
              style={{ cursor: "pointer" }}
              width={80}
              height={80}
              key={image}
              src={image}
              alt={image}
              onClick={() => setAvatar(image)}
            />
          ))}
        </div>
        {scrollX <= -260 ? null : (
          <Button
            className="edit-avatar-next-btn"
            onClick={() => handleScroll(-80)}
          >
            <RightOutlined />
          </Button>
        )}
      </div>
    </Modal>
  );
};

export default EditAvatarModal;