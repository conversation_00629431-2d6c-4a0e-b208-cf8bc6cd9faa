import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Switch, Spin, message } from 'antd';
import { QADocument, TaskDetailType } from '../../../types';
import { QaUpdateParams, updateQA } from '../../../api/qa';
import { ScoreReviewParams, scoreReview } from '../../../api/review';

interface ReviewActionsProps {
  question: QADocument;
  taskDetail: TaskDetailType;
  score: string;
  autoSwitch: boolean;
  currentQues: string;
  currentAnsw: string;
  editQuestion: boolean;
  editAnswer: boolean;
  isReviewing: boolean;
  onAutoSwitchChange: (checked: boolean) => void;
  onRefresh: () => void;
  onReviewingChange: (isReviewing: boolean) => void;
  onSetNextQA: () => void;
}

const ReviewActions: React.FC<ReviewActionsProps> = ({
  question,
  taskDetail,
  score,
  autoSwitch,
  currentQues,
  currentAnsw,
  editQuestion,
  editAnswer,
  isReviewing,
  onAutoSwitchChange,
  onRefresh,
  onReviewingChange,
  onSetNextQA,
}) => {
  const handleCompleteReview = async () => {
    // 更新QA
    if (question.modify || !editQuestion || !editAnswer) {
      const params: QaUpdateParams = {
        answer: currentAnsw,
        id: question.id,
        question: currentQues,
        taskId: taskDetail.id,
      };
      updateQA(params).then(async (res) => {
        onReviewingChange(true);
        if (res.data?.code === 200 && taskDetail) {
          const review: ScoreReviewParams = {
            id: question.id,
            taskId: taskDetail.id,
            score: score,
          };
          await scoreReview(review).then((res) => {
            if (res.data?.code === 200) {
              message.success('审核成功');
            } else {
              message.error(res.data?.message);
            }
          });
          onRefresh();
          onReviewingChange(false);
        } else {
          message.error(res.data?.message);
          onReviewingChange(false);
        }
      });
    } else {
      onReviewingChange(true);
      const review: ScoreReviewParams = {
        id: question.id,
        taskId: taskDetail.id,
        score: score,
      };
      await scoreReview(review);
      onRefresh();
      onReviewingChange(false);
    }
  };

  return (
    <>
      <div
        style={{
          bottom: '0.2rem',
          textAlign: 'end',
          position: 'absolute',
          right: '1rem',
        }}
      >
        <Space size={16}>
          {question.review ? (
            <Button
              type="primary"
              className="review-btn-default"
              disabled
              onClick={() => {
                onSetNextQA();
              }}
            >
              已审核，下一组
            </Button>
          ) : (
            <Button
              type="primary"
              className="review-btn-default"
              disabled={!score}
              onClick={handleCompleteReview}
            >
              完成审核，下一条
              {isReviewing && <Spin />}
            </Button>
          )}
        </Space>
      </div>
    </>
  );
};

export default ReviewActions;
