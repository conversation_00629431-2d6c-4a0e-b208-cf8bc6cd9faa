import React from 'react';
import { Form, Segmented, Tooltip, Slider, Divider } from 'antd';
import { SliderMarks } from 'antd/es/slider';
import { ModelConfigType, CreateTaskFormType } from '../types';
import { paragraph, questionDensity } from '../../../../utils/conts';
import { TOOLTIP_MESSAGES, BUTTON_TEXTS, LABELS, STYLES, SLIDER_CONFIG } from '../constants';
import group151 from '../../../../assets/img/group-151.svg';

interface ModelConfigSectionProps {
  modelConfig: ModelConfigType;
  splitLevel: number;
  questionDensityValue: number;
  onModelConfigChange: (config: ModelConfigType) => void;
  onSplitLevelChange: (level: number) => void;
  onQuestionDensityChange: (density: number) => void;
}

const ModelConfigSection: React.FC<ModelConfigSectionProps> = ({
  modelConfig,
  splitLevel,
  questionDensityValue,
  onModelConfigChange,
  onSplitLevelChange,
  onQuestionDensityChange,
}) => {
  // 生成段落精细度滑块标记
  const paragraphMarks: SliderMarks = {
    0: (
      <Tooltip title={paragraph[0]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    25: (
      <Tooltip title={paragraph[1]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    50: (
      <Tooltip title={paragraph[2]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    75: (
      <Tooltip title={paragraph[3]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    100: (
      <Tooltip title={paragraph[4]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
  };

  // 生成提问密度滑块标记
  const questionDensityMarks: SliderMarks = {
    0: (
      <Tooltip title={questionDensity[0]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    25: (
      <Tooltip title={questionDensity[1]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    50: (
      <Tooltip title={questionDensity[2]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    75: (
      <Tooltip title={questionDensity[3]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
    100: (
      <Tooltip title={questionDensity[4]}>
        <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
      </Tooltip>
    ),
  };

  return (
    <>
      <Form.Item<CreateTaskFormType> label={LABELS.MODEL_PARAMS}>
        <Segmented
          className="createtask-segmented"
          size="large"
          options={[
            {
              label: (
                <Tooltip title={BUTTON_TEXTS.AUTO_CONFIG}>
                  <a
                    onClick={() => onModelConfigChange('自动配置')}
                    className={
                      modelConfig === '自动配置' ? 'model-config-active' : 'model-config'
                    }
                  >
                    {BUTTON_TEXTS.AUTO_CONFIG}
                  </a>
                </Tooltip>
              ),
              value: '自动配置',
            },
            {
              label: (
                <Tooltip title={BUTTON_TEXTS.MANUAL_CONFIG}>
                  <a
                    onClick={() => onModelConfigChange('手动配置')}
                    className={
                      modelConfig === '手动配置' ? 'model-config-active' : 'model-config'
                    }
                  >
                    {BUTTON_TEXTS.MANUAL_CONFIG}
                  </a>
                </Tooltip>
              ),
              value: '手动配置',
            },
          ]}
          value={modelConfig}
          onChange={onModelConfigChange}
        />
      </Form.Item>
      
      {modelConfig === '手动配置' && (
        <div>
          <div className="createTaskItem">
            <div className="customConfigLabel">
              <label className="crateTaskDraggerLabel reqularText">
                {LABELS.PARAGRAPH_FINENESS}
              </label>
              <Tooltip title={TOOLTIP_MESSAGES.PARAGRAPH_FINENESS}>
                <img className="frame-child179" src={group151} />
              </Tooltip>
            </div>
            <Form.Item<CreateTaskFormType> name="splitLevel" noStyle>
              <Slider
                defaultValue={splitLevel}
                className="create-task-slider"
                dots
                tooltip={{
                  formatter: (val) => {
                    const q = val ? val : 0;
                    return paragraph[q / 25];
                  },
                }}
                step={SLIDER_CONFIG.STEP}
                marks={paragraphMarks}
                value={splitLevel}
                onChange={onSplitLevelChange}
                railStyle={STYLES.SLIDER.RAIL}
                trackStyle={STYLES.SLIDER.TRACK}
                handleStyle={{}}
                style={{
                  width: STYLES.SLIDER.WIDTH,
                  display: 'inline-flex',
                  margin: 'unset',
                }}
              />
            </Form.Item>
            <label
              className="info-label"
              style={{ display: 'inline-block', marginLeft: '1rem' }}
            >
              <div className="slider-label reqularText">{paragraph[splitLevel / 25]}</div>
            </label>
          </div>
          
          <div className="createTaskItem" style={{ margin: '1rem 0' }}>
            <div className="customConfigLabel">
              <label className="crateTaskDraggerLabel reqularText">
                {LABELS.QUESTION_DENSITY}
              </label>
              <Tooltip title={TOOLTIP_MESSAGES.QUESTION_DENSITY}>
                <img className="frame-child179" src={group151} />
              </Tooltip>
            </div>
            <Form.Item<CreateTaskFormType> name="questionDensity" noStyle>
              <Slider
                defaultValue={questionDensityValue}
                className="create-task-slider"
                dots
                tooltip={{
                  formatter: (val) => {
                    const q = val ? val : 0;
                    return questionDensity[q / 25];
                  },
                }}
                step={SLIDER_CONFIG.STEP}
                marks={questionDensityMarks}
                value={questionDensityValue}
                onChange={onQuestionDensityChange}
                railStyle={STYLES.SLIDER.RAIL}
                trackStyle={STYLES.SLIDER.TRACK}
                handleStyle={{}}
                style={{
                  width: STYLES.SLIDER.WIDTH,
                  display: 'inline-flex',
                  margin: 'unset',
                }}
              />
            </Form.Item>
            <label
              className="info-label"
              style={{ display: 'inline-block', marginLeft: '1rem' }}
            >
              <div className="slider-label reqularText">
                {questionDensity[questionDensityValue / 25]}
              </div>
            </label>
          </div>
          <Divider />
        </div>
      )}
    </>
  );
};

export default ModelConfigSection;
