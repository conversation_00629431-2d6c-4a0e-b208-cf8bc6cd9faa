import { AxiosResponse } from "axios";
import request from "../../utils/request";
import { UserInfo } from "./data";

/**
 * 用户信息
 * @param UserInfoParams 用户信息
 * @returns
 */
export async function queryUserInfo(
): Promise<AxiosResponse<UserInfo>|undefined> {
    try {
        const url = `/user`;
        const res = await request.get(url);
        return res.data
    } catch (e) {
        return undefined
    }
}