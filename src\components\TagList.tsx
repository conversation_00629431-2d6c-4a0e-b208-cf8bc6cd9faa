import { Badge, Button, Input, InputRef, Space, Tag, message } from "antd";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { PlusOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { changeDataSetsTags } from "../api/dataset";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";

interface TagListProps {
  tagList: string[];
  dataSetId: string;
  onChange?: (tags: string[]) => void;
  OnEdit?: (edit: boolean) => void;
  showAddBtn: boolean;
  AddBtnLabel?: string;
  isClose?: boolean;
}

const tagInputStyle: React.CSSProperties = {
  width: 64,
  height: 22,
  marginInlineEnd: 8,
  verticalAlign: "top",
};

const tagPlusStyle: React.CSSProperties = {
  height: 22,
  // background: token.colorBgContainer,
  borderStyle: "dashed",
};

const tagStyle: React.CSSProperties = {
  borderRadius: "var(--br-9xs)",
  backgroundColor: "var(--color-skyblue)",
  color: "var(--color-steelblue-100)",
  padding: "var(--padding-10xs) 0.5rem",
  fontSize: "14px",
};

const TagList:React.FC<TagListProps> = ({ tagList, onChange, showAddBtn, AddBtnLabel, OnEdit, isClose }) => {
  // const { tagList, onChange, showAddBtn, AddBtnLabel, OnEdit } = prop;

  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [scrollX, setScrollX] = useState(0);
  const inputRef = useRef<InputRef>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showButtons, setShowButtons] = useState(false);
  const [displayTagList, setDisplatTagList] = useState<string[]>(tagList||[]);

  useEffect(() => {
    setDisplatTagList(tagList);
    // updateTagList();
  }, []);

  useEffect(() => {
    updateTagList();
  }, [displayTagList]);

  useEffect(() => {
    updateTagList();
  }, [scrollX]);

  useEffect(() => {
    console.log('showButtons',showButtons);
  }, [showButtons]);

  const showInput = () => {
    if (OnEdit) OnEdit(true);
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const onReset = () => {
    setScrollX(0);
    // setShowButtons(false);
  };

  const updateTagList = () => {
    if (containerRef.current) {
      const container = containerRef.current;
      if (scrollX === 0) {
        setShowButtons(
          containerRef.current.scrollWidth > containerRef.current.clientWidth
        );
      } else {
        setShowButtons(scrollX > container.clientWidth - container.scrollWidth);
      }
    }
  };

  const handleInputConfirm = () => {
    console.log("handleInputConfirm");
    if (OnEdit) OnEdit(false);
    if (inputValue && !displayTagList.includes(inputValue)) {
      const tags = [...displayTagList, inputValue];
      setDisplatTagList(tags);
      if (onChange) onChange(tags);
      updateTagList();
    }
    setInputVisible(false);
    setInputValue("");
  };

  const handleScroll = (offset: number) => {
    if (containerRef.current) {
      const container = containerRef.current;
      setScrollX((prev) => {
        return prev + offset > 0 ? 0 : prev + offset;
        // prev + offset <= container.clientWidth - container.scrollWidth
        //   ? container.clientWidth - container.scrollWidth
        //   : prev + offset;
      });
    }
  };
  
  return (
    <div
      style={{ display: "flex", alignItems: "center" }}
      onMouseLeave={() => {
        onReset();
      }}
    >
      {scrollX >= 0 ? null : (
        <Button
          style={{ position: "relative", left: "0" }}
          type="text"
          size="small"
          onClick={() => handleScroll(80)}
        >
          <LeftOutlined style={{ fontSize: "12px" }} />
        </Button>
      )}
      <div className="tag-list ant-table-cell ant-table-cell-ellipsis" ref={containerRef}>
        <Space>
          <div
            style={{
              display: "inline-flex",
              transform: `translateX(${scrollX}px)`,
              transition: "transform 400ms ease-in-out 0s",
            }}
          >
            {displayTagList.map((tag, index) => {
              return (
                <Tag
                  className="tag-item reqularText"
                  bordered={false}
                  key={index}
                  style={tagStyle}
                  onClose={(e) => {
                    e.preventDefault();
                    const updateTagList = displayTagList?.filter(
                      (tag, i) => i !== index
                    );
                    setDisplatTagList(updateTagList);
                    if (onChange) onChange(updateTagList);
                  }}
                  closable={isClose ? true : false}
                  closeIcon={
                    <a
                      style={{ position: "absolute", top: "-8px" }}
                      className="tag-item-close-btn"
                    >
                      <Badge
                        count={
                          <CloseCircleOutlined
                            style={{
                              color: "white",
                              backgroundColor: "#a5b1c5",
                              borderRadius: "50%",
                              cursor: "pointer",
                              fontSize: "12px",
                            }}
                          />
                        }
                      />
                    </a>
                  }
                >
                  {tag}
                </Tag>
              );
            })}
            {showAddBtn ? (
              inputVisible ? (
                <Input
                  autoFocus
                  maxLength={5}
                  ref={inputRef}
                  type="text"
                  size="small"
                  style={tagInputStyle}
                  value={inputValue}
                  onChange={handleInputChange}
                  onBlur={handleInputConfirm}
                  onPressEnter={handleInputConfirm}
                />
              ) : (
                <div style={{ width: "28px", height: "22px" }}>
                  <Tag
                    style={tagPlusStyle}
                    icon={<PlusOutlined />}
                    onClick={showInput}
                    className="add-btn"
                  >
                    {AddBtnLabel}
                  </Tag>
                </div>
              )
            ) : (
              <div style={{ width: "28px", height: "22px" }}></div>
            )}
          </div>
        </Space>
      </div>
      {!showButtons ? null : (
        <Button
          //   className="edit-avatar-next-btn"
          style={{ position: "relative", right: "0" }}
          type="text"
          size="small"
          onClick={() => handleScroll(-80)}
        >
          <RightOutlined style={{ fontSize: "12px" }} />
        </Button>
      )}
    </div>
  );
}

export default TagList;
