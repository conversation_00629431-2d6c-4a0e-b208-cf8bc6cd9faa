import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";
import { opacity } from "html2canvas/dist/types/css/property-descriptors/opacity";
import { title } from "process";
interface EchartsBarComponentProps {
  status?: string;
  powerData?: {
    name: string;
    data: {
      estimated_increased_rate: string;
      free_rate: string;
      usage_rate: string;
    };
  }[];
}
const EchartsBarComponent: React.FC<EchartsBarComponentProps> = (props) => {
  const { powerData, status="0" } = props;
  // 创建一个ref来获取DOM元素，用于Echarts实例的初始化
  const chartRef = useRef(null);

  useEffect(() => {

    const maxData =
      (powerData?.[0]?.data?.estimated_increased_rate
        ? parseFloat(powerData[0].data.estimated_increased_rate.slice(0, -1))
        : 0) +
      (powerData?.[0]?.data?.usage_rate
        ? parseFloat(powerData[0].data.usage_rate.slice(0, -1))
        : 0);
    const  maxDateUpdate  = maxData.toFixed(1);
    // console.log("maxDateUpdate", maxDateUpdate);
    // 基于获取到的DOM元素初始化Echarts实例
    const myChart = echarts.init(chartRef.current);

    // 配置项与原示例类似
    const option = {
      title: {
        label: {
          height: 0,
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
          show: false, // 禁止显示坐标轴指示器
          lineStyle: {
            opacity: 0,
          },
        },
        backgroundColor: "black", // 设置提示框背景色为黑色
        label: {
          color: "white", // 设置提示框文字颜色为白色
        },
        formatter: function (params: any) {
          if (Number(status) !== 7) {
            if (params.length === 3) {
              const baseText = `（${params[0].axisValue} ：${params[2].value}% -> ${params[1].value}%(估值））`;
              return baseText;
            }
            return "";
          } else {
            if (params.length === 2) {
              const baseText = `（${params[0].axisValue} ：${params[1].value}% `;
              return baseText;
            }
            return "";
          }
        },
      },

      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          data: [powerData && powerData[0]?.name + " GPU"],
          axisTick: { show: false }, // 隐藏x轴刻度线
          axisLine: { show: false }, // 隐藏x轴线
          axisLabel: { show: false }, // 隐藏x轴刻度标签
          splitLine: { show: false },
        },
      ],
      yAxis: [
        {
          type: "value",
          axisTick: { show: false }, // 隐藏y轴刻度线
          axisLine: { show: false }, // 隐藏y轴线
          axisLabel: { show: false }, // 隐藏y轴刻度标签
          splitLine: { show: false }, // 隐藏网格线
          axisPointer: {
            show: false, // 针对y轴，设置坐标轴指示器不显示
            lineStyle: {
              opacity: 0,
            },
          },
        },
      ],
      series: [
        {
          name: "完整量",
          type: "bar",
          stack: "total",
          areaStyle: {},
          emphasis: {
            // focus: "series",
          },
          data: [100],
          barWidth: "30%",
          barGap: "-100%",
          itemStyle: {
            color: "#fff",
            borderRadius: [5],
            borderColor: "rgba(104, 228, 180,1)",
            borderWidth: 5,
          },
        },
        status &&
          Number(status) !== 7 && {
            name: "最大量",
            type: "bar",
            stack: "完整量",
            areaStyle: {},
            emphasis: {
              // focus: "series",
            },
            data: [maxDateUpdate], // 截取字符串去掉%符号并转换为数字，如果没有数据则设置默认值为0
            barWidth: "30%",
            barGap: "-100%",
            itemStyle: {
              color: "rgb(133, 223, 210,0.5)",
              borderRadius: [5],
              // borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}%",

              color: "black",
              fontSize: 14,
            },
          },

        {
          name: "使用量",
          type: "bar",
          stack: "最大量",
          areaStyle: {},
          emphasis: {
            // focus: "series",
          },
          data: [
            powerData?.[0]?.data?.usage_rate.slice(0, 5)
              ? parseFloat(powerData[0].data.usage_rate.slice(0, -1))
              : 0,
          ], // 同样进行数据类型转换和默认值处理
          barWidth: "30%", // 设置柱子宽度为100%，使其占满整个分类区间，实现重叠效果
          barGap: "-100%", // 设置柱子间隔为-100%，让柱子完全重叠
          itemStyle: {
            color: "rgba(133, 223, 210,1)",
            borderRadius: [5],
            borderColor: "rgba(104, 228, 180,1)",
            borderWidth: 2,
          },
          label: {
            show: true,
            position: "top",
            formatter: "{c}%",

            color: "black",
            fontSize: 14,
          },
        },
      ],
    };

    // 设置配置项到Echarts实例中，绘制图表
    myChart.setOption(option);

    // 组件卸载时，销毁Echarts实例，释放资源
    return () => {
      myChart.dispose();
    };
  }, [powerData]);

  return <div style={{ width: "300px", height: "300px" }} ref={chartRef}></div>;
};

export default EchartsBarComponent;
