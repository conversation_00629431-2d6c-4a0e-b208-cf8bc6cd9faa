import React from 'react';
import { Tooltip } from 'antd';
import { SliderMarks } from 'antd/es/slider';
import { trainingSet } from '@/utils/conts';

interface CreateSliderMarksProps {
  infoArray: string[];
}

export const createSliderMarks = ({ infoArray }: CreateSliderMarksProps): SliderMarks => {
  const marks: SliderMarks = {};
  
  infoArray.forEach((info, index) => {
    const value = index * 25;
    marks[value] = (
      <>
        <div style={{ color: '#8E98A7', width: index === 4 ? '40px' : 'auto' }}>
          {info}
        </div>
        <Tooltip title={trainingSet[index]}>
          <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
        </Tooltip>
      </>
    );
  });
  
  return marks;
};
