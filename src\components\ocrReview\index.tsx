import React, { useEffect, useRef, useState } from 'react';
import { Row, Col, Space, Button, message } from 'antd';
import { LeftOutlined, DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { ReviewData, getReviewDataAPI, updateReviewStatusAPI, ReviewStatus, reviewDataAPI } from '../../api/ocr';
import { filterText, generateUniqueIds } from './tree';
import './index.module.css';
import TreeView from './components/TreeView';
import ResultView from './components/ResultView';
import TextView from './components/TextView';
import { DisplayOCRChapters, OCRBboxes, OCRChapters, OCRFilePath, OCRTable } from './components/ResultView/type';

const OcrReview: React.FC = () => {
  // const { file_id } = useParams<string>();
  const [search] = useSearchParams();
  const fileId = search.getAll('id')[0];
  const [chapters, setChapters] = useState<DisplayOCRChapters[]>([]);
  const [ocrBboxes, setOcrBboxes] = useState<OCRBboxes[][]>([]);
  const [ocrTables, setOcrTables] = useState<OCRTable[]>([]);
  const [ocrImages, setOcrImages] = useState<{ [key: string]: any }>({});
  const imgRef = useRef(null);
  const resultRef = useRef();

  useEffect(() => {
    // console.log(fileId); '6588a656b9bd6348e5f0da41625af7b4'
    const params = {
      file_id: fileId,
      review_status: ReviewStatus.InReview,
    };
    updateReviewStatusAPI(params).then((res) => {
      if (res.data?.code !== 200) {
        message.error(res.data?.message);
      }
    });
    getReviewDataAPI(fileId).then((res) => {
      if (res.data?.code === 200) {
        const valueStructure = res.data.data?.value_structure;
        if (!valueStructure) {
          message.error(res.data.message);
        }
        if (valueStructure?.chapters) {
          const displayChapters = generateUniqueIds(valueStructure?.chapters);
          console.log(displayChapters);
          setChapters(displayChapters);
          const filterResult = filterText(displayChapters);
        }
        if (valueStructure?.ocr_bboxes) {
          setOcrBboxes(valueStructure?.ocr_bboxes);
        }
        if (valueStructure?.images)
          setOcrImages(valueStructure?.images);
        if (valueStructure?.tables)
          setOcrTables(valueStructure?.tables);
      }
    });
  }, []);
  return (
    <div className={'review-container'}>
      {/* <Row className={'files-management-content'}>
        <Col span={11} className={'review-container-mid'}> */}
          <ResultView
            ref={resultRef}
            chapters={chapters}
            tables={ocrTables}
            images={ocrImages}
            onSelect={(ocrIndex: string[]) => {
              let bboxes: { [index: number]: number[][]; } = {};
              const bboxesList: Set<{ [index: number]: number[][]; }> = new Set<{ [index: number]: number[][]; }>();
              const existList: string[] = [];
              ocrIndex.forEach((indexStr) => {
                if (indexStr.indexOf('.') > -1 && !existList.includes(indexStr.slice(0, indexStr.lastIndexOf('.')))) {
                  const numberList = indexStr.split('.').map((item) => Number(item));
                  bboxes[numberList[0]] = ocrBboxes[numberList[0]][numberList[1]].bbox;
                  existList.push(`${numberList[0]}.${numberList[1]}`);
                  bboxesList.add(bboxes);
                  bboxes = {};
                }
              });
              if (imgRef.current) {
                console.log(bboxesList);
                (imgRef.current as any).onDraw(bboxesList);
              }
            }}
            onChange={(newChapters: DisplayOCRChapters[]) => {
              // 处理章节变化的逻辑
              console.log('Chapters changed:', newChapters);
            }}
            onImgSelect={(data: number[][], pageNum: number) => {
              const bboxesList: Set<{ [index: number]: number[][] }> = new Set<{ [index: number]: number[][] }>();
              const bboxes: { [index: number]: number[][] } = {};
              bboxes[pageNum] = data
              if (imgRef.current) {
                bboxesList.add(bboxes);
                (imgRef.current as any).onDraw(bboxesList);
              }
            }}
          ></ResultView>
        {/* </Col>
      </Row> */}
    </div>
  );
};

export default OcrReview;
