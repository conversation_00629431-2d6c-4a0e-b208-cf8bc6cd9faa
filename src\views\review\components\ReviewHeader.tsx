import React from 'react';
import { Button, <PERSON>, Typography, Tag, Modal } from 'antd';
import { ArrowLeftOutlined, ExclamationCircleFilled, LeftOutlined } from '@ant-design/icons';
import { TaskDetailType } from '../../../types';
import { useLocation, useNavigate } from 'react-router-dom';
import { getDeallocate } from '@/api/qa';

const { Title } = Typography;

interface ReviewHeaderProps {
  taskDetail: TaskDetailType;
  task_id: string;
}

const ReviewHeader: React.FC<ReviewHeaderProps> = ({ taskDetail, task_id }) => {
  const { confirm } = Modal;
  const navigate = useNavigate();
  const location = useLocation();

  const handleModalOk = () => {
    navigate(-1);
  };

  const getIsDeallocate = () => {
    const userId = sessionStorage.getItem('id');
    if (task_id && userId) {
      getDeallocate(task_id, userId!);
    }
  };
  return (
    <div
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: '24px',
      }}
    >
      <Space size={20}>
        <Button
          style={{ fontSize: '12px', width: '36px', height: '36px' }}
          shape="circle"
          icon={<LeftOutlined />}
          onClick={() => {
            const taskModal = confirm({
              centered: true,
              title: '保留提示',
              icon: <ExclamationCircleFilled />,
              width: 540,
              content: (
                <>
                  <div className="default-info" style={{ color: 'black' }}>
                    当前任务未全部审核完成，是否保留？
                  </div>
                </>
              ),
              footer: [
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    padding: '2rem 0 0 0',
                    gap: '8px',
                  }}
                >
                  <Button
                    type="text"
                    onClick={() => {
                      getIsDeallocate();
                      taskModal.destroy();
                      setTimeout(() => {
                        const navigateBack = location.state?.fromButton ? -2 : -1;
                        navigate(navigateBack);
                      }, 1000);
                    }}
                    shape="round"
                  >
                    否
                  </Button>
                  <Button
                    type="primary"
                    className="primary-btn"
                    style={{ width: '120px' }}
                    onClick={() => {
                      taskModal.destroy();
                      setTimeout(() => {
                        const navigateBack = location.state?.fromButton ? -2 : -1;
                        navigate(navigateBack);
                      }, 1000);
                    }}
                    shape="round"
                  >
                    是
                  </Button>
                </div>,
              ],
              onOk() {
                handleModalOk();
              },
              onCancel() {
                handleModalOk();
              },
            });
          }}
        />
        <div
          style={{
            fontSize: '28px',
            lineHeight: '36px',
            fontWeight: '500',
          }}
          className="mediumText"
        >
          {taskDetail.taskName}
        </div>
        <div className="review-tag boldText">人工审核</div>
      </Space>
    </div>
  );
};

export default ReviewHeader;
