import { useEffect, useState, RefObject } from 'react';

interface UseStepNavigationProps {
  sectionRefs: RefObject<HTMLDivElement>[];
  showResult: boolean;
  showTraining: boolean;
}

export const useStepNavigation = ({ sectionRefs, showResult, showTraining }: UseStepNavigationProps) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [scrollBasedStepIndex, setScrollBasedStepIndex] = useState(0);
  const [maxReachedStepIndex, setMaxReachedStepIndex] = useState(0);
  const [isSticky, setIsSticky] = useState(false);

  // 滚动监听和步骤切换逻辑
  useEffect(() => {
    const timer = setTimeout(() => {
      const observers: IntersectionObserver[] = [];
      const intersectingEntries = new Map<number, IntersectionObserverEntry>();

      const scrollElement = document.querySelector('.createTaskArea');

      if (scrollElement) {
        const observerOptions = {
          root: scrollElement,
          rootMargin: '-80px 0px -60% 0px',
          threshold: [0.1, 0.3, 0.5],
        };

        const updateCurrentStep = () => {
          if (intersectingEntries.size === 0) return;

          let maxRatio = 0;
          let mostVisibleIndex = 0;

          intersectingEntries.forEach((entry, index) => {
            if (entry.intersectionRatio > maxRatio) {
              maxRatio = entry.intersectionRatio;
              mostVisibleIndex = index;
            }
          });

          setScrollBasedStepIndex(mostVisibleIndex);
          setMaxReachedStepIndex((prevMax) => Math.max(prevMax, mostVisibleIndex));
        };

        sectionRefs.forEach((sectionRef, index) => {
          if (sectionRef.current) {
            const observer = new IntersectionObserver((entries) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting && entry.intersectionRatio >= 0.1) {
                  intersectingEntries.set(index, entry);
                } else {
                  intersectingEntries.delete(index);
                }
              });
              updateCurrentStep();
            }, observerOptions);
            observer.observe(sectionRef.current);
            observers.push(observer);
          }
        });
      }

      return () => {
        observers.forEach((observer) => observer.disconnect());
      };
    }, 500);

    return () => clearTimeout(timer);
  }, [sectionRefs]);

  // 吸顶状态检测
  useEffect(() => {
    const debounce = (func: Function, wait: number) => {
      let timeout: NodeJS.Timeout | null = null;
      return (...args: any[]) => {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => {
          func(...args);
        }, wait);
      };
    };

    const checkIfSticky = () => {
      if (showResult && !showTraining) {
        setIsSticky(false);
        return;
      }

      const scrollElement = document.querySelector('.ScrollbarsCustom-Scroller');
      if (scrollElement) {
        const scrollTop = scrollElement.scrollTop || 0;
        setIsSticky(scrollTop > 170);
      }
    };

    const debouncedCheckIfSticky = debounce(checkIfSticky, 5);

    const timer = setTimeout(() => {
      const scrollElement = document.querySelector('.ScrollbarsCustom-Scroller');
      if (scrollElement) {
        scrollElement.addEventListener('scroll', debouncedCheckIfSticky, { passive: true });
        checkIfSticky();
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      const scrollElement = document.querySelector('.ScrollbarsCustom-Scroller');
      if (scrollElement) {
        scrollElement.removeEventListener('scroll', debouncedCheckIfSticky);
      }
    };
  }, [showResult, showTraining]);

  const handleStepClick = (index: number) => {
    setCurrentStepIndex(index);
    const headerHeight = 50;
    setTimeout(() => {
      const targetSection = sectionRefs[index]?.current;
      if (targetSection) {
        const scrollElement = document.querySelector('.createTaskArea');
        if (scrollElement) {
          let targetPosition = targetSection.offsetTop - headerHeight;
          const maxScrollTop = scrollElement.scrollHeight - scrollElement.clientHeight;
          targetPosition = Math.min(targetPosition, maxScrollTop);
          targetPosition = Math.max(targetPosition, 0);

          scrollElement.scrollTo({
            top: targetPosition,
            behavior: 'smooth',
          });
        }
      }
    }, 0);
  };

  const scrollToStep = (stepIndex: number) => {
    setCurrentStepIndex(stepIndex);
    setScrollBasedStepIndex(stepIndex);

    const headerHeight = 50;
    const targetSection = sectionRefs[stepIndex]?.current;
    if (targetSection) {
      const scrollElement = document.querySelector('.createTaskArea');
      if (scrollElement) {
        let targetPosition = targetSection.offsetTop - headerHeight;
        const maxScrollTop = scrollElement.scrollHeight - scrollElement.clientHeight;
        targetPosition = Math.min(targetPosition, maxScrollTop);
        targetPosition = Math.max(targetPosition, 0);

        scrollElement.scrollTo({
          top: targetPosition,
          behavior: 'smooth',
        });
      }
    }
  };

  return {
    currentStepIndex,
    scrollBasedStepIndex,
    maxReachedStepIndex,
    isSticky,
    handleStepClick,
    scrollToStep,
    setIsSticky,
  };
};
