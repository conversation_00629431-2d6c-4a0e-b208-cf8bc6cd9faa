import {createStore,combineReducers,applyMiddleware} from 'redux'
// 导入权限处理器 如果是在./auth/reducer.js 
// export default reducer 这时auth的名称可以随意命名
import auth from './auth/reducer'

import user from './user/reducer'
// 导入异步中间件处理器
import thunk from 'redux-thunk'
// 导入日志中间件
import logger from 'redux-logger'
// 导入训练配置数据
import training from './training/reducer'
// 通过createStore创建仓库
const store = createStore(
  // 第一个参数需要一个reducer， combineReducers把多个reducer合并为一个
  combineReducers({auth, user, training}),
  // 添加中间件处理器
  applyMiddleware(thunk,logger as any)
)
// 导出默认store
export default store
// 返回一个RootState类型，类型的位 执行store.getState 的数据类型
// ReturnType获取到函数返回值类型
export type State = ReturnType <typeof store.getState>
export type Dispatch = typeof store.dispatch