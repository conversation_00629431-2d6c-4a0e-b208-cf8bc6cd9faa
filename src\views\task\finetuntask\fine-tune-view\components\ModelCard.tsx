import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Tooltip, message } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ModelSetType } from '@/types';
import { cannelTrain } from '@/api/modle';

interface ModelCardProps {
  item: ModelSetType;
  showRetrain: boolean;
  showInterrupt: boolean;
  onShowRetrainChange: (show: boolean) => void;
  onShowInterruptChange: (show: boolean) => void;
  onModelClick: (id: string) => void;
  onModelDelete: (id: string) => void;
  onRetrainClick: (id: string) => void;
  onInterruptClick: (id: string) => void;
  onNavigateToConfig: (id: string, name: string, status: number) => void;
  onNavigateToDetail: (id: string, name: string) => void;
  onNavigateToAdjustment: (id: string) => void;
  getModelName: (id: string) => string | undefined;
  getModelConfig: (id: string) => Promise<any>;
}

const ModelCard: React.FC<ModelCardProps> = ({
  item,
  showRetrain,
  showInterrupt,
  onShowRetrainChange,
  onShowInterruptChange,
  onModelClick,
  onModelDelete,
  onRetrainClick,
  onInterruptClick,
  onNavigateToConfig,
  onNavigateToDetail,
  onNavigateToAdjustment,
  getModelName,
  getModelConfig,
}) => {
  const navigate = useNavigate();

  const modelStatusChange = (status: number) => {
    if (status === 7) {
      return '在线';
    } else if (status === 6) {
      return '上线中';
    } else if (status === 5) {
      return '离线';
    } else if (status === 4) {
      return '停止训练';
    } else if (status === 2) {
      return '训练中';
    } else if (status === 3) {
      return '训练失败';
    } else if (status === 8) {
      return '上线失败';
    } else {
      return '';
    }
  };

  if (showRetrain && (item.status === 3 || item.status === 0 || item.status === 4)) {
    return (
      <Col key={item.id} span={6}>
        <Card
          className="interrupted"
          bordered={false}
          headStyle={{ borderBottom: 'none' }}
          style={{
            wordWrap: 'break-word',
            height: '185px',
            width: '320px',
            color: '#FFFFFF',
          }}
          title={
            <span>
              <ExclamationCircleFilled style={{ color: 'red', fontSize: '16px' }} /> 提示
            </span>
          }
        >
          <div className="text">确定要重新训练吗？</div>
          <div className="btn">
            <Button onClick={() => onShowRetrainChange(false)}>取消</Button>
            <Button
              onClick={() => {
                onRetrainClick(item.id);
                onShowRetrainChange(false);
              }}
            >
              确定
            </Button>
          </div>
        </Card>
      </Col>
    );
  }

  if (showInterrupt && (item.status === 2 || item.status === 0)) {
    return (
      <Col key={item.id} span={6}>
        <Card
          className="interrupted"
          bordered={false}
          headStyle={{ borderBottom: 'none' }}
          style={{
            wordWrap: 'break-word',
            height: '170px',
            width: '230px',
            color: '#FFFFFF',
          }}
          title={
            <span>
              <ExclamationCircleFilled style={{ color: 'red', fontSize: '16px' }} /> 提示
            </span>
          }
        >
          <div className="text">确定要停止训练吗？</div>
          <div className="btn">
            <Button onClick={() => onShowInterruptChange(false)}>取消</Button>
            <Button
              onClick={() => {
                cannelTrain(Number(item.id));
                onShowInterruptChange(false);
                message.success('模型训练正在停止...');
              }}
            >
              确定
            </Button>
          </div>
        </Card>
      </Col>
    );
  }

  return (
    <Col key={item.id} span={6}>
      <Card
        className={
          item.status === 7
            ? 'islive'
            : item.status === 6
              ? 'goingLive'
              : item.status === 5
                ? 'done'
                : item.status === 4
                  ? 'training'
                  : item.status === 2
                    ? 'training'
                    : 'interrupted'
        }
        bordered={false}
        headStyle={{ borderBottom: 'none' }}
        style={{
          wordWrap: 'break-word',
          height: '185px',
          width: '320px',
          color: '#FFFFFF',
        }}
      >
        <Tooltip title={item.modelName}>
          <div className={'modelNameTitle'}>{item.modelName}</div>
        </Tooltip>
        <Tooltip title={item.introduction}>
          <div className={'introduction'}>{item.introduction}</div>
        </Tooltip>

        <div className={'statusInfo'}>
          <div className={item.status === 5 ? 'modelStatus' : 'otherStatus'}>
            <span
              className={
                item.status === 7
                  ? 'liveround'
                  : item.status === 6
                    ? 'goliveround'
                    : item.status === 5
                      ? 'interruptedround'
                      : item.status === 2
                        ? 'traninground'
                        : 'doneround'
              }
            />
            {modelStatusChange(item.status)}
          </div>
          <div className={'actionInfo'}>
            {item.status === 7 ? (
              <div className="prolong">
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => onNavigateToAdjustment(item.id)}
                >
                  调试 |
                </Button>
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => {
                    const name = getModelName(item.id);
                    onNavigateToConfig(item.id, name || '', item.status);
                  }}
                >
                  配置|
                </Button>
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => {
                    const name = getModelName(item.id);
                    onNavigateToDetail(item.id, name || '');
                  }}
                >
                  详情
                </Button>
              </div>
            ) : item.status === 5 || item.status === 8 ? (
              <div className="donetext">
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => {
                    const name = getModelName(item.id);
                    onNavigateToConfig(item.id, name || '', item.status);
                  }}
                >
                  配置|
                </Button>
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => {
                    const name = getModelName(item.id);
                    onNavigateToDetail(item.id, name || '');
                  }}
                >
                  详情
                </Button>
              </div>
            ) : item.status === 3 || item.status === 4 ? (
              <div className="Interrupted">
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => {
                    onShowRetrainChange(true);
                  }}
                >
                  重新训练
                </Button>
                <Button type="text" className={'btdStyle'} onClick={() => onModelDelete(item.id)}>
                  |删除
                </Button>
              </div>
            ) : item.status === 2 ? (
              <div className="training1">
                <Button
                  type="text"
                  className={'btdStyle'}
                  onClick={() => {
                    onShowInterruptChange(true);
                  }}
                >
                  停止训练
                </Button>
              </div>
            ) : null}
          </div>
        </div>
      </Card>
    </Col>
  );
};

export default ModelCard;
