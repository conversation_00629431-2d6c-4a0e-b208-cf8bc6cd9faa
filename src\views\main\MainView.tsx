import { Layout, Space } from 'antd';
import { Outlet } from 'react-router-dom';
import PageHeader from '../../components/PageHeader/PageHeader';
import '../../css/PageHeader.css';

const { Content } = Layout;
const contentStyle: React.CSSProperties = {
  textAlign: 'center',
  minHeight: 'calc(100vh - 52px)',
  color: '#000',
  backgroundColor: '#fff',
};
const MainView: React.FC = () => {
  return (
    <Space direction="vertical" style={{ width: '100%' }} size={[0, 48]}>
      <Layout className="pageLayout">
        <PageHeader></PageHeader>
        <Content style={contentStyle}>
          <div className="main-content">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Space>
  );
};

export default MainView;
