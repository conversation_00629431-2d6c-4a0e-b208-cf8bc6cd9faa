import { Slider, Tooltip } from "antd";
import { SliderMarks } from "antd/es/slider";
import {
  maxInfo,
  maxlenght,
  repeatition,
  repeatitionInfo,
  split,
  temperature,
  topk,
  topkInfo,
  topp,
  toppInfo,
} from "../../../../../utils/conts";
export const temperatureMarks: SliderMarks = {
  0: (
    <>
      <div style={{ color: "#8E98A7" }}>{split[0]}</div>
      <Tooltip title={temperature[0]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  25: (
    <>
      <div style={{ color: "#8E98A7" }}>{split[1]}</div>
      <Tooltip title={temperature[1]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  50: (
    <>
      <div style={{ color: "#8E98A7" }}>{split[2]}</div>
      <Tooltip title={temperature[2]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  75: (
    <>
      <div style={{ color: "#8E98A7" }}>{split[3]}</div>
      <Tooltip title={temperature[3]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  100: (
    <>
      <div style={{ color: "#8E98A7" }}>{split[4]}</div>
      <Tooltip title={temperature[4]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
};
export const topkMarks: SliderMarks = {
  0: (
    <>
      <div style={{ color: "#8E98A7" }}>{topkInfo[0]}</div>
      <Tooltip title={topk[0]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  25: (
    <>
      <div style={{ color: "#8E98A7" }}>{topkInfo[1]}</div>
      <Tooltip title={topk[1]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  50: (
    <>
      <div style={{ color: "#8E98A7" }}>{topkInfo[2]}</div>
      <Tooltip title={topk[2]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  75: (
    <>
      <div style={{ color: "#8E98A7" }}>{topkInfo[3]}</div>
      <Tooltip title={topk[3]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  100: (
    <>
      <div style={{ color: "#8E98A7" }}>{topkInfo[4]}</div>
      <Tooltip title={topk[4]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
};
export const toppMarks: SliderMarks = {
  0: (
    <>
      <div style={{ color: "#8E98A7" }}>{toppInfo[0]}</div>
      <Tooltip title={topk[0]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  25: (
    <>
      <div style={{ color: "#8E98A7" }}>{toppInfo[1]}</div>
      <Tooltip title={topk[1]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  50: (
    <>
      <div style={{ color: "#8E98A7" }}>{toppInfo[2]}</div>
      <Tooltip title={topk[2]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  75: (
    <>
      <div style={{ color: "#8E98A7" }}>{toppInfo[3]}</div>
      <Tooltip title={topk[3]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  100: (
    <>
      <div style={{ color: "#8E98A7" }}>{toppInfo[4]}</div>
      <Tooltip title={topk[4]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
};
export const maxLenMarks: SliderMarks = {
  0: (
    <>
      <div style={{ color: "#8E98A7" }}>{maxInfo[0]}</div>
      <Tooltip title={maxlenght[0]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  25: (
    <>
      <div style={{ color: "#8E98A7" }}>{maxInfo[1]}</div>
      <Tooltip title={maxlenght[1]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  50: (
    <>
      <div style={{ color: "#8E98A7" }}>{maxInfo[2]}</div>
      <Tooltip title={maxlenght[2]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  75: (
    <>
      <div style={{ color: "#8E98A7" }}>{maxInfo[3]}</div>
      <Tooltip title={maxlenght[3]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  100: (
    <>
      <div style={{ color: "#8E98A7" }}>{maxInfo[4]}</div>
      <Tooltip title={maxlenght[4]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
};
export const repeatitionMark: SliderMarks = {
  0: (
    <>
      <div style={{ color: "#8E98A7" }}>{repeatitionInfo[0]}</div>
      <Tooltip title={maxlenght[0]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  25: (
    <>
      <div style={{ color: "#8E98A7" }}>{repeatitionInfo[1]}</div>
      <Tooltip title={maxlenght[1]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  50: (
    <>
      <div style={{ color: "#8E98A7" }}>{repeatitionInfo[2]}</div>
      <Tooltip title={maxlenght[2]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  75: (
    <>
      <div style={{ color: "#8E98A7" }}>{repeatitionInfo[3]}</div>
      <Tooltip title={maxlenght[3]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
  100: (
    <>
      <div style={{ color: "#8E98A7" }}>{repeatitionInfo[4]}</div>
      <Tooltip title={maxlenght[4]}>
        <div
          style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
        ></div>
      </Tooltip>
    </>
  ),
};

export const creativeSliders : SliderProps[]= [
  {
    key: 'splitLevel',
    label: '温度',
    tooltipTitle: '影响输出的随机性和创新性。较高的温度值促进创造性。',
    SiderMark:  temperatureMarks,
    formatter: (val) =>val? temperature[Math.floor(val / 25)]:temperature[0],
  },
  {
    key: 'topkValue',
    label: 'Top-k采样',
    tooltipTitle: '控制候选词的范围，影响文本的多样性和新颖性。',
    SiderMark: topkMarks,
    formatter: (val) => val?topk[Math.floor(val / 25)]:topk[0],
  },
  {
    key: 'toppValue',
    label: 'Top-p采样',
    tooltipTitle: '通过动态调整候选词池的大小，影响文本的创新性和多样性。',
    SiderMark: toppMarks,
    formatter: (val) => val? topp[Math.floor(val / 25)]:topp[0],
  }
];

export const correlationSliders : SliderProps[]= [
  {
    key: 'maxLen',
    label: '最大长度',
    tooltipTitle: '虽然不直接影响相关性，但适当的长度可以确保回答不偏离主题。',
    SiderMark: maxLenMarks,
    formatter: (val) => val? maxlenght[Math.floor(val / 25)]:maxlenght[0],
  },
  {
    key: 'repeatValue',
    label: '重复率惩罚',
    tooltipTitle: '通过减少重复，帮助模型保持话题的聚焦和相关性。',
    SiderMark: repeatitionMark,
    formatter: (val) =>val? repeatition[Math.floor(val / 25)]:repeatition[0],
  }
];
export const OPTIONS_ARRAY :string[] =["自动配置","手动配置"]

export const generateSegmentedOptions = (
  currentConfig: string|number,
  setConfig: React.Dispatch<React.SetStateAction<string|number>>
) => {
  return OPTIONS_ARRAY.map((option) => ({
    label: (
      <Tooltip title={option}>
        <a
          onClick={() => setConfig(option)}
          className={
            currentConfig === option
              ? "model-config-active"
              : "model-config"
          }
        >
          {option}
        </a>
      </Tooltip>
    ),
    value: option,
  }));
};
