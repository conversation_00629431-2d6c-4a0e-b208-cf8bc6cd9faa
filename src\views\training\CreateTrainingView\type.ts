import { DataSetType, ModelDetailType, TaskType, trainingConfigType } from "../../../types";

export interface TariningType {
    dataset: string;
    modelName: string;
    modelIntro: string;
    testSet: string;
    testSetConfig: string;
    tasks: TaskType[];
    trainingConfig: trainingConfigType[];
    testSetRatio: number;
    modelId: number;
    srategy: string; //训练策略
    iterationLevel: string; //迭代轮次
    learningValue: string; //学习率
    batchValue: string; //Top-p 采样
    trainSetConfig: string; // 参数配置：自动配置/手动配置
    category: number; // 模型类型
    framework:string;//训练框架
    serverSelection:string;//服务器选择
    arithmeticConfiguration:string[];//算力配置
    serverConfigId:number;
  
}
export interface ModelSelectType {
    dataset: string;
    name: string;
    modelintro: string;
    modelType: number;
}
export interface TrainSet {
    splitLevel?: string, 
    topkValue?: string, 
    toppValue?: string, 
    maxLen?: string, 
    repeatValue?: string
}
export interface ServerDataType {
    label: string;
    value: string;
    id: number;
  };
  export interface ModelFailData {
    properties: {
        modelId: string;
        category: number;
        modelName: string;
        trainConfig: {
            id: string[];
            datasets: null | any;
            batchSize: number;
            learnRate: number;
            properties: null | any;
            baseModelId: number;
            datasetRadio: number;
            trainStrategy: string;
            interationNumber: number;
        };
        introduction: string;
        basicConfigRequest: {
            server: string;
            serverConfigId: number;
            computeResource: string[];
            trainingFramework: string;
        };
    };
  }
  