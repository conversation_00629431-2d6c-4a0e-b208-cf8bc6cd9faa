.review-title-lf {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 22px;
}

.review-title-lf {
  color: #6D6D6D;
  font-family: HarmonyOS Sans SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.result-container {
  width: 100%;
  display: inline-flex;
  gap: 8px;
}

.result-container .result-title {
  color: #000;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  padding: 8px 21px;
}

.result-container .result-text,
.result-container .result-img-text {
  color: #000;
  text-align: justify;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
  padding: 8px 21px;
}

.result-container .result-img-text {
  text-align: center;
  width: 100%;
}

.result-container Button {
  visibility: hidden;
}



.result-container:hover But<PERSON> {
  visibility: inherit;
}

.result-container:hover .result-text,
.result-container:hover .result-title {
  border-radius: 10px;
  border: 1px solid #EEF1F5;
}


.review-edit-textarea {
  resize: none;
  color: #000;
  text-align: justify;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
  padding-left: 38px;
  border-radius: 10px;
  border: 1px solid #EEF1F5;
  background: #FFF;
  padding: 8px 21px;
  width: 100%;
}

.img-item {
  max-width: 50%;
  position: relative;
  left: 50%;
  transform: translate(-50%);
}