import {
  <PERSON><PERSON>,
  Card,
  Col,
  Descriptions,
  DescriptionsProps,
  Divider,
  Dropdown,
  Empty,
  Flex,
  Input,
  MenuProps,
  Modal,
  Progress,
  Row,
  Select,
  Slider,
  Space,
  Spin,
  Table,
  Tooltip,
  message,
} from "antd";
import { Children, useEffect, useRef, useState } from "react";
import "../../../css/FineTuneView.css";
import "../../../css/SourceDatasetView.css";
import { Scrollbar } from "react-scrollbars-custom";
import {
  CaretDownOutlined,
  SearchOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons";
import infoIcon from "../../../assets/img/info-icon.svg";
import logo from "../../../assets/img/logo-x.png";
import warningIcon from "../../../assets/img/warningIcon.svg";
import OpenDraw from "../../../components/OpenDraw";
import TimeModal from "../../../components/TimeModal";
import { useNavigate } from "react-router-dom";
import { ModelSetType, ModelDetailType } from "../../../types";
import {
  GetModlesParams,
  cannelTrain,
  getModelDetail,
  getModelSets,
  getMyModelDetail,
  modelOffline,
  modelOnline,
  startTrain,
  onlineModel,
  getModelConfig,
  getModelConfigByFailId,
  deleteModel
} from "../../../api/modle";
import Ribbon from "antd/es/badge/Ribbon";
import { number } from "echarts";
import { status } from "nprogress";
import { useDispatch } from "react-redux";
import { ThunkDispatch } from "redux-thunk";
import { AnyAction } from "redux";
const FineTuneView: React.FC = () => {
  const [openDrawView, setOpenDrawView] = useState(false);
  const [myopenDrawView, setMyOpenDrawView] = useState(false);
  const [interruptDrawView, setInterruptDrawView] = useState(false);
  const [online, setOnline] = useState(false);
  const [prolong, setProlong] = useState(false);
  const [offmodel, setOffmodel] = useState(false);
  const [cancelTrain, setCancelTrain] = useState(false);
  const [titltext, setTitltext] = useState("");
  const [modelId, setModelId] = useState("");
  const [trainTaskId, setTrainTaskId] = useState("");
  const [mytitltext, setMytitltext] = useState("");
  const [verifyUsers, setVerifyUser] = useState<
    { label: string; key: string; value: string }[]
  >([]);
  const [reviewOptions, setReviewOptions] = useState<
    { label: string; key: string; value: string }[]
  >([]);
  const navigate = useNavigate();
  const [modelDatasets, setModelDatasets] = useState<ModelSetType[]>([]);
  const [myModelDatasets, setMyModelDatasets] = useState<ModelSetType[]>([]);
  const [modelDetail, setModelDetail] = useState<any>(
    new Object() as ModelDetailType
  );
  const [myModelDetail, setMyModelDetail] = useState<ModelDetailType>(
    new Object() as ModelDetailType
  );
  const [tarinDetial, setTrainDetial] = useState<any>(
    new Object() as ModelDetailType
  );
  // 定义分页数据
  const [pagination, setPagination] = useState({
    page: 1,
    size: 20,
    total: 0,
  });
  const [sortAttribute, setSortAttribute] = useState("modelName");
  const [sortDirection, setSortDirection] = useState("");
  const [sortMyModel, setSortMyModel] = useState("");
  const [apiFlg, setApiFlg] = useState(false);
  const [filterInput, setFilterInput] = useState("");
  const [filterMyInput, setFilterMyInput] = useState("");
  const [selectModelDate, setSelectModelDate] = useState("BASE_MODEL");
  const [selectMyDataSet, setSelectMyDataSet] = useState("FINE_TUNING");
  //const [modelStatus, setModelStatus] = useState("");
  const [myModelStatus, setMyModelStatus] = useState("");
  const [tableIndex, setTableIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timer>();
  const [timeValue, setTimeValue] = useState<any>(1);
  const [modelStatus, setModelStatus] = useState(1);
  const [modalTitle, setModalTitle] = useState("");
  const [lightWeight, setLightWeight] = useState(false);
  const [isLightWeight, setIsLightWeight] = useState(false);
  const [lightWeightState, setLightWeightState] = useState({});
  const [currentItemId, setCurrentItemId] = useState<number>(0);
  // 是否重新训练
  const [showRetrain, setShowRetrain] = useState<{ [key: string]: boolean }>(
    {}
  );
// 是否停止训练
  const [showInterrupt, setShowInterrupt] = useState<{ [key: string]: boolean }>(
    {}
  );
  const buttonText = "微调训练";
  const moduletext = modelDetail.modelName;
  const tooltip = (
    <span style={{ lineHeight: "25px" }}>
      模型微调流程:
      <br />
      1.选定合适基座模型
      <br />
      2.调整训练配置
      <br />
      3.执行训练任务
      <br />
      4.模型部署应用
    </span>
  );
  const handlepedestalMenuClick: MenuProps["onClick"] = (e) => {
    if (e.key === "2-1") {
      setSortDirection("asc");
    } else if (e.key === "2-2") {
      setSortDirection("desc");
    }
  };
  const handleMyModelMenuClick: MenuProps["onClick"] = (e) => {
    if (e.key === "1-1") {
      setMyModelStatus("");
      setSortMyModel("asc");
    } else if (e.key === "1-2") {
      setMyModelStatus("");
      setSortMyModel("desc");
    }
    if (e.key === "2-1") {
      setMyModelStatus("7");
    } else if (e.key === "2-2") {
      setMyModelStatus("5");
    } else if (e.key === "2-3") {
      setMyModelStatus("2");
    } else if (e.key === "2-4") {
      setMyModelStatus("3");
    }
  };
  const handleLightWeight = (id: any) => {
    setCurrentItemId(id);
    setLightWeight(true);
  };

  const confirmLightWeight = (id: any) => {
    setLightWeightState((prevState) => ({
      ...prevState,
      [id]: true, // 标记该 id 为轻量化
    }));
  };
  const onClickDetail = (id: string) => {
    const selectedModel = data.find((item) => item.id === id);
    const modelName = selectedModel ? selectedModel.modelName : "";
    const modelId = selectedModel ? selectedModel.id : "";
    setTitltext(modelName);
    setModelId(modelId);
  };

  const onClickMyDetail = (id: string) => {
    const selectedModel = mydata.find((item) => item.id === id);
    const modelName = selectedModel ? selectedModel.modelName : "";
    const modelId = selectedModel ? selectedModel.id : "";
    setMytitltext(modelName);
    setModelId(modelId);
  };

  function getModels() {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetModlesParams = {
        ...pagination,
        category: 0,
        sortAttribute,
        sortDirection: sortDirection,
        modelName: sortAttribute === "modelName" ? filterInput : "",
        status: "",
        // modelCategory: selectModelDate === "BASE_MODEL" ? "BASE_MODEL" : "FINE_TUNING"
      };
      getModelSets(params).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          setModelDatasets(res.data?.data);
          setPagination({ ...pagination, total: res.data?.totalCount });
          setTableIndex((pagination.page - 1) * pagination.size);
        }
      });
    }
  }

  function getMyModels() {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetModlesParams = {
        ...pagination,
        category: 1,
        sortAttribute,
        sortDirection: sortMyModel,
        modelName: sortAttribute === "modelName" ? filterMyInput : "",
        status: myModelStatus,
        // modelCategory: selectMyDataSet
      };
      getModelSets(params).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          // const data = res.data?.data.filter(item => item.status !== 0);
          const data = res.data?.data;
          setMyModelDatasets(data);
          // setMyModelDatasets(res.data?.data)
          setPagination({ ...pagination, total: res.data?.totalCount });
          setTableIndex((pagination.page - 1) * pagination.size);
          const foundModel: any = res.data?.data.some(
            (model: any) => model.status === 2
          );
          if (foundModel) {
            localStorage.setItem("isOnline", "1");
          } else {
            localStorage.setItem("isOnline", "0");
          }
        }
      });
    }
  }

  const getModelsDetail = (modelId: string) => {
    getModelDetail(modelId).then((res) => {
      if (res.data?.code === 200) {
        const modelInfo = res.data?.data.modelInfo;
        const data = res.data?.data.trainDetail;
        setModelDetail(modelInfo);
        setTrainDetial(data);
      }
    });
  };
  const getMyModelsDetail = (modelId: string) => {
    getMyModelDetail(modelId).then((res) => {
      if (res.data?.code === 200) {
        const data = res.data?.data;
        setMyModelDetail(data);
      }
    });
  };
  // 重新训练
  const useReTrain = (id: string) => {
    const dispatch: ThunkDispatch<{}, {}, AnyAction> = useDispatch();
    const reTrain = (id: string) => {
        getModelConfigByFailId(id).then((res: any) => {
            if (res.data?.code === 200) {
                const data = res.data?.data;
                navigate("/main/finetune/create", 
           
              );
                dispatch({
                    type: "SET_TRAINING_DATA",
                    payload: {
                        modelId: id,
                        modelConfigData: data
                    }
                });
            }
        });
    };
    return reTrain;
};
  // 请求数据
  useEffect(() => {
    getModels();
    getMyModels();
  }, []);

  useEffect(() => {
    clearInterval(intervalRef.current as NodeJS.Timeout);
    getModels();
    intervalRef.current = setInterval(getModels, 5000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout); // 在组件卸载时清除定时器
    };
  }, [
    pagination.page,
    pagination.size,
    sortDirection,
    sortAttribute,
    filterInput,
  ]);

  useEffect(() => {
    clearInterval(intervalRef.current as NodeJS.Timeout);
    getMyModels();
    intervalRef.current = setInterval(getMyModels, 5000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout); // 在组件卸载时清除定时器
    };
  }, [
    pagination.page,
    pagination.size,
    sortMyModel,
    sortAttribute,
    myModelStatus,
    filterMyInput,
  ]);
  const data = modelDatasets;
  const mydata = myModelDatasets;
  const modelStatusChange = (status: number) => {
    // if (status === 1) {
    //   return "未训练";
    // } else if (status === 2) {
    //   return "训练中";
    // } else if (status === 4) {
    //   return "已中断";
    // } else if (status === 5) {
    //   return "已完成";
    // } else if (status === 6) {
    //   return "上线中";
    // } else if (status === 7) {
    //   return "已上线";
    // } else if (status === 3) {
    //   return "失败了";
    // } else if (status === 0) {
    //   return "未知";
    // }
    if (status === 7) {
      return "在线";
    } else if (status === 6) {
      return "上线中";
    } else if (status === 5) {
      return "离线";
    } else if (status === 2) {
      return "训练中";
    } else if (status === 3) {
      return "训练失败";
    }else{
      return "训练失败";
    }
  };
  const pedestalDropdownItems: MenuProps["items"] = [
    {
      key: "verifyUser",
      label: "按模型名称",
      children: [
        {
          key: "2-1",
          label: "正序",
        },
        {
          key: "2-2",
          label: "倒序",
        },
      ],
    },
  ];
  const myModelDropdownItems: MenuProps["items"] = [
    {
      key: "moduleName",
      label: "按模型名称",
      children: [
        {
          key: "1-1",
          label: "正序",
        },
        {
          key: "1-2",
          label: "倒序",
        },
      ],
    },
    {
      key: "trainingStatus",
      label: "按训练状态",
      children: [
        {
          key: "2-1",
          label: "在线",
        },
        {
          key: "2-2",
          label: "离线",
        },
        {
          key: "2-3",
          label: "训练中",
        },
        {
          key: "2-4",
          label: "训练失败",
        },
      ],
    },
  ];
  const getModelName = (id: string) => {
    const selectedModel = data.find((item) => item.id === id);
    const selectedBaseMpdel = mydata.find((item) => item.id === id);
    if (selectedModel) {
      return selectedModel ? selectedModel.modelName : "";
    } else if (selectedBaseMpdel) {
      return selectedBaseMpdel ? selectedBaseMpdel.modelName : "";
    }
  };
  const learnRatio = (num: number) => {
    if (num === 0.00001) {
      return "1e-5";
    } else if (num === 0.00005) {
      return "5e-5";
    } else if (num === 0.0001) {
      return "1e-4";
    } else if (num === 0.0005) {
      return "5e-4";
    } else if (num === 0.001) {
      return "1e-3";
    }
  };

  const descItems: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "模型ID",
      children: modelDetail.id,
    },
    {
      key: "2",
      label: "模型类型",
      children: modelDetail.category === 0 ? "基座模型" : "微调模型",
    },
    {
      key: "3",
      label: "参数量",
      children: modelDetail.parameter,
    },
    {
      key: "4",
      label: "模型介绍",
      children: modelDetail.introduction,
    },
    {
      key: "5",
      label: "适用场景推荐",
      children: modelDetail.scene,
    },
    // {
    //   key: "6",
    //   label: "微调训练提示",
    //   children: modelDetail.
    // },
  ];
  const trainItems: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "模型ID",
      children: modelDetail.id,
    },
    {
      key: "2",
      label: "模型介绍",
      children: modelDetail.introduction,
    },
    {
      key: "3",
      label: "数据集",
      children: tarinDetial?.datasets || "-",
    },
    {
      key: "4",
      label: "数据集比例",
      children: tarinDetial?.datasetRadio
        ? tarinDetial?.datasetRadio * 100 + "%"
        : "-",
    },
    {
      key: "5",
      label: "基座模型",
      children: getModelName(tarinDetial?.modelBaseId) || "-",
    },
    {
      key: "6",
      label: "训练策略",
      children: tarinDetial?.trainStrategy || "-",
    },
    {
      key: "7",
      label: "迭代次数",
      children: tarinDetial?.interationNumber || "-",
    },
    {
      key: "8",
      label: "批次大小",
      children: tarinDetial?.batchSize || "-",
    },
    {
      key: "9",
      label: "学习率",
      children: learnRatio(tarinDetial?.learnRate) || "-",
    },
    {
      key: "10",
      label: (
        <span style={{ color: "#000000", fontWeight: "bold" }}>模型状态</span>
      ),
      children: modelStatusChange(tarinDetial?.status) || "-",
    },
    {
      key: "11",
      label: (
        <span style={{ color: "#000000", fontWeight: "bold" }}>训练进度</span>
      ),
      children: tarinDetial?.trainProgress ? (
        <>
          <Progress percent={Number(tarinDetial?.trainProgress)} type="line" />
        </>
      ) : (
        "-"
      ),
    },
  ];
  const interruptItems: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "模型ID",
      children: modelDetail.id,
    },
    {
      key: "2",
      label: "模型介绍",
      children: modelDetail.introduction || "-",
    },
    {
      key: "3",
      label: "数据集",
      children: tarinDetial?.datasets || "-",
    },
    {
      key: "4",
      label: "数据集比例",
      children: tarinDetial?.datasetRadio
        ? tarinDetial?.datasetRadio * 100 + "%"
        : "-",
    },
    {
      key: "5",
      label: "基座模型",
      children: getModelName(tarinDetial?.modelBaseId) || "-",
    },
    {
      key: "6",
      label: "训练策略",
      children: tarinDetial?.trainStrategy || "-",
    },
    {
      key: "7",
      label: "迭代次数",
      children: tarinDetial?.interationNumber || "-",
    },
    {
      key: "8",
      label: "批次大小",
      children: tarinDetial?.batchSize || "-",
    },
    {
      key: "9",
      label: "学习率",
      children: learnRatio(tarinDetial?.learnRate) || "-",
    },
    {
      key: "10",
      label: (
        <span style={{ color: "#000000", fontWeight: "bold" }}>模型状态</span>
      ),
      children: modelStatusChange(tarinDetial?.status) || "-",
    },
    {
      key: "11",
      label: (
        <span style={{ color: "#000000", fontWeight: "bold" }}>中断原因</span>
      ),
      children: tarinDetial?.stopReason || "-",
    },
    {
      key: "12",
      label: (
        <span style={{ color: "#000000", fontWeight: "bold" }}>训练进度</span>
      ),
      children: tarinDetial?.trainProgress ? (
        <>
          <Progress percent={Number(tarinDetial?.trainProgress)} type="line" />
        </>
      ) : (
        "-"
      ),
    },
  ];

  const DescriptionsWrapper = ({ children }: { children: any }) => {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <div className="descriptions">
          <Descriptions
            column={1}
            style={{
              padding: "1rem 0rem 2rem 0.6rem",
            }}
            size="small"
            items={children}
          />
        </div>
        <div
          style={{ position: "absolute", bottom: "46px", right: "25%" }}
        ></div>
      </div>
    );
  };
  const handleButtonClick = (value: number) => {
    // 父组件的点击按钮处理逻辑
    onlineModel(modelId);
    setTimeValue(1); // 重置为1
    setOnline(false); // 关闭模态框
  };
 // 调用useReTrain获取reTrain函数
 const reTrainById = useReTrain('');
  return (
    <Scrollbar>
      <div className="createModelContent">
        <div
          className="mediumText"
          style={{
            fontSize: "28px",
            lineHeight: "36px",
            fontWeight: "500",
            marginLeft: "2rem",
            justifyContent: "space-between",
            display: "flex",
          }}
        >
          <div> 模型仓库 </div>
          <div>
            <Button
              type="primary"
              size="large"
              shape="round"
              style={{
                backgroundColor: "black",
                fontSize: "14px",
                fontWeight: "700",
                margin: "0 1rem 0 0",
              }}
              className="boldText"
              onClick={() => {
                navigate("/main/finetune/uploadBaseModel");
              }}
            >
              上传基座
            </Button>
            <Button
              type="primary"
              size="large"
              shape="round"
              style={{
                backgroundColor: "black",
                fontSize: "14px",
                fontWeight: "700",
                margin: "0 1rem 0 0",
              }}
              className="boldText"
              onClick={() => {
                navigate("/main/finetune/create");
              }}
            >
              微调训练
            </Button>
            <Tooltip title={tooltip}>
              <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
            </Tooltip>
          </div>
        </div>

        <div className="pedestal-info">
          <div
            style={{
              width: "100%",
              textAlign: "start",
              justifyContent: "space-between",
              display: "inline-flex",
              marginBottom: "1.5rem",
              alignItems: "center",
            }}
          >
            <Space size="small">
              <div
                style={{
                  margin: "0 1rem 0 0",
                  width: "64px",
                  height: "19px",
                  fontWeight: "500",
                  fontSize: "16px",
                }}
              >
                基座模型
              </div>
              <Space.Compact>
                <Input
                  size="large"
                  className="filter-input"
                  suffix={<SearchOutlined />}
                  value={filterInput}
                  onChange={(e) => {
                    setFilterInput(e.target.value);
                  }}
                  placeholder={`请输入基座模型名称`}
                />
              </Space.Compact>

              <Dropdown
                menu={{
                  items: pedestalDropdownItems,
                  onClick: handlepedestalMenuClick,
                }}
              >
                <Button
                  className="default-btn"
                  // style={{ width: "124px", height: "40px", fontSize: "14px" }}
                  style={{
                    width: 124,
                    height: "40px",
                    fontSize: "14px",
                    justifyContent: "center",
                  }}
                >
                  筛选
                  <CaretDownOutlined />
                </Button>
              </Dropdown>
            </Space>
            <div>{`共${data.length}个`}</div>
          </div>
          <div>
            <div
              className="scrollbar"
              style={{ overflowX: data.length >= 5 ? "auto" : "hidden" }}
            >
              {data.length === 0 && <Empty />}
              <Row
                gutter={[20, 36]}
                style={{
                  display: "flex",
                  flexWrap: "nowrap",
                  marginBottom: "15px",
                }}
              >
                {data.map((item) => (
                  <Col span={5} key={item.id} style={{ flex: 1 }}>
                    <Card
                      className={
                        item.modelName.slice(0, 2) === "行至"
                          ? "purpleBlue"
                          : "otherColor"
                      }
                      bordered={false}
                      headStyle={{ borderBottom: "none" }}
                      style={{
                        wordWrap: "break-word",
                        height: "170px",
                        width: "248px",
                        color: "#FFFFFF",
                      }}
                    >
                      <Tooltip title={item.modelName}>
                        <div className={"baseModelNameTitle"}>
                          {item.modelName}
                        </div>
                      </Tooltip>
                      <Tooltip title={item.introduction}>
                        <div className={"baseModelIntroduction"}>
                          {item.introduction}
                        </div>
                      </Tooltip>
                      <Button
                        style={{
                          position: "absolute",
                          left: "74%",
                          bottom: "7px",
                          color: "#FFFFFF",
                        }}
                        type="text"
                        onClick={() => {
                          //setSelectDataSet(dataSet);
                          onClickDetail(item.id);
                          getModelsDetail(item.id);
                          if (!openDrawView) {
                            setOpenDrawView(true);
                          }
                        }}
                      >
                        详情
                      </Button>
                      <div style={{ position: "relative" }}>
                        {item.modelName.slice(0, 2) === "行至" && (
                          <img
                            src={logo}
                            style={{
                              position: "absolute",
                              left: "88%",
                              bottom: "35px",
                              width: "36px",
                              height: "35px",
                            }}
                          />
                        )}
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          </div>
        </div>
        <div className="myModelArea">
          <div
            style={{
              width: "100%",
              textAlign: "start",
              justifyContent: "space-between",
              display: "inline-flex",
              marginBottom: "1.5rem",
              alignItems: "center",
            }}
          >
            <Space size="small">
              <div
                style={{
                  margin: "0 1rem 0 0",
                  width: "64px",
                  height: "19px",
                  fontWeight: "500",
                  fontSize: "16px",
                }}
              >
                我的模型
              </div>
              <Space.Compact>
                <Input
                  size="large"
                  className="filter-input"
                  suffix={<SearchOutlined />}
                  onChange={(e) => {
                    setFilterMyInput(e.target.value);
                  }}
                  placeholder={`请输入我的模型名称`}
                />
              </Space.Compact>

              <Dropdown
                menu={{
                  items: myModelDropdownItems,
                  onClick: handleMyModelMenuClick,
                }}
              >
                <Button
                  className="default-btn"
                  // style={{ width: "124px", height: "40px", fontSize: "14px" }}
                  style={{
                    width: 124,
                    height: "40px",
                    fontSize: "14px",
                    justifyContent: "center",
                  }}
                >
                  筛选
                  <CaretDownOutlined />
                </Button>
              </Dropdown>
            </Space>
            <div>{`共${mydata.length}个`}</div>
          </div>
          <div
            style={{ minHeight: "159px" }}
            className={mydata.length === 0 ? "empty" : ""}
          >
            {mydata.length === 0 && <Empty />}
            <Row gutter={[20, 36]} style={{ display: "flex" }}>
              {mydata.map((item) => (
                <Col key={item.id} span={5} style={{ flex: 1 }}>
                  {showRetrain[item.id] && (item.status === 3||item.status === 0||item.status === 4)? (
                    <Card
                      className="interrupted"
                      bordered={false}
                      headStyle={{ borderBottom: "none" }}
                      style={{
                        wordWrap: "break-word",
                        height: "170px",
                        width: "230px",
                        color: "#FFFFFF",
                      }}
                      title={
                        <span>
                          <ExclamationCircleFilled
                            style={{ color: "red", fontSize: "16px" }}
                          />{" "}
                          提示
                        </span>
                      }
                    >
                      <div className="text">确定要重新训练吗？</div>
                      <div className="btn">
                        <Button
                          onClick={() => {
                            setShowRetrain((prevShowRetrain) => ({
                              ...prevShowRetrain,
                              [item.id]: false,
                            }));
                          }}
                        >
                          取消
                        </Button>
                        <Button
                          onClick={() => {
                            // deleteModel(Number(item.id));
                            reTrainById(item.id)
                            setShowRetrain((prevShowRetrain) => ({
                              ...prevShowRetrain,
                              [item.id]: false,
                            }));
                          }}
                        >
                          确定
                        </Button>
                      </div>
                    </Card>
                  ) : ((showInterrupt[item.id]&&(item.status === 2||item.status === 0))?<>
                    <Card
                      className="interrupted"
                      bordered={false}
                      headStyle={{ borderBottom: "none" }}
                      style={{
                        wordWrap: "break-word",
                        height: "170px",
                        width: "230px",
                        color: "#FFFFFF",
                      }}
                      title={
                        <span>
                          <ExclamationCircleFilled
                            style={{ color: "red", fontSize: "16px" }}
                          />{" "}
                          提示
                        </span>
                      }
                    >
                      <div className="text">确定要停止训练吗？</div>
                      <div className="btn">
                        <Button
                          onClick={() => {
                            setShowInterrupt((prevShowRetrain) => ({
                              ...prevShowRetrain,
                              [item.id]: false,
                            }));
                          }}
                        >
                          取消
                        </Button>
                        <Button
                          onClick={() => {
                            cannelTrain(Number(item.id));
                            setShowInterrupt((prevShowRetrain) => ({
                              ...prevShowRetrain,
                              [item.id]: false,
                            }));
                            message.success('模型训练正在停止...')
                          }}
                        >
                          确定
                        </Button>
                      </div>
                    </Card></>:
                    <Card
                      className={
                        item.status === 7
                          ? "islive"
                          : item.status === 6
                          ? "goingLive"
                          : item.status === 5
                          ? "done"
                          : item.status === 2
                          ? "training"
                          : "interrupted"
                      }
                      bordered={false}
                      headStyle={{ borderBottom: "none" }}
                      style={{
                        wordWrap: "break-word",
                        height: "170px",
                        width: "230px",
                        color: "#FFFFFF",
                      }}
                    >
                      <Tooltip title={item.modelName}>
                        <div className={"modelNameTitle"}>{item.modelName}</div>
                      </Tooltip>
                      <Tooltip title={item.introduction}>
                        <div className={"introduction"}>
                          {item.introduction}
                        </div>
                      </Tooltip>

                      <div className={"statusInfo"}>
                        <div
                          className={
                            item.status === 5 ? "modelStatus" : "otherStatus"
                          }
                        >
                          <span
                            className={
                              item.status === 7
                                ? "liveround"
                                : item.status === 6
                                ? "goliveround"
                                : item.status === 5
                                ? "interruptedround"
                                : item.status === 2
                                ? "traninground"
                                : "doneround"
                            }
                          />
                          {modelStatusChange(item.status)}
                        </div>
                        <div className={"actionInfo"}>
                          {item.status === 7 ? (
                            <div className="prolong">
                              {/* <Button style={{ left: "76%", color: "#000000", fontSize: "13px" }} type="text"
                            onClick={() => {
                              setProlong(true)
                            }}
                          >延长 |
                          </Button> */}
                              {/* <Button
                              type="text"
                              className={"btdStyle"}
                              onClick={() => {
                                setModelId(item.id);
                                setModelStatus(item.status);
                                setOffmodel(true);
                              }}
                            >
                              下线 |
                            </Button> */}
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  //setSelectDataSet(dataSet);
                                  if (!openDrawView) {
                                    getModelConfig(item.id).then((res: any) => {
                                      if (res.data?.code === 200) {
                                        sessionStorage.setItem(
                                          "config",
                                          JSON.stringify(res.data.data)
                                        );
                                      }
                                    });

                                    navigate(
                                      `/main/finetune/adjustment/${item.id}`
                                    );
                                  }
                                }}
                              >
                                调试 |
                              </Button>
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  const name = getModelName(item.id);
                                  navigate(
                                    `/main/finetune/configure/${item.id}/${name}/${item.status}`
                                  );
                                }}
                              >
                                配置|
                              </Button>
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  //setSelectDataSet(dataSet);
                                  getModelName(modelDetail.modelBaseId);
                                  const name = getModelName(item.id);
                                  navigate(
                                    `/main/finetune/detail/${item.id}/${name}`
                                  );
                                }}
                              >
                                详情
                              </Button>
                            </div>
                          ) : item.status === 5 ? (
                            <div className="donetext">
                              {/* <Button
                              type="text"
                              className={"btdStyle"}
                              // disabled={!lightWeightState[currentItemId as keyof typeof lightWeightState]}
                              onClick={() => {
                                //setSelectDataSet(dataSet);
                                if (!openDrawView) {
                                  setModelId(item.id);
                                  setModelStatus(item.status);
                                  setOnline(true);
                                }
                              }}
                            >
                              上线 |
                            </Button> */}
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  const name = getModelName(item.id);
                                  navigate(
                                    `/main/finetune/configure/${item.id}/${name}/${item.status}`
                                  );
                                }}
                              >
                                配置|
                              </Button>
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  //setSelectDataSet(dataSet);
                                  // onClickMyDetail(item.id)
                                  //getModelsDetail(item.id)
                                  const name = getModelName(item.id);
                                  navigate(
                                    `/main/finetune/detail/${item.id}/${name}`
                                  );
                                }}
                              >
                                详情
                              </Button>
                              {/* <Button type="text" className={'btdStyle'}
                              onClick={() => { handleLightWeight(item.id); setModelStatus(item.status) }}
                            >轻量化
                            </Button> */}
                            </div>
                          ) : item.status === 3 || item.status === 4 ? (
                            <div className="Interrupted">
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  setShowRetrain((prevShowRetrain) => ({
                                    ...prevShowRetrain,
                                    [item.id]: true,
                                  }));
                                  setModelId(item.id);
                                }}
                              >
                                重新训练
                              </Button>
                              {/* <Button
                              type="text"
                              className={"btdStyle"}
                              onClick={() => {
                                const name = getModelName(item.id);
                                navigate(
                                  `/main/finetune/configure/${item.id}/${name}`
                                );
                              }}
                            >
                              配置|
                            </Button>
                            <Button
                              type="text"
                              className={"btdStyle"}
                              onClick={() => {
                                onClickMyDetail(item.id);
                                getModelsDetail(item.id);
                                // getMyModelsDetail(item.id)
                                if (!interruptDrawView) {
                                  setInterruptDrawView(true);
                                }
                              }}
                            >
                              详情
                            </Button> */}
                            </div>
                          ) : item.status === 2 ? (
                            <div className="training1">
                              <Button
                                type="text"
                                className={"btdStyle"}
                                onClick={() => {
                                  // setModelId(item.id);
                                  // setModelStatus(item.status);
                                  // setCancelTrain(true);
                                  // setTrainTaskId(item.trainTaskId);
                                  // setModalTitle("模型暂停");
                                  // deleteModel(Number(item.id));
                                  setShowInterrupt((prevShowInterrupt) => ({
                                    ...prevShowInterrupt,
                                    [item.id]: true,
                                  }))
                                }}
                              >
                                停止训练
                              </Button>
                              {/* <Button
                              type="text"
                              className={"btdStyle"}
                              onClick={() => {
                                const name = getModelName(item.id);
                                navigate(
                                  `/main/finetune/configure/${item.id}/${name}`
                                );
                              }}
                            >
                              配置|
                            </Button>
                            <Button
                              type="text"
                              className={"btdStyle"}
                              onClick={() => {
                                onClickMyDetail(item.id);
                                getModelsDetail(item.id);
                                // getMyModelsDetail(item.id)
                                if (!myopenDrawView) {
                                  setMyOpenDrawView(true);
                                }
                              }}
                            >
                              详情
                            </Button> */}
                            </div>
                          ) : // : item.status === 6 ? (
                          //   <div className="Interrupted">
                          //     <Button
                          //       type="text"
                          //       className={"btdStyle"}
                          //       onClick={() => {
                          //         const name = getModelName(item.id);
                          //         navigate(
                          //           `/main/finetune/configure/${item.id}/${name}`
                          //         );
                          //       }}
                          //     >
                          //       配置|
                          //     </Button>
                          //     <Button
                          //       type="text"
                          //       className={"btdStyle"}
                          //       onClick={() => {
                          //         //setSelectDataSet(dataSet);
                          //         getMyModelsDetail(item.id);
                          //         if (!myopenDrawView) {
                          //           setMyOpenDrawView(true);
                          //         }
                          //       }}
                          //     >
                          //       详情
                          //     </Button>
                          //   </div>
                          // )
                          // : (
                          //   item.status === 1 && (
                          //     <div className="online">
                          //       <Button
                          //         type="text"
                          //         className={"btdStyle"}
                          //         onClick={() => {
                          //           setModelId(item.id);
                          //           setModelStatus(item.status);
                          //           setCancelTrain(true);
                          //           setModalTitle("模型训练");
                          //         }}
                          //       >
                          //         训练 |
                          //       </Button>
                          //       <Button
                          //         type="text"
                          //         className={"btdStyle"}
                          //         onClick={() => {
                          //           const name = getModelName(item.id);
                          //           navigate(
                          //             `/main/finetune/configure/${item.id}/${name}`
                          //           );
                          //         }}
                          //       >
                          //         配置|
                          //       </Button>
                          //       <Button
                          //         type="text"
                          //         className={"btdStyle"}
                          //         onClick={() => {
                          //           onClickMyDetail(item.id);
                          //           if (!myopenDrawView) {
                          //             setMyOpenDrawView(true);
                          //           }
                          //         }}
                          //       >
                          //         详情
                          //       </Button>
                          //     </div>
                          //   )
                          // )
                          null}
                        </div>
                      </div>
                    </Card>
                  )}
                </Col>
              ))}
            </Row>
          </div>
        </div>
      </div>
      <OpenDraw
        visible={openDrawView}
        titltext={titltext}
        moduletext={moduletext}
        buttontext={buttonText}
        logo={true}
        text={true}
        onButtonClick={() => {
          navigate("/main/finetune/create", {
            state: { modelName: titltext, modelId: modelId },
          });
        }}
        OnClose={() => {
          setOpenDrawView(false);
        }}
      >
        <DescriptionsWrapper>{descItems}</DescriptionsWrapper>
        <div style={{ position: "absolute", bottom: "46px", right: "25%" }}>
          <Tooltip title={tooltip}>
            <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
          </Tooltip>
        </div>
      </OpenDraw>
      <TimeModal
        visible={online}
        titltext={"选择上线时间"}
        buttontext={"确认上线"}
        timetext="持续"
        onButtonClick={() => handleButtonClick(timeValue)}
        value={timeValue}
        OnClose={() => {
          setOnline(false);
        }}
        onValueChange={setTimeValue}
      >
        <></>
      </TimeModal>
      <TimeModal
        visible={prolong}
        titltext={"延长上线时间"}
        buttontext={"确认延长"}
        timetext="延长"
        onButtonClick={() => {
          // 处理点击事件的逻辑
          //modal.destroy()
          setProlong(false);
        }}
        OnClose={() => {
          setProlong(false);
        }}
      >
        <></>
      </TimeModal>
      <Modal
        width={429}
        title={
          <div className="modalTitle">
            <img src={warningIcon} style={{ marginRight: "12px" }} alt="" />
            模型体积信息
          </div>
        }
        centered
        open={lightWeight}
        footer={null}
        maskClosable={false}
        onCancel={() => setLightWeight(false)}
      >
        <div className="lightWeightModal">
          <span className={"lightWeightContent origion"}>原模型体积：10GB</span>
          <span className={"lightWeightContent"}>
            轻量化模型体积：
            {status === 7
              ? "8GB"
              : lightWeightState[currentItemId as keyof typeof lightWeightState]
              ? "8GB"
              : "待确认"}
          </span>
        </div>
        <div className="offmodelButton">
          <Button
            className="offmodeltrue"
            disabled={
              status === 7
                ? true
                : lightWeightState[
                    currentItemId as keyof typeof lightWeightState
                  ]
            }
            onClick={() => confirmLightWeight(currentItemId)}
          >
            轻量化
          </Button>
        </div>
      </Modal>
      <Modal
        width={429}
        title="模型下线"
        centered
        open={offmodel}
        footer={null}
        maskClosable={false}
        onCancel={() => setOffmodel(false)}
      >
        <div className="offmodel">确认模型下线</div>
        <div className="offmodelButton">
          <Button className="offmodelCancel" onClick={() => setOffmodel(false)}>
            取消
          </Button>
          <Button
            className="offmodeltrue"
            onClick={() => {
              // modelOffline(modelId, modelStatus)
              setOffmodel(false);
              onlineModel(modelId).then((res:any) => {
                if (res.data?.code === 200) {
                  message.success(res.data?.data?.message || "下线成功");
                }
              });
            }}
          >
            确认下线
          </Button>
        </div>
      </Modal>
      <Modal
        width={429}
        title={modalTitle}
        centered
        open={cancelTrain}
        footer={null}
        maskClosable={false}
        onCancel={() => setCancelTrain(false)}
      >
        <div className="offmodel">{"确认" + modalTitle}</div>
        <div className="offmodelButton">
          <Button
            className="offmodelCancel"
            onClick={() => setCancelTrain(false)}
          >
            取消
          </Button>
          <Button
            className="offmodeltrue"
            onClick={() => {
              if (modalTitle === "模型训练") {
                startTrain(modelId).then((res:any) => {
                  if (res.data?.code === 200) {
                    message.success("开始训练");
                  }
                });
              } else if (modalTitle === "模型暂停") {
                cannelTrain(Number(modelId)).then((res:any) => {
                  if (res.data?.code === 200) {
                    message.success("暂停成功");
                  }
                });
              }
              setCancelTrain(false);
            }}
          >
            确认
          </Button>
        </div>
      </Modal>

      <OpenDraw
        visible={myopenDrawView}
        titltext={titltext}
        buttontext={"确定"}
        logo={false}
        text={false}
        isbutton={false}
        onButtonClick={() => {
          setMyOpenDrawView(false);
        }}
        OnClose={() => {
          setMyOpenDrawView(false);
        }}
      >
        <DescriptionsWrapper>{trainItems}</DescriptionsWrapper>
      </OpenDraw>
      <OpenDraw
        visible={interruptDrawView}
        titltext={titltext}
        buttontext={"确定"}
        logo={false}
        text={false}
        isbutton={false}
        onButtonClick={() => {
          setInterruptDrawView(false);
        }}
        OnClose={() => {
          setMyOpenDrawView(false);
        }}
      >
        <DescriptionsWrapper>{trainItems}</DescriptionsWrapper>
      </OpenDraw>
      <OpenDraw
        visible={interruptDrawView}
        titltext={titltext}
        // buttontext="继续训练"
        // buttontext1="重新训练"
        buttontext="重新训练"
        interruptText="请结合中断原因及训练进度选择适合您的训练方式"
        isInterruptText={true}
        logo={false}
        text={false}
        // isbutton={true}
        isbutton={false}
        onButtonClick={() => {
          startTrain(modelId).then((res:any) => {
            if (res.data?.code === 200) {
              message.success("开始训练");
              setInterruptDrawView(false);
            } else {
              setInterruptDrawView(false);
            }
          });
        }}
        OnClose={() => {
          setInterruptDrawView(false);
        }}
      >
        <DescriptionsWrapper>{interruptItems}</DescriptionsWrapper>
        <div style={{ position: "absolute", bottom: "103px", left: "18%" }}>
          <Tooltip title="请结合中断原因及训练进度选择适合您的训练方式">
            <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
          </Tooltip>
        </div>
      </OpenDraw>
    </Scrollbar>
  );
};

export default FineTuneView;
