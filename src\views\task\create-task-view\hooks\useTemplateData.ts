import { useState, useEffect } from 'react';
import { getAllTemplates } from '../../../../api/task';
import { TemplateType } from '../types';

/**
 * 模板数据管理Hook
 */
export const useTemplateData = () => {
  const [exampleText, setExampleText] = useState<TemplateType[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [textValue, setTextValue] = useState('');
  const [textValueTemplate, setTextValueTemplate] = useState<TemplateType>({
    id: 0,
    templateName: '',
    roleBackground: '',
    detailedDescription: '',
  });

  // 获取模板数据
  useEffect(() => {
    getAllTemplates()
      .then((response) => {
        if (response.data.code === 200) {
          const res = response.data;
          setExampleText(res.data);
        }
      })
      .catch((error) => {
        console.log('获取模板数据失败', error);
      });
  }, []);

  // 应用模板
  const handleApplyTemplate = (item: TemplateType, index: number) => {
    setTextValueTemplate(item);

    // 去除roleBackground开头和结尾的换行符
    const processedRoleBackground = item.roleBackground.replace(/^\s+|\s+$/g, '');
    // 去除detailedDescription开头和结尾的换行符
    const processedDetailedDescription = item.detailedDescription.replace(/^\s+|\s+$/g, '');

    const newText = `角色背景：\n${processedRoleBackground}\n详情描述：\n${processedDetailedDescription}`;
    setTextValue(newText);
  };

  // 选择模板项
  const handleItemClick = (index: number) => {
    setSelectedIndex(index);
  };

  // 文本变化处理
  const handleTextChange = (text: string) => {
    setTextValue(text);
    setSelectedIndex(-1);
  };

  return {
    exampleText,
    selectedIndex,
    textValue,
    textValueTemplate,
    handleApplyTemplate,
    handleItemClick,
    handleTextChange,
  };
};
