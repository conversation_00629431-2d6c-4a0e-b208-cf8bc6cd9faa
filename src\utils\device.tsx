const userAgent = navigator.userAgent;

export function getBroswer() {
  let browser = "";
  if (/Chrome/.test(userAgent)) {
    browser = "Chrome";
  } else if (/Safari/.test(userAgent)) {
    browser = "Safari";
  } else if (/Firefox/.test(userAgent)) {
    browser = "Firefox";
  } else if (/Edge/.test(userAgent)) {
    browser = "Edge";
  }
  return browser;
}

export function getOS() {
  let os = "";
  if (userAgent.indexOf("Windows") !== -1) {
    os = "Windows";
  } else if (userAgent.indexOf("Macintosh") !== -1) {
    os = "MacOS";
  } else if (userAgent.indexOf("Linux") !== -1) {
    os = "Linux";
  } else {
    os = "Unknown";
  }
  return os;
}

// export function getIP() {
//     const networkInfo = navigator.connection || {};
//     const { type, effectiveType } = networkInfo;

//     if (type === 'cellular' || effectiveType === '4g') {
//       // 如果是移动网络，使用IPV4地址
//       const ipv4 = getIPv4();
//       setIp(ipv4);
//     } else {
//       // 如果是有线网络，使用IPV6地址
//       const ipv6 = getIPv6();
//       setIp(ipv6);
//     }
// }

// function getIPv4() {
//   const interfaces = require('os').networkInterfaces();
//   let ipv4 = '';

//   Object.keys(interfaces).forEach((netInterface) => {
//     interfaces[netInterface].forEach((interfaceItem) => {
//       if (interfaceItem.family === 'IPv4' && !interfaceItem.internal) {
//         ipv4 = interfaceItem.address;
//       }
//     });
//   });

//   return ipv4;
// }

// function getIPv6() {
//   const interfaces = require('os').networkInterfaces();
//   let ipv6 = '';

//   Object.keys(interfaces).forEach((netInterface) => {
//     interfaces[netInterface].forEach((interfaceItem) => {
//       if (interfaceItem.family === 'IPv6' && !interfaceItem.internal) {
//         ipv6 = interfaceItem.address;
//       }
//     });
//   });

//   return ipv6;
// }