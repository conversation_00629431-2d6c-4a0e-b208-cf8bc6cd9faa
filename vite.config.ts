import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import * as path from 'path'

export default defineConfig({
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },

  // 构建优化配置
  build: {
    // 启用 gzip 压缩提示的阈值
    chunkSizeWarningLimit: 1000,
    // 启用多线程构建
    target: 'esnext',
    // 减少构建输出
    reportCompressedSize: false,
    rollupOptions: {
      output: {
        // 手动分包策略 - 更细粒度的分包
        manualChunks: (id) => {
          // React 核心库
          if (id.includes('react') || id.includes('react-dom')) {
            return 'react-core';
          }
          // React 路由
          if (id.includes('react-router')) {
            return 'react-router';
          }
          // Antd 核心组件
          if (id.includes('antd') && !id.includes('@ant-design/icons')) {
            return 'antd-core';
          }
          // Antd 图标
          if (id.includes('@ant-design/icons')) {
            return 'antd-icons';
          }
          // 图表相关
          if (id.includes('echarts')) {
            return 'charts';
          }
          // 网络请求
          if (id.includes('axios')) {
            return 'http';
          }
          // 文件处理相关
          if (id.includes('file-saver') || id.includes('jszip') || id.includes('html2canvas')) {
            return 'file-utils';
          }
          // 编辑器和文档查看器
          if (id.includes('react-file-viewer') || id.includes('react-markdown') || id.includes('@cyntler/react-doc-viewer')) {
            return 'editors';
          }
          // 滚动条组件
          if (id.includes('scrollbars')) {
            return 'scrollbars';
          }
          // Redux 相关
          if (id.includes('redux') || id.includes('react-redux')) {
            return 'redux';
          }
          // 其他工具库
          if (id.includes('js-cookie') || id.includes('nprogress') || id.includes('normalize.css')) {
            return 'utils';
          }
          // 大型第三方库单独分包
          if (id.includes('jspreadsheet-ce')) {
            return 'spreadsheet';
          }
          if (id.includes('react-wordcloud')) {
            return 'wordcloud';
          }
          // node_modules 中的其他库
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        }
      }
    },
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 构建后清理输出目录
    emptyOutDir: true,
    // 启用源码映射（生产环境可关闭以减小体积）
    sourcemap: false,
    // 设置资源内联阈值（小于4kb的资源会被内联为base64）
    assetsInlineLimit: 4096,
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 移除 console
        drop_console: true,
        // 移除 debugger
        drop_debugger: true,
        // 移除无用代码
        dead_code: true,
        // 移除未使用的变量
        unused: true,
        // 压缩条件表达式
        conditionals: true,
        // 压缩比较运算
        comparisons: true,
        // 压缩序列
        sequences: true
      },
      mangle: {
        // 混淆变量名
        safari10: true
      }
    },
    // 设置输出目录
    outDir: 'dist',
    // 设置静态资源目录
    assetsDir: 'assets'
  },

  // 依赖优化
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      '@ant-design/icons',
      'axios',
      'dayjs'
    ],
    // // 排除开发依赖
    // exclude: [
    //   'redux-logger',
    //   'web-vitals'
    // ]
  },

  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://xz1.puhuacloud.com:18105',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})