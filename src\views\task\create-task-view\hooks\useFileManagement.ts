import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { message } from 'antd';
import { uploadDataset } from '../../../../api/dataset';
import { DatasetFile } from '../types';
import { DataSetStatus, DataSetType } from '../../../../types';

/**
 * 文件管理Hook
 */
export const useFileManagement = () => {
  const { state } = useLocation();
  const [fileList, setFileList] = useState<DatasetFile[]>([]);
  const [showDataModal, setShowDataModal] = useState<boolean>(false);
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);

  // 初始化文件列表
  useEffect(() => {
    if (state && state.selectedRows) {
      const { selectedRows } = state;
      const updateFiles = fileList;
      selectedRows.forEach((row: DataSetType) => {
        const data: any = new Object();
        data.dataSetId = row.id;
        data.name = row.datasetName;
        data.tags = row.tags;
        data.parseProcess = row.progress;
        data.dataSource = 0;
        if (row.datasetStatus === DataSetStatus.sucess) {
          data.status = 'success';
        } else if (row.datasetStatus === DataSetStatus.failed) {
          data.status = 'error';
        }
        updateFiles.push(data);
      });
      setFileList(updateFiles);
    }
  }, []);

  // 添加标签
  const handleAddTag = (dataSetId: string, tags: string[]) => {
    const updateList = fileList;
    updateList.forEach((file) => {
      if (file.dataSetId === dataSetId) {
        file.tags = tags;
      }
    });
    setFileList(updateList);
  };

  // 删除文件
  const handleDelFile = (index: number, datasetModalRef: React.RefObject<any>) => {
    const fileToDelete = fileList[index];

    // 如果删除的文件来自数据集选择（有dataSetId），则需要同步更新checkbox状态
    if (fileToDelete.dataSetId && datasetModalRef.current) {
      datasetModalRef.current.removeSelection(fileToDelete.dataSetId);
    }

    // 从文件列表中移除
    const updatedDataList = fileList.filter((item, i) => i !== index);
    setFileList(updatedDataList);
  };

  // 重新上传
  const handleReUpload = (index: number) => {
    const file = fileList[index];
    let status = 'uploading';
    const importFile: any = file;
    importFile.status = status;
    uploadDataset({ importDataset: file, tags: [] }).then((res: any) => {
      if (res.data?.code === 200) {
        importFile.status = 'success';
        importFile.dataSetId = res.data.data;
      } else {
        importFile.status = 'error';
      }
      fileList[index] = importFile;
      setFileList(fileList);
    });
  };

  // 处理数据集选择模态框关闭
  const handleDataModalClose = (rows?: DataSetType[]) => {
    const files = fileList;
    if (rows) {
      rows.forEach((row) => {
        const isDuplicate = files.some((file) => file.dataSetId === row.id);

        if (!isDuplicate) {
          const data: any = new Object();
          data.dataSetId = row.id;
          data.name = row.datasetName;
          data.tags = row.tags;
          data.parseProcess = row.progress;
          data.dataSource = 0;
          if (row.datasetStatus === DataSetStatus.sucess) {
            data.status = 'success';
          } else if (row.datasetStatus === DataSetStatus.failed) {
            data.status = 'error';
          }
          files.push(data);
        }
      });
      setFileList(files);
    }
    setShowDataModal(false);
  };

  // 处理上传错误模态框关闭
  const handleUploadErrorModalClose = () => {
    setUploadErrorModal(false);
  };

  // 显示数据模态框
  const handleShowDataModal = () => {
    setShowDataModal(true);
  };

  // 关闭数据模态框
  const handleCloseDataModal = () => {
    setShowDataModal(false);
  };

  // 数据模态框确认
  const handleDataModalOk = (selectedRows?: DataSetType[]) => {
    if (selectedRows) {
      const files = [...fileList];
      selectedRows.forEach((row: DataSetType) => {
        const data: any = {
          dataSetId: row.id,
          name: row.datasetName,
          tags: row.tags,
          parseProcess: row.progress,
          dataSource: 0,
        };
        if (row.datasetStatus === DataSetStatus.sucess) {
          data.status = 'success';
        } else if (row.datasetStatus === DataSetStatus.failed) {
          data.status = 'error';
        }
        files.push(data);
      });
      setFileList(files);
    }
    setShowDataModal(false);
  };

  // 关闭上传错误模态框
  const handleCloseUploadErrorModal = () => {
    setUploadErrorModal(false);
  };

  return {
    fileList,
    showDataModal,
    uploadErrorModal,
    setShowDataModal,
    setUploadErrorModal,
    handleAddTag,
    handleDelFile,
    handleReUpload,
    handleShowDataModal,
    handleCloseDataModal,
    handleDataModalOk,
    handleDataModalClose,
    handleUploadErrorModalClose,
    handleCloseUploadErrorModal,
  };
};
