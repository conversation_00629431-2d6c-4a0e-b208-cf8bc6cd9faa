import { DatasetFileEnum } from '@/types';

export const getFileType = (fileTypeStr: string) => {
  let fileType = 0;
  if (fileTypeStr === 'txt') {
    fileType = DatasetFileEnum.Txt;
  } else if (fileTypeStr === 'doc' || fileTypeStr === 'docx') {
    fileType = DatasetFileEnum.Doc;
  } else if (fileTypeStr === 'pdf') {
    fileType = DatasetFileEnum.Pdf;
  } else if (fileTypeStr === 'csv') {
    fileType = DatasetFileEnum.Csv;
  } else if (fileTypeStr === 'xlsx') {
    fileType = DatasetFileEnum.Xlsx;
  } else if (fileTypeStr === 'json') {
    fileType = DatasetFileEnum.Json;
  } else if (fileTypeStr === 'md') {
    fileType = DatasetFileEnum.Markdown;
  } else if (fileTypeStr === 'png' || fileTypeStr === 'jpg' || fileTypeStr === 'jpeg') {
    fileType = DatasetFileEnum.Image;
  }
  return fileType;
};
