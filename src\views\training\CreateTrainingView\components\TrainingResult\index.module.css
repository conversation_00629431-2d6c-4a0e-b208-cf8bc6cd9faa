.result-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 2rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.result-container img {
    width: 48px;
    height: 48px;
}

.result-title-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.result-title {
    font-size: 30px;
    font-weight: 400;
    line-height: 35px;
    letter-spacing: 0em;
    text-align: left;
    color: rgba(0, 0, 0, 1);
}

.result-subtitle {
    font-size: 20px;
    font-weight: 400;
    line-height: 23px;
    letter-spacing: 0em;
    text-align: left;
    color: rgba(142, 152, 167, 1);
}

.result-btn-div {}

.confirm-btn {
    position: absolute;
    bottom: 8rem;
    left: 50%;
    transform: translateX(-50%);
    width: 202px;
    height: 48px;
}