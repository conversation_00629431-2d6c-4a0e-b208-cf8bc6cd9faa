import React, { useCallback } from 'react';
import { Button, Dropdown, Empty, Input, Row, Space, Tag } from 'antd';
import { CaretDownOutlined, SearchOutlined, CloseOutlined } from '@ant-design/icons';
import { ModelSetType } from '@/types';
import { debounce } from '@/utils/utils';
import ModelCard from './ModelCard';

interface MyModelSectionProps {
  data: ModelSetType[];
  filterMyInput: string;
  sortMyModel: string;
  myModelStatus: string;
  showRetrain: { [key: string]: boolean };
  showInterrupt: { [key: string]: boolean };
  onFilterMyInputChange: (value: string) => void;
  onSortMyModelChange: (sort: string) => void;
  onMyModelStatusChange: (status: string) => void;
  onShowRetrainChange: (showRetrain: { [key: string]: boolean }) => void;
  onShowInterruptChange: (showInterrupt: { [key: string]: boolean }) => void;
  onModelClick: (id: string) => void;
  onModelDelete: (id: string) => void;
  onRetrainClick: (id: string) => void;
  onInterruptClick: (id: string) => void;
  onNavigateToConfig: (id: string, name: string, status: number) => void;
  onNavigateToDetail: (id: string, name: string) => void;
  onNavigateToAdjustment: (id: string) => void;
  getModelName: (id: string) => string | undefined;
  getModelConfig: (id: string) => Promise<any>;
}

const MyModelSection: React.FC<MyModelSectionProps> = ({
  data,
  filterMyInput,
  sortMyModel,
  myModelStatus,
  showRetrain,
  showInterrupt,
  onFilterMyInputChange,
  onSortMyModelChange,
  onMyModelStatusChange,
  onShowRetrainChange,
  onShowInterruptChange,
  onModelClick,
  onModelDelete,
  onRetrainClick,
  onInterruptClick,
  onNavigateToConfig,
  onNavigateToDetail,
  onNavigateToAdjustment,
  getModelName,
  getModelConfig,
}) => {
  // 使用防抖处理搜索输入
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onFilterMyInputChange(value);
    }, 500),
    [onFilterMyInputChange]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  const handleMenuClick = (e: any) => {
    if (e.key === '1-1') {
      onMyModelStatusChange('');
      onSortMyModelChange('asc');
    } else if (e.key === '1-2') {
      onMyModelStatusChange('');
      onSortMyModelChange('desc');
    }
    if (e.key === '2-1') {
      onMyModelStatusChange('7');
    } else if (e.key === '2-2') {
      onMyModelStatusChange('5');
    } else if (e.key === '2-3') {
      onMyModelStatusChange('2');
    } else if (e.key === '2-4') {
      onMyModelStatusChange('3');
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case '7':
        return '在线';
      case '5':
        return '离线';
      case '2':
        return '训练中';
      case '3':
        return '训练失败';
      default:
        return '';
    }
  };

  const clearFilter = () => {
    onMyModelStatusChange('');
  };

  const myModelDropdownItems = [
    {
      key: 'moduleName',
      label: '按模型名称',
      children: [
        {
          key: '1-1',
          label: '正序',
        },
        {
          key: '1-2',
          label: '倒序',
        },
      ],
    },
    {
      key: 'trainingStatus',
      label: '按训练状态',
      children: [
        {
          key: '2-1',
          label: '在线',
        },
        {
          key: '2-2',
          label: '离线',
        },
        {
          key: '2-3',
          label: '训练中',
        },
        {
          key: '2-4',
          label: '训练失败',
        },
      ],
    },
  ];

  return (
    <div className="myModelArea">
      <div
        style={{
          width: '100%',
          textAlign: 'start',
          justifyContent: 'space-between',
          display: 'inline-flex',
          marginBottom: '1.5rem',
          alignItems: 'center',
        }}
      >
        <Space size="small">
          <div
            style={{
              margin: '0 1rem 0 0',
              width: '64px',
              height: '19px',
              fontWeight: '500',
              fontSize: '16px',
            }}
          >
            我的模型
          </div>
          <Space.Compact>
            <Input
              size="large"
              className="filter-input"
              suffix={<SearchOutlined />}
              defaultValue={filterMyInput}
              onChange={handleInputChange}
              placeholder="请输入我的模型名称"
            />
          </Space.Compact>

          <Dropdown
            menu={{
              items: myModelDropdownItems,
              onClick: handleMenuClick,
            }}
          >
            <Button
              className="default-btn"
              style={{
                width: 124,
                height: '40px',
                fontSize: '14px',
                justifyContent: 'center',
                margin: '0 0 0 1rem',
              }}
            >
              {/* 显示筛选状态 */}
              {myModelStatus ? (
                <div>
                  <span style={{ marginRight: '8px' }}>{getStatusLabel(myModelStatus)}</span>
                  <CloseOutlined onClick={clearFilter} />
                </div>
              ) : (
                '筛选'
              )}

              <CaretDownOutlined />
            </Button>
          </Dropdown>
        </Space>
        <div>{`共${data.length}个`}</div>
      </div>
      <div style={{ minHeight: '159px' }} className={data.length === 0 ? 'empty' : ''}>
        {data.length === 0 && <Empty />}
        <Row gutter={[20, 36]} wrap={true}>
          {data.map((item) => (
            <ModelCard
              key={item.id}
              item={item}
              showRetrain={showRetrain[item.id]}
              showInterrupt={showInterrupt[item.id]}
              onShowRetrainChange={(show) =>
                onShowRetrainChange({ ...showRetrain, [item.id]: show })
              }
              onShowInterruptChange={(show) =>
                onShowInterruptChange({ ...showInterrupt, [item.id]: show })
              }
              onModelClick={onModelClick}
              onModelDelete={onModelDelete}
              onRetrainClick={onRetrainClick}
              onInterruptClick={onInterruptClick}
              onNavigateToConfig={onNavigateToConfig}
              onNavigateToDetail={onNavigateToDetail}
              onNavigateToAdjustment={onNavigateToAdjustment}
              getModelName={getModelName}
              getModelConfig={getModelConfig}
            />
          ))}
        </Row>
      </div>
    </div>
  );
};

export default MyModelSection;
