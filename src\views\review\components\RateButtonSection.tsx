import React, { useEffect, useState } from 'react';
import { Button, Space } from 'antd';
import Icon from '@ant-design/icons';
import { QADocument, TaskDetailType, AllocatedQA } from '../../../types';
import { ScoreReviewParams, queryQaAllocated, scoreReview } from '../../../api/review';
import { ReviewConfigBtnType } from '../../../components/ReviewConfigModal/components/ReviewConfigBtn/type';
import { svgMap } from '../../../components/ReviewConfigModal/components/ReviewConfigBtn';

interface RateButtonSectionProps {
  question: QADocument;
  taskDetail: TaskDetailType;
  score: string;
  autoSwitch: boolean;
  isConfirm: boolean;
  scoreButtonInfo: ReviewConfigBtnType[];
  onScoreChange: (score: string) => void;
  onRefresh: () => void;
  onReviewingChange: (isReviewing: boolean) => void;
}

const RateButtonSection: React.FC<RateButtonSectionProps> = ({
  question,
  taskDetail,
  score,
  autoSwitch,
  isConfirm,
  scoreButtonInfo,
  onScoreChange,
  onRefresh,
  onReviewingChange,
}) => {
  const [clickCount, setClickCount] = useState<number>(0);
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    console.log(clickCount);
    let timeout: string | number | NodeJS.Timeout | undefined;
    if (clickCount >= 3) {
      setShowWarning(true);

      timeout = setTimeout(() => {
        setShowWarning(false);
        setClickCount(0);
      }, 3000);
    }

    return () => clearTimeout(timeout);
  }, [clickCount]);

  const handleClick = (score: string) => {
    let clickTimeout: string | number | NodeJS.Timeout | undefined;

    if (clickTimeout) {
      clearTimeout(clickTimeout);
    }
    onScoreChange(score);
    setClickCount((preCount) => preCount + 1);

    // score api
    if (autoSwitch) {
      const review: ScoreReviewParams = {
        id: question.id,
        taskId: taskDetail.id,
        score: score,
      };
      scoreReview(review).then((res) => {
        onReviewingChange(true);
        setTimeout(() => {
          const userId = sessionStorage.getItem('id') ?? '';
          const taskId = taskDetail.id;
          queryQaAllocated(taskId, userId).then((res) => {
            onRefresh();
            onScoreChange('');
            onReviewingChange(false);
          });
        }, 500);
      });
      clickTimeout = setTimeout(() => {
        setClickCount(0);
      }, 3000);
    }
  };

  return (
    <>
      <Space direction="vertical" style={{ width: '100%', flex: '1', padding: '0 1rem' }} size={18}>
        <div className="boldText" style={{ color: '#6D7279', fontSize: 16 }}>
          Step 2
        </div>
        <div className="boldText">打分：</div>
        <div style={{ color: '#6D7279' }}>
          评价标准：请将生成数据的字符总长度、语言自然度作为主要指标进行主观打分评价
        </div>
        <Space
          size={4}
          direction="vertical"
          style={{
            textAlign: 'center',
            position: 'relative',
            top: '-18px',
          }}
        >
          <div style={{ height: 22 }}>
            <span
              style={{ color: 'red', display: showWarning ? '' : 'none' }}
              className="shake-text"
            >
              操作过快，请认真审核
            </span>
          </div>
          <Space>
            {scoreButtonInfo?.map((btn, index) => {
              return (
                <Button
                  key={index}
                  disabled={showWarning || !isConfirm}
                  type="primary"
                  style={
                    {
                      width: '120px',
                      background: `${score === btn.value ? btn.color : ''}`,
                      '--btn-hover-color': `${btn.value ? btn.color : 'red'}`,
                    } as React.CSSProperties
                  }
                  className={'review-btn-default'}
                  onClick={() => {
                    handleClick(btn.value);
                  }}
                >
                  <Icon component={svgMap(btn.icon)} />
                  <span className="text-spacing">{btn.value}</span>
                </Button>
              );
            })}
          </Space>
        </Space>
      </Space>
    </>
  );
};

export default RateButtonSection;
