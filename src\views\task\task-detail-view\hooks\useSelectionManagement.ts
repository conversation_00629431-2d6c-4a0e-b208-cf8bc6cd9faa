import { useState, useCallback } from 'react';
import { QADocument } from '../../../../types';
import { QAInfoType } from '../../../../api/qa';

export const useSelectionManagement = () => {
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [checkedQASet, setCheckedQASet] = useState<QAInfoType[]>([]);
  const [checkedFileSet, setCheckedFileSet] = useState<Set<string>>(new Set<string>());
  const [excludeQASet, setExcludeQASet] = useState<QAInfoType[]>([]);
  const [globalSelectedQAIds, setGlobalSelectedQAIds] = useState<Set<string>>(new Set());
  const [globalSelectedQADetails, setGlobalSelectedQADetails] = useState<
    Map<string, { fileId: string; id: string }>
  >(new Map());
  const [currentPageSelectAll, setCurrentPageSelectAll] = useState(false);

  // 重置选择状态
  const resetCheck = useCallback(() => {
    setExcludeQASet([]);
    setCheckedFileSet(new Set<string>());
    setCheckedQASet([]);
    setGlobalSelectedQAIds(new Set());
    setGlobalSelectedQADetails(new Map());
    setCurrentPageSelectAll(false);
  }, []);

  // 处理当前页面全选/取消全选
  const handleCurrentPageSelectAll = useCallback((checked: boolean, displayQaList: QADocument[]) => {
    setCurrentPageSelectAll(checked);

    if (checked) {
      // 全选当前页面的所有QA
      const newGlobalSelected = new Set(globalSelectedQAIds);
      const newGlobalDetails = new Map(globalSelectedQADetails);

      displayQaList.forEach((qa) => {
        newGlobalSelected.add(qa.id);
        newGlobalDetails.set(qa.id, { fileId: qa.fileId, id: qa.id });
      });

      setGlobalSelectedQAIds(newGlobalSelected);
      setGlobalSelectedQADetails(newGlobalDetails);
    } else {
      // 取消选择当前页面的所有QA
      const currentPageQAIds = new Set(displayQaList.map((qa) => qa.id));
      const newGlobalSelected = new Set(globalSelectedQAIds);
      const newGlobalDetails = new Map(globalSelectedQADetails);

      currentPageQAIds.forEach((id) => {
        newGlobalSelected.delete(id);
        newGlobalDetails.delete(id);
      });

      setGlobalSelectedQAIds(newGlobalSelected);
      setGlobalSelectedQADetails(newGlobalDetails);
    }
  }, [globalSelectedQAIds, globalSelectedQADetails]);

  // 检查当前页面是否全选
  const isCurrentPageAllSelected = useCallback((displayQaList: QADocument[]) => {
    if (displayQaList.length === 0) return false;
    return displayQaList.every((qa) => globalSelectedQAIds.has(qa.id));
  }, [globalSelectedQAIds]);

  // 检查当前页面是否部分选中
  const isCurrentPageIndeterminate = useCallback((displayQaList: QADocument[]) => {
    if (displayQaList.length === 0) return false;
    const selectedCount = displayQaList.filter((qa) => globalSelectedQAIds.has(qa.id)).length;
    return selectedCount > 0 && selectedCount < displayQaList.length;
  }, [globalSelectedQAIds]);

  return {
    showCheckbox,
    checkedQASet,
    checkedFileSet,
    excludeQASet,
    globalSelectedQAIds,
    globalSelectedQADetails,
    currentPageSelectAll,
    setShowCheckbox,
    setCheckedQASet,
    setCheckedFileSet,
    setExcludeQASet,
    setGlobalSelectedQAIds,
    setGlobalSelectedQADetails,
    setCurrentPageSelectAll,
    resetCheck,
    handleCurrentPageSelectAll,
    isCurrentPageAllSelected,
    isCurrentPageIndeterminate,
  };
};
