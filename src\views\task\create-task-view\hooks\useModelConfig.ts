import { useState } from 'react';
import { ModelConfigType } from '../types';
import { DEFAULT_VALUES } from '../constants';

/**
 * 模型配置管理Hook
 */
export const useModelConfig = () => {
  const [modelConfig, setModelConfig] = useState<ModelConfigType>(DEFAULT_VALUES.MODEL_CONFIG);
  const [splitLevel, setSplitLevel] = useState(DEFAULT_VALUES.SPLIT_LEVEL);
  const [questionDensityValue, setQuestionDensity] = useState(DEFAULT_VALUES.QUESTION_DENSITY);

  // 模型配置变化处理
  const handleModelConfigChange = (config: ModelConfigType) => {
    setModelConfig(config);
  };

  // 段落精细度变化处理
  const handleSplitLevelChange = (level: number) => {
    setSplitLevel(level);
  };

  // 提问密度变化处理
  const handleQuestionDensityChange = (density: number) => {
    setQuestionDensity(density);
  };

  return {
    modelConfig,
    splitLevel,
    questionDensityValue,
    handleModelConfigChange,
    handleSplitLevelChange,
    handleQuestionDensityChange,
  };
};
