import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import "../css/PageHeader.css";
import { Avatar, Drawer } from "antd";
import whiteLogo from "../assets/img/white-logo.svg";
import UserView from "../views/UserView";
import avatar1 from "../assets/img/avatar-1.png";
const AdminPageHeader: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [userInfo, setUserInfo] = useState<any>();
  const [avatar, setAvatar] = useState(avatar1);
  const navigate = useNavigate();
  const location = useLocation();
  const tabs: string[] = ["/overview", "/main/sourcedata", "/main/task"];
  const [userCompOpen, setUserCompOpen] = useState(false);
  useEffect(() => {
    const userInfoStr = sessionStorage.getItem('userInfo');
    if (userInfoStr) {
      setUserInfo(JSON.parse(userInfoStr));
    }
  }, [])

  useEffect(() => {
    if (userInfo) {
      setAvatar(userInfo.avatar);
    }
  }, [userInfo])
  useEffect(() => {
    // 在路由变化时执行的操作
    console.log("路由已变化:", location.pathname);
    tabs.forEach((tab, index) => {
      // if (tab === location.pathname) {
      //   setActiveTab(index + 1);
      // }
      if (location.pathname.indexOf(tab) > -1) {
        setActiveTab(index + 1);
      }
    });
  }, [location]);
  return (
    <div
      className={
        location.pathname === "/overview"
          ? "hello-parent14 headerFirst"
          : "hello-parent14 headerDefault"
      }
    >
      <img src={whiteLogo} className="logo37" />
      <div className="everreachai-pollux20 enText">Pollux 产品后台</div>
      <div className="parent131">
        <div
          className={activeTab === 1 ? "div691 active-tab" : "div691"}
          onClick={() => {
            if (userCompOpen) {
              setUserCompOpen(false)
            }
            setActiveTab(1);
            navigate("/admin/dashboard");
          }}
        >
          数据看板
        </div>
        <div
          className={activeTab === 2 ? "div691 active-tab" : "div691"}
          onClick={() => {
            if (userCompOpen) {
              setUserCompOpen(false)
            }
            setActiveTab(2);
            navigate("/admin/feedback");
          }}
        >
          用户反馈
        </div>
      </div>
      <b className="hello16">Hello,</b>
      <Avatar className="ellipse-parent29"
        src={avatar} />
    </div>
  );
};

export default AdminPageHeader;
