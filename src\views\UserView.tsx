import { <PERSON><PERSON>, But<PERSON>, Col, Input, Row, Space } from "antd";
import { useEffect, useState } from "react";
import AccountComponent from "../components/AccountComponent";
import AgreementComponent from "../components/AgreementComponent";
import FeedbackComponent from "../components/FeedbackComponent";
import UserInfoComponent from "../components/UserInfoComponent";
import "../css/UserView.css";
import { CheckOutlined, EditOutlined } from '@ant-design/icons';
import FeedbackModal from "../components/FeedbackModal";
import EditAvatarModal from "../components/EditAvatarModal";
import vector6 from "../assets/img/vector-6.svg";
import { DownOutlined } from '@ant-design/icons';
import bxsrightarrowalt from "../assets/img/bxsrightarrowalt.svg";
import avatar1 from "../assets/img/avatar-1.png";

interface UserViewProp {
  OnClose: () => void
}
const UserView: React.FC<UserViewProp> = ({ OnClose }) => {
  const [userName, setUserName] = useState("ElioHan");
  const [phoneNum, setPhoneNum] = useState("130 4567 8910");
  const [job, setJob] = useState("学生");
  const [avatar, setAvatar] = useState(avatar1);

  const [editUserName, setEditUserName] = useState(false);
  const [editPhoneNum, setEditPhoneNum] = useState(false);
  const [editJob, setEditJob] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  // const userInfo = useSelector((state: State) => state.auth.userInfo);
  const [userInfo, setUserInfo] = useState<any>();
  useEffect(() => {
    const userInfoStr = sessionStorage.getItem('userInfo');
    if (userInfoStr) {
      setUserInfo(JSON.parse(userInfoStr));
    }
  }, [])

  useEffect(() => {
    if (userInfo) {

      setUserName(userInfo.name);
      setPhoneNum(userInfo.phone);
      setJob(userInfo.job);
      setAvatar(userInfo.avatar);

    }
  }, [userInfo])

  return (
    <>
      <div className="div23 mediumText">
        <Button
          shape="circle"
          icon={<DownOutlined
            style={{ fontSize: "14px" }}
          />}
          onClick={() => OnClose()}
        />个人中心</div>
      <div className="group-div">
        <UserInfoComponent>

          <div className="b6 boldText">基本信息</div>
          <div className="ellipse-parent6">
            <Avatar size={88} src={avatar} />
            <Button shape="circle" className="avatar-edit-btn" onClick={() => setShowAvatarModal(true)}><EditOutlined /></Button>
          </div>
          <div className="info-div">
            <div className="info-item-div" style={editUserName ? {} : { paddingLeft: "0.75rem" }}>
              {
                editUserName ?
                  <Input allowClear value={userName} onChange={e => setUserName(e.target.value)} onBlur={() => setEditUserName(false)} suffix={
                    <Button type="text" onClick={() => {
                      //TODO 更新用戶信息
                    }}><CheckOutlined /></Button>
                  } />
                  : <><span className="info-username boldText">{userName}</span> <Button className="div18" type="link" onClick={() => setEditUserName(true)}>修改</Button></>
              }
            </div>

            <div className="info-item-div" style={editPhoneNum ? {} : { paddingLeft: "0.75rem" }}>
              {
                editPhoneNum ?
                  <Input allowClear value={phoneNum} onChange={e => setPhoneNum(e.target.value)} onBlur={() => setEditPhoneNum(false)} suffix={
                    <Space.Compact>
                      <Button type="text" onClick={() => {
                        //TODO 更新用戶信息
                      }}><CheckOutlined /></Button>
                    </Space.Compact>}
                  />
                  : <><span>{phoneNum}</span>
                    <Button className="div19" type="link" onClick={() => setEditPhoneNum(!editPhoneNum)}>更改绑定</Button></>
              }
            </div>
            <div className="info-item-div" style={editJob ? {} : { paddingLeft: "0.75rem" }}>
              {
                editJob ?
                  <Input allowClear value={job} onChange={e => setJob(e.target.value)} onBlur={() => setEditJob(false)} suffix={
                    <Space.Compact>
                      <Button type="text" onClick={() => {
                        //TODO 更新用戶信息
                      }}><CheckOutlined /></Button>
                    </Space.Compact>}
                  />
                  : <><span>{job}</span>
                    <Button className="div18" type="link" onClick={() => setEditJob(!editJob)}>修改</Button></>
              }
            </div>
          </div>
        </UserInfoComponent>
      </div>
      <Row
        gutter={24}
        style={{
          position: 'relative',
          top: '28rem',
          left: '13rem',
          width: '91.5rem',
        }}
      >
        <Col span={12} style={{
          height: '355px',
          borderRadius: '24px',
          background: 'linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 0.40) 100%)',
          boxShadow: '0px 4px 4px 0px rgba(119, 146, 185, 0.10)'
        }}>
          <div>
            2
          </div>
        </Col>
        <Col span={12}>
          3
        </Col>
      </Row>
      {/*
          <AgreementComponent />
        <div className="rectangle-parent1" onClick={() => setShowModal(true)}>
          <FeedbackComponent />
          <div className="group-child4" />
          <div className="group-child5" />
          <div className="div22">我要反馈</div>
          <img className="group-child6" alt="" src={vector6} />
          <b className="b7 boldText">意见反馈</b>
        </div>
        <img
          className="bxs-right-arrow-alt-1-icon1"
          alt=""
          src={bxsrightarrowalt}
        /> */}
      <FeedbackModal visible={showModal} OnClose={() => setShowModal(false)} />
      <EditAvatarModal visible={showAvatarModal} OnClose={() => setShowAvatarModal(false)} userName={userName} />
    </>
  );
}

export default UserView;