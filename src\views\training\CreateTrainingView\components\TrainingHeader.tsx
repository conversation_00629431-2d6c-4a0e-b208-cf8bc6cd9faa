import React, { CSSProperties } from 'react';
import { Button, Space, Steps } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import classes from '../index.module.css';

interface TrainingHeaderProps {
  isSticky: boolean;
  showResult: boolean;
  scrollBasedStepIndex: number;
  onStepClick: (index: number) => void;
}

const TrainingHeader: React.FC<TrainingHeaderProps> = ({
  isSticky,
  showResult,
  scrollBasedStepIndex,
  onStepClick,
}) => {
  const navigate = useNavigate();

  const customProgressDot = (
    _dot: React.ReactNode,
    { index }: { status: string; index: number }
  ) => {
    let style: CSSProperties = {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      boxSizing: 'border-box',
      backgroundColor: '#7792B9',
    };

    // 当前步骤：空心圆（白色填充，绿色边框）
    if (index === scrollBasedStepIndex || index < scrollBasedStepIndex) {
      style = {
        ...style,
        width: '16px',
        height: '16px',
        backgroundColor: 'white',
        border: '4px solid #0FB698',
        marginInlineStart: '-3px',
        top: '-3px',
        position: 'relative',
      };
    }

    return <div style={style} />;
  };

  const stepItems = [
    { title: '模型选择' },
    { title: '数据配置' },
    { title: '训练配置' },
    { title: '资源配置' },
  ];

  return (
    <div
      className={classes['training-header']}
      style={{
        position: 'sticky',
        top: 0,
        background: isSticky ? 'white' : 'transparent',
        zIndex: 1000,
        height: isSticky ? '88px' : '44px',
        paddingTop: isSticky ? '44px' : '0',
        boxShadow: isSticky ? '0px 4px 4px 0px #7792B91A' : 'none',
        transition: 'height 0.08s linear, padding-top 0.08s linear, background-color 0s linear',
      }}
    >
      <Space size={20} style={{ display: 'inline-flex', alignItems: 'center' }}>
        <Button
          style={{ fontSize: '12px', width: '36px', height: '36px' }}
          shape="circle"
          icon={<LeftOutlined />}
          onClick={() => {
            navigate('/main/finetune');
            localStorage.removeItem('trainingData');
          }}
        />
        <div
          className="mediumText"
          style={{
            fontSize: '28px',
            lineHeight: '36px',
            fontWeight: '500',
          }}
        >
          微调训练
        </div>
      </Space>
      <div className={classes['step-div']}>
        {!showResult ? (
          <Steps
            progressDot={customProgressDot}
            size="small"
            current={scrollBasedStepIndex}
            onChange={onStepClick}
            items={stepItems}
            style={{ height: '4px' }}
          />
        ) : null}
      </div>
    </div>
  );
};

export default TrainingHeader;
