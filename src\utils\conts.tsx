export const questionDensity = [
  // '1-2个句子分段',
  // '3-5个句子分段',
  // '6-10个句子分段',
  // '11-20个句子分段',
  // '20+个句子分段',
  "笼统提问",
  "较笼统提问",
  "正常提问",
  "较集中提问",
  "集中提问",
];
export const paragraph = [
  // '10个问题/段',
  // '8个问题/段',
  // '6个问题/段',
  // '4个问题/段',
  // '2个问题/段',
  "最大长度分段",
  "较大长度分段",
  "中等长度分段",
  "较小长度分段",
  "最小长度分段",
];

export const temperature = [
  "一致性高",
  "略增多样性",
  "平衡稳定",
  "创新性强",
  "极致创新",
];

export const topk = ["严格限制", "较严格", "平衡选择", "宽松选择", "极度宽松"];

export const topp = [
  "保守选择",
  "略增多样性",
  "平衡多样",
  "多样性高",
  "极高多样性",
];
export const maxlenght = [
  "简洁回答",
  "中等详细",
  "较详细",
  "详尽回答",
  "极度详尽",
];
export const repeatition = [
  "允许重复",
  "轻微抑制重复",
  "中等抑制",
  "强力抑制",
  "极端抑制",
];

export const trainingSet = [
  "快速训练，适合初步模型探索",
  "速度优先，适度性能考虑",
  "平衡选择，兼顾效率与效果",
  "性能优先，适应深入分析需求",
  "深度训练，追求高精度结果",
];

export const split = ["0.0", "0.25", "0.5", "0.75", "1.0"];
export const topkInfo = ["5", "10", "20", "50", "100"];
export const toppInfo = ["0.6", "0.7", "0.8", "0.9", "0.95"];
export const maxInfo = ["400", "800", "1200", "1600", "2000"];
export const repeatitionInfo = ["1.0", "1.2", "1.5", "1.8", "2.0"];
export const iteratInfo = ["1", "2", "3", "4", "5"];
export const learnInfo = ["1e-5", "5e-5", "1e-4", "5e-4", "1e-3"];
export const batchInfo = ["1", "2", "4", "8", "16"];
export const testSetRatio = ["10%", "20%", "30%", "40%", "50%"];
export const performance = [
  "并发优先",
  "并发优先",
  "速度优先",
  "速度优先",
  "速度优先",
];
