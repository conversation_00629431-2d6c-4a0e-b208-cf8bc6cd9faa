import { Layout, Space } from "antd";
import AdminPageHeader from "../../components/AdminPageHeader";
import { Outlet } from "react-router-dom";

const { Content } = Layout;
const contentStyle: React.CSSProperties = {
    textAlign: "center",
    minHeight: "calc(100vh - 52px)",
    color: "#000",
    backgroundColor: "#fff",
};

const AdminView: React.FC = () => {
    return <>
        <Space direction="vertical" style={{ width: "100%" }} size={[0, 48]}>
            <Layout className="pageLayout">
                <AdminPageHeader></AdminPageHeader>
                <Content style={contentStyle}>
                    <div className="main-content">
                        <Outlet />
                    </div>
                </Content>
            </Layout>
        </Space></>
}

export default AdminView;