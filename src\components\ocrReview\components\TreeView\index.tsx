import { DoubleLeftOutlined } from '@ant-design/icons';
import { Button, Tree } from 'antd';
import React, { useEffect, useState } from 'react';
import DirectoryTree from 'antd/lib/tree/DirectoryTree';
import { DataNode } from 'antd/lib/tree';
import Scrollbars from 'react-custom-scrollbars';
import './index.module.css';
import { DisplayOCRChapters, OCRChapters } from '../ResultView/type';

interface TreeViewProp {
  chapters: DisplayOCRChapters[];
}

const TreeView: React.FC<TreeViewProp> = ({ chapters }) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['0-0-0', '0-0-1']);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>(['0-0-0']);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  useEffect(() => {
    setTreeData(chapters as any[]);
  }, [chapters]);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    console.log('onExpand', expandedKeysValue);
    // if not set autoExpandParent to false, if children expanded, parent can not collapse.
    // or, you can remove all expanded children keys.
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onCheck = (checkedKeysValue: React.Key[]) => {
    console.log('onCheck', checkedKeysValue);
    setCheckedKeys(checkedKeysValue);
  };

  const onSelect = (selectedKeysValue: React.Key[], info: any) => {
    console.log('onSelect', info);
    setSelectedKeys(selectedKeysValue);
  };
  return (
    <>
      <div className={'review-title-lf'}>
        层级视图
        <Button type="text" size="small">
          <DoubleLeftOutlined />
        </Button>
      </div>
      <Scrollbars>
      <DirectoryTree
        defaultExpandAll
        showIcon={false}
        expandAction={false}
        fieldNames={{ title: 'text', key: 'displayId' }}
        onSelect={onSelect}
        onExpand={onExpand}
        treeData={treeData}
        titleRender={(nodeData) => {
          const data = nodeData as any;
          return (
            <a style={{ color: 'black' }} href={`#${data.displayId}`}>
              {data.text}
            </a>
          );
        }}
      />
      </Scrollbars>
    </>
  );
};

export default TreeView;
