import { Dispatch, useState } from "react";
import { useDispatch } from "react-redux";
import { Button, Col, Form, Input, Row } from "antd";
import { useNavigate, useSearchParams } from "react-router-dom";
import SendMessageBtn from "./SendMessageBtn";
import { verifyLogin } from "../store/auth/action";
import { VerifyCodeTypeEnum } from "../types";
interface LoginMessageBoxProps {
  isAgree: boolean;
}

type LoginFieldType = {
  account?: string;
  verifyCode?: string;
};

const LoginMessageBox: React.FC<LoginMessageBoxProps> = ({ isAgree }) => {
  const [form] = Form.useForm();
  const [account, setAccount] = useState("");
  const [accountValidation, setAccountValidataion] = useState(false);
  const [verifyCode, setVerifyCode] = useState("");
  // 定义dispatch 工具（发送action动作执行reducer）
  const dispath: Dispatch<any> = useDispatch();
  // 获取导航navigate
  const navigate = useNavigate();
  // 获取查询参数
  const [search] = useSearchParams();
  //获取到查询参数redirect 也就是 http://localhost:3000/#?redirect=/admin/abc
  // 上面地址的 /admin/abc
  const path = search.getAll("redirect")[0];
  const redirect = !path || path === '/login' || path === '/register' || path === '/' ? "/overview" : path;
  // 定义回调方法
  const callback = () => {
    navigate(redirect);
  };

  // 验证成功
  const onFinish = (values: any) => {
    const { account, verificationCode } = values;
    // 执行dispath action传入用户名和密码
    const loginType = "PHONE_NUMBER";
    
    dispath(verifyLogin({ account, verificationCode, loginType }, callback));
  };

  // 验证失败
  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };
  return (
    <Form
      name="loginMessage"
      // style={{ maxWidth: 600 }}
      initialValues={{
        remember: true,
        username: "",
        message: "",
      }}
      form={form}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
    >
      <Form.Item<LoginFieldType>
        name="account"
        rules={[
          { required: true, message: "请输入手机号" },
          {
            pattern: /^1[3456789]\d{9}$/,
            message: "手机号格式不正确",
          },
        ]}
      >
        <Input
          className="loginInput"
          placeholder="请输入手机号"
          value={account}
          onChange={(e) => {
            setAccount(e.target.value);
            setAccountValidataion(form.getFieldError('account').length > 0);
          }}
        />
      </Form.Item>

      <Row justify="start" gutter={8}>
        <Col span={16}>
          <Form.Item<LoginFieldType>
            validateTrigger="onSubmit"
            name="verifyCode"
            rules={[
              { required: true, message: "短信验证码" },
              {
                validator: (_, value) =>
                  isAgree
                    ? Promise.resolve()
                    : Promise.reject(new Error("请阅读并同意协议")),
              },
            ]}
          >
            <Input
              className="loginVerifyCodeInput"
              placeholder="短信验证码"
              value={verifyCode}
              onChange={(e) => setVerifyCode(e.target.value)}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          {form.getFieldError('account')}
          <SendMessageBtn
            disabled={!account || account?.length === 0 || accountValidation}
            params={{ account, loginType: "PHONE_NUMBER" }}
            type={VerifyCodeTypeEnum.Login}
            onClick={() => {
              form.validateFields(['account'])
            }}
          ></SendMessageBtn>
        </Col>
      </Row>
      <Form.Item style={{ paddingTop: "1.5rem" }}>
        <Button
          type="primary"
          shape="round"
          block
          htmlType="submit"
          className="verifyloginBtn loginBtn boldText"
        >
          登录/注册
        </Button>
      </Form.Item>
    </Form>
  );
};

export default LoginMessageBox;
