import React from 'react';
import { Button, Dropdown, Input, Select, Space, Checkbox } from 'antd';
import {
  SearchOutlined,
  CaretDownOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

interface SearchFilterSectionProps {
  filterType: string;
  selectText: string;
  selectReview: string;
  filterVal?: string;
  searchTagValue: string;
  tagsVisible: boolean;
  tagList: any[];
  showCheckbox: boolean;
  isCurrentPageAllSelected: boolean;
  isCurrentPageIndeterminate: boolean;
  filterItems: MenuProps['items'];
  onFilterChange: MenuProps['onClick'];
  onFilterValChange: (value: string) => void;
  onSearchTagValueChange: (value: string) => void;
  onSelectReviewChange: (value: string) => void;
  onTagsVisibleToggle: () => void;
  onCurrentPageSelectAllChange: (checked: boolean) => void;
  onOpenChange: (open: boolean) => void;
  onInitSelect: () => void;
}

const SearchFilterSection: React.FC<SearchFilterSectionProps> = ({
  filterType,
  selectText,
  selectReview,
  filterVal,
  searchTagValue,
  tagsVisible,
  tagList,
  showCheckbox,
  isCurrentPageAllSelected,
  isCurrentPageIndeterminate,
  filterItems,
  onFilterChange,
  onFilterValChange,
  onSearchTagValueChange,
  onSelectReviewChange,
  onTagsVisibleToggle,
  onCurrentPageSelectAllChange,
  onOpenChange,
  onInitSelect,
}) => {
  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      {showCheckbox && (
        <Checkbox
          checked={isCurrentPageAllSelected}
          indeterminate={isCurrentPageIndeterminate}
          onChange={(e) => onCurrentPageSelectAllChange(e.target.checked)}
        />
      )}

      <div className="task-title mediumText">问答对</div>

      <Button
        className="default-btn"
        style={{
          width: '106px',
          height: '40px',
          fontSize: '14px',
          justifyContent: 'center',
          gap: '6px',
        }}
        onClick={onTagsVisibleToggle}
      >
        标签显现
        {tagsVisible ? (
          <EyeOutlined style={{ color: '#000000', fontSize: '20px' }} />
        ) : (
          <EyeInvisibleOutlined style={{ color: '#666', opacity: 0.3, fontSize: '20px' }} />
        )}
      </Button>

      <div className="filter-container">
        <Dropdown
          menu={{
            items: filterItems,
            onClick: onFilterChange,
          }}
          popupRender={(menu) => (
            <>
              {React.cloneElement(menu as React.ReactElement, {
                onMouseEnter: (e: any) => {
                  // 当hover到审核检索项时，显示子下拉菜单
                  const target = e.target.closest('[data-menu-id*="reviewedSearch"]');
                  if (target && !target.dataset.menuId?.includes('submenu')) {
                    // setFilterType('reviewedSearch');
                  }
                },
              })}
            </>
          )}
        >
          <Button
            className="default-btn"
            style={{
              width: 112,
              height: '40px',
              fontSize: '14px',
              justifyContent: 'start',
              position: 'relative',
            }}
          >
            {selectText}
            <CaretDownOutlined style={{ position: 'absolute', right: 10 }} />
          </Button>
        </Dropdown>

        {filterType === 'keyWord' && (
          <Input
            size="large"
            suffix={<SearchOutlined />}
            className="default-input"
            placeholder="请输入关键词"
            style={{ width: 320 }}
            value={filterVal}
            onChange={(e) => onFilterValChange(e.target.value)}
          />
        )}

        {filterType === 'labelSearch' && (
          <Select
            mode="multiple"
            style={{ width: 320 }}
            placeholder="请选择标签"
            suffixIcon={<SearchOutlined />}
            allowClear
            options={tagList
              .filter((tagItem) =>
                searchTagValue
                  ? tagItem.tag.toLowerCase().includes(searchTagValue.toLowerCase())
                  : true
              )
              .map((tagItem) => {
                const tagText = `${tagItem.tag}`;
                return { label: tagText, value: tagText };
              })}
            dropdownRender={(menu: any) => (
              <>
                <Space style={{ padding: '0 8px 4px' }}>
                  <Input
                    placeholder="请输入"
                    style={{ width: 300 }}
                    size="large"
                    suffix={<SearchOutlined />}
                    allowClear
                    value={searchTagValue}
                    onChange={(e) => onSearchTagValueChange(e.target.value)}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                </Space>
                {menu}
              </>
            )}
          />
        )}

        {filterType === 'reviewedSearch' && (
          <Input
            placeholder="请选择审核检索项"
            style={{ width: 320 }}
            size="large"
            prefix={<SearchOutlined />}
            allowClear
            readOnly
            onClick={() => onOpenChange(true)}
            value={selectReview}
            onChange={(e) => {
              onSelectReviewChange(e.target.value);
              onInitSelect();
            }}
            suffix={
              selectReview ? (
                <CloseCircleOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectReviewChange('');
                    onInitSelect();
                  }}
                />
              ) : (
                ''
              )
            }
            onKeyDown={(e) => e.stopPropagation()}
          />
        )}
      </div>
    </div>
  );
};

export default SearchFilterSection;
