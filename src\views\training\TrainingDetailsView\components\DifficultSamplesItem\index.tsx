import { Space } from "antd";
import q from "../../../../../assets/img/training-q.svg";
import a from "../../../../../assets/img/training-a.svg";
import t from "../../../../../assets/img/training-t.svg";
import classes from './index.module.css';

interface DifficultSamplesItemProp {
    question: string;
    text: string;
    answer: string;
    score: number;
}

const DifficultSamplesItem: React.FC<DifficultSamplesItemProp> = ({ question, text, answer, score }) => {
    return <>
        <Space direction='vertical' size={19}>
            <div className={classes['sample-div']}>
                <img src={q} alt="qusestion" className={classes['sample-img']} />
                {question}
            </div>
            <div className={classes['sample-div-text']}>
                <img src={t} alt="text" className={classes['sample-img']} />
                {text}
            </div>
            <div className={classes['sample-div']}>
                <img src={a} alt="answer" className={classes['sample-img']} />
                {answer}
            </div>
            <div className={classes['sample-div-score']}>
                得分： {score}
            </div>
        </Space>
    </>
}
export default DifficultSamplesItem;