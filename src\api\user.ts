import { AxiosResponse } from "axios";
import request from "../utils/request";
import { CustomResponseType } from "../types";

export interface UserType {
    "id": string;
    "userName": string;
    "userAccount": string;
    "userAvatar": string;
    "gender": string;
    "userRole": string;
    "userPassword": string;
    "createTime": number[];
    "updateTime": number[];
    "isDelete": number;
}

export interface GetAllUserResp extends CustomResponseType {
    data: UserType[];
}

/**
 * 获取用户列表
 * @returns
 */
export function getAllUser(): Promise<AxiosResponse<GetAllUserResp>> {
    return request.get("/user/all");
}
