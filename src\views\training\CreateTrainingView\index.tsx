import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import { TariningType } from './type';
import TrainingResult from './components/TrainingResult';
import ResultView from './components/resultView';
import TrainingHeader from './components/TrainingHeader';
import TrainingContent from './components/TrainingContent';
import { useStepNavigation } from './hooks/useStepNavigation';
import { useFormValidation } from './hooks/useFormValidation';
import { useTrainingData } from './hooks/useTrainingData';
import classes from './index.module.css';
const CreateTrainingView: React.FC = () => {
  const navigate = useNavigate();
  const [selectedModelId, setSelectedModelId] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [showTraining, setShowTraining] = useState(false);
  const [isOnline, setIsOnline] = useState('');

  // Refs for form components
  const modelSelectRef = useRef<any>(null);
  const taskSelectRef = useRef<any>(null);
  const trainingSetSelectRef = useRef<any>(null);
  const resourceSetSelectRef = useRef<any>(null);

  // Refs for sections
  const modelSectionRef = useRef<HTMLDivElement>(null);
  const taskSectionRef = useRef<HTMLDivElement>(null);
  const trainingSetSectionRef = useRef<HTMLDivElement>(null);
  const overviewSectionRef = useRef<HTMLDivElement>(null);
  const sectionRefs = [modelSectionRef, taskSectionRef, trainingSetSectionRef, overviewSectionRef];

  // Custom hooks
  const { scrollBasedStepIndex, isSticky, handleStepClick, scrollToStep, setIsSticky } =
    useStepNavigation({
      sectionRefs,
      showResult,
      showTraining,
    });

  const { validateAllSteps } = useFormValidation({
    modelSelectRef,
    taskSelectRef,
    trainingSetSelectRef,
    resourceSetSelectRef,
  });

  const { trainingData, collectAllTrainingData, handleTrainingSubmit } = useTrainingData({
    modelSelectRef,
    taskSelectRef,
    trainingSetSelectRef,
    resourceSetSelectRef,
  });

  useEffect(() => {
    const online: any = localStorage.getItem('isOnline');
    setIsOnline(online);
  }, [localStorage.getItem('isOnline')]);

  const handleModelSelectChange = (modelId: string) => {
    setSelectedModelId(modelId);
  };

  const handleNextBtnClick = async () => {
    const validationResult = await validateAllSteps();
    if (!validationResult.isValid) {
      message.error('请将信息填写完整');
      if (validationResult.firstInvalidStepIndex !== undefined) {
        scrollToStep(validationResult.firstInvalidStepIndex);
      }
      return;
    }
    collectAllTrainingData();
    setShowResult(true);
    setIsSticky(false);
  };

  return (
    <>
      <div style={{ height: '90vh' }}>
        <div className="createTaskContent" style={{ height: '100%' }}>
          <TrainingHeader
            isSticky={isSticky}
            showResult={showResult}
            scrollBasedStepIndex={scrollBasedStepIndex}
            onStepClick={handleStepClick}
          />

          {!showResult ? (
            <TrainingContent
              trainingData={trainingData}
              selectedModelId={selectedModelId}
              modelSelectRef={modelSelectRef}
              taskSelectRef={taskSelectRef}
              trainingSetSelectRef={trainingSetSelectRef}
              resourceSetSelectRef={resourceSetSelectRef}
              modelSectionRef={modelSectionRef}
              taskSectionRef={taskSectionRef}
              trainingSetSectionRef={trainingSetSectionRef}
              overviewSectionRef={overviewSectionRef}
              onModelSelectChange={handleModelSelectChange}
              onNextClick={handleNextBtnClick}
            />
          ) : (
            <div className="createTaskArea" style={{ overflowY: 'auto', height: '100%' }}>
              {showTraining ? (
                <TrainingResult />
              ) : (
                <div style={{ marginTop: '10px' }}>
                  <ResultView trainingData={trainingData} />
                </div>
              )}
              {!showTraining && (
                <div className={classes['pre-next-div']}>
                  <button
                    type="button"
                    className={`primary-btn ${classes['next-btn']}`}
                    onClick={() => handleTrainingSubmit(setShowTraining)}
                  >
                    开始训练
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};
export default CreateTrainingView;
