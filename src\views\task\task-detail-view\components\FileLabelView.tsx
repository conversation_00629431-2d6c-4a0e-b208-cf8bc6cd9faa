import { getTaglList, QAInfoType } from '@/api/qa';
import { flattenFileTree, getQATaskFileTree } from '@/api/task';
import { CheckOutlined } from '@ant-design/icons';
import { Collapse, CollapseProps, message, Segmented, Tree, TreeProps } from 'antd';
import { DataNode } from 'antd/es/tree';
import { useEffect, useState } from 'react';

interface FileLabelViewProps {
  taskId?: string;
  showCheckbox?: boolean;
  // 通过props传入的数据
  fileTree: any[];
  flattenTree: any[];
  treeData?: any[];
  tagList?: [];
  // 回调函数
  onFileListChange?: (files: string[]) => void;
  onTagsChange?: (tags: Set<string>) => void;
  onCheckedFileSet?: (files: Set<string>) => void;
  onExcludeQAChange?: (qa: QAInfoType[]) => void;
}

const FileLabelView: React.FC<FileLabelViewProps> = ({
  taskId,
  showCheckbox,
  onFileListChange,
  onTagsChange,
  onCheckedFileSet,
  onExcludeQAChange,
  // 通过props传入的数据
  fileTree,
  flattenTree,
  treeData,
  tagList,
}) => {
  const [segmented, setSegmented] = useState<string>('文件视图');
  const [treeCheckKeys, setTreeCheckKeys] = useState<React.Key[]>([]);
  const [fileList, setFileList] = useState<string[]>([]);
  // 点击选中文件树文件ID数组
  const [checkedFileSet, setCheckedFileSet] = useState<Set<string>>(new Set<string>());
  // 选中文件树后，点击QA checkbox 反选的QA对象数组
  const [excludeQASet, setExcludeQASet] = useState<QAInfoType[]>([]);
  // 点击QA checkbox 选中的QA对象数组
  const [checkedQASet, setCheckedQASet] = useState<QAInfoType[]>([]);
  const [activeKey, setActiveKey] = useState<string[] | string>('1');
  //标签选中状态管理
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());

  const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
    if (info.selected) {
      if (!info.node.children || info.node.children.length === 0) {
        // 选中子节点
        const docuList = flattenTree.filter((qa) => qa.fileId === info.node.key);
        setFileList(docuList.map((item) => item.fileId));
      } else {
        // 选中父节点
        const children = fileTree.filter(
          (item) => item.fileTreeNodeForTask.fileId === info.node.key
        );
        if (children && children.length > 0)
          setFileList(
            children[0].fileTreeNodeForTask.children.map((item: { fileId: any }) => item.fileId)
          );
      }
    } else {
      setFileList([]);
    }
  };
  function findFileIds(node: any): any[] {
    let fileIds: any[] = [];
    if (node.children) {
      node.children.forEach((item: { fileId: any; children: any[] }) => {
        fileIds.push(item.fileId);
        if (item.children) {
          fileIds = fileIds.concat(findFileIds(item));
        }
      });
    }
    return fileIds;
  }
  // 处理标签点击
  const handleTagClick = (tag: string) => {
    const newSelectedTags = new Set(selectedTags);
    if (newSelectedTags.has(tag)) {
      newSelectedTags.delete(tag);
    } else {
      newSelectedTags.add(tag);
    }
    setSelectedTags(newSelectedTags);
  };
  const onCheck: TreeProps['onCheck'] = (checkedKeys, info) => {
    setTreeCheckKeys(checkedKeys as any);
    if (!info.node.children || info.node.children.length === 0) {
      // 选中子节点
      const node = flattenTree.filter((qa) => qa.fileId === info.node.key);
      // if (isChecked(info.node.key)) {
      if (!info.checked) {
        // 已选中时执行取消选中操作
        setCheckedFileSet(new Set(null));
      } else {
        // 未选中时执行选中操作
        setCheckedFileSet(new Set(node.map((item) => item.fileId)));
        setExcludeQASet([]);
        setCheckedQASet([]);
        if (checkedQASet.length !== 0) {
          setCheckedFileSet(new Set(node.map((item) => item.fileId)));
        }
      }
    } else {
      // 选中父节点
      const children = fileTree.filter((item) => item.fileTreeNodeForTask.fileId === info.node.key);
      const childrendata = fileTree.map((item) => item.fileTreeNodeForTask.children[0]);
      const child = childrendata.map((item) => item.children);
      if (Array.isArray(checkedKeys)) {
        // 如果checkedKeys是数组类型
        if (checkedKeys.length === 0) {
          // 取消选中操作
          setCheckedFileSet(new Set(null));
          setExcludeQASet([]);
          setCheckedQASet([]);
        } else if (children && children.length > 0) {
          const allFileIds = findFileIds(children[0].fileTreeNodeForTask);
          // setCheckedFileSet(new Set(children[0].fileTreeNodeForTask.children.map((item: { fileId: any; }) => item.fileId)));
          setCheckedFileSet(new Set(allFileIds));
        } else {
          setCheckedFileSet(new Set(child[0].map((item: { fileId: any }) => item.fileId)));
        }
      }
    }
  };

  const collapseItems: CollapseProps['items'] = [
    {
      key: '1',
      label: <></>,
      showArrow: false,
      children: (
        <>
          <Tree
            defaultExpandAll
            autoExpandParent={true}
            checkedKeys={treeCheckKeys}
            checkable={showCheckbox}
            onSelect={onSelect}
            onCheck={onCheck}
            treeData={treeData}
            fieldNames={{
              title: 'name',
              key: 'fileId',
              children: 'children',
            }}
            className="tree-file"
            titleRender={(nodeData: any) => {
              const { name, qaCount, reviewedCount } = nodeData as any;
              return (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    whiteSpace: 'nowrap',
                  }}
                >
                  <span
                    style={{
                      flex: 4,
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                      overflow: 'hidden',
                      maxWidth: '125px',
                      width: '125px',
                    }}
                  >
                    {name}
                  </span>
                  <label style={{ color: '#6D7279', flex: 1 }}>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {'已审核: ' + reviewedCount + ' / ' + qaCount}
                  </label>
                </div>
              );
            }}
          />
        </>
      ),
    },
  ];

  function useOnChange<T>(value: T, onChange?: (value: T) => void): void {
    useEffect(() => {
      if (onChange) {
        onChange(value);
      }
    }, [value, onChange]);
  }

  useOnChange(fileList, onFileListChange);
  useOnChange(selectedTags, onTagsChange);
  useOnChange(checkedFileSet, onCheckedFileSet);
  useOnChange(excludeQASet, onExcludeQAChange);

  return (
    <div
      className="taskDetailLf"
      style={{
        borderBottom: '1px solid #efefef',
        height: '27rem',
      }}
    >
      <Segmented
        options={['文件视图', '标签视图']}
        size="large"
        onChange={(value) => {
          setSegmented(value as string);
        }}
        className="taskContent-segmented"
      />
      {segmented === '文件视图' && (
        <Collapse
          ghost={true}
          bordered={false}
          expandIconPosition="end"
          size="small"
          destroyOnHidden={true}
          items={collapseItems}
          activeKey={activeKey}
          onChange={(e) => {
            setActiveKey(e);
          }}
          className="taskContent-collapse"
        />
      )}
      {segmented === '标签视图' && (
        <div style={{ padding: '20px 0' }}>
          {tagList && tagList.length > 0 ? (
            <div className="tag-container">
              {tagList.map((tagItem: { tag: string; count: number }, index) => {
                const tagText = `${tagItem.tag}`;
                const isSelected = selectedTags.has(tagText);

                return (
                  <div
                    key={index}
                    onClick={() => handleTagClick(tagText)}
                    className={isSelected ? 'tag-selected' : 'tag-item'}
                  >
                    <span>{tagText + `(${tagItem.count})`}</span>
                    {isSelected && (
                      <CheckOutlined
                        style={{
                          color: '#0FB698',
                          fontSize: '12px',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div
              style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px 0',
                fontSize: '14px',
              }}
            >
              暂无标签数据
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FileLabelView;
