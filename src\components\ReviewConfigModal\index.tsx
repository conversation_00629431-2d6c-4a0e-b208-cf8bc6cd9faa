import {
  Avatar,
  Button,
  Input,
  Modal,
  Space,
  Switch,
  Tag,
  message,
} from "antd";
import { TaskType } from "../../types";
import classes from "./index.module.css";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import ReviewConfigBtn from "./components/ReviewConfigBtn";
import {
  getReviewConfigInfo,
  getTaskIsSetReviewConfig,
  setReviewConfigInfo,
} from "../../api/task";
import {
  ReviewConfigBtnType,
  TaskConfigInfo,
} from "./components/ReviewConfigBtn/type";
import VisableRangeSelect from "../VisableRangeSelect";
import { UserType } from "../../api/user";
import { getAllUserByTaskId } from "../../api/review";
import { getAllocateQA } from "../../api/qa";
import { useNavigate } from "react-router-dom";
interface ReviewConfigModalProp {
  taskData?: TaskType;
  visible: boolean;
  OnClose: () => void;
 
}

export const DefaultBtnConfig: ReviewConfigBtnType[] = [
  {
    icon: "DislikeSvg",
    value: "较差",
    color: "#B4B4B4",
    edit: false,
  },
  {
    icon: "ChecKSvg",
    value: "一般",
    color: "#FBE2A4",
    edit: false,
  },
  {
    icon: "LikeSvg",
    value: "良好",
    color: "#0FB698",
    edit: false,
  },
];

const ReviewConfigModal: React.FC<ReviewConfigModalProp> = forwardRef(
  (props, ref) => {
    const { taskData, visible, OnClose } = props;
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [problemInput, setProblemInput] = useState<string>(
      "请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除"
    );
    const [answerInput, setAnswerInput] = useState<string>(
      "请审核生成的答案内容是否正确，如不正确请对其进行编辑修改"
    );
    const [rateInput, setRateInput] = useState<string>(
      "请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价"
    );
    const [buttons, setButtons] =
      useState<ReviewConfigBtnType[]>(DefaultBtnConfig);
    const reviewBtnRef = useRef(null);
    const [verifyUsers, setVerifyUser] = useState<UserType[]>([]);

    useEffect(() => {
      if (visible && taskData?.taskId) {
        const promise = getTaskIsSetReviewConfig(taskData?.taskId);
        promise.then((p1) => {
          if (p1.data.code === 200 && p1.data.data) {
            getReviewConfigInfo(taskData?.taskId).then((res) => {
              if (res?.data.code === 200) {
                const data = res.data.data;
                console.log("step", data);
                setProblemInput(data.qreviewCriteria);
                setAnswerInput(data.areviewCriteria);
                setRateInput(data.scoreReviewCriteria);
                setRateConfig(data.isStepTwo);
                setButtons(data.scoreButtonInfo);
              }
            });
          } else {
            setButtons(DefaultBtnConfig);
          }
        });

        getAllUserByTaskId(taskData?.taskId).then((res) => {
          if (res?.data?.code === 200) {
            setVerifyUser(res.data.data);
          }
        });
      }
    }, [visible, taskData]);

    const [rateConfig, setRateConfig] = useState<boolean>(true);

    const onConfirm = () => {
      if (reviewBtnRef.current) {
        const buttons = (reviewBtnRef.current as any)
          .getButtons()
          .map((btn: ReviewConfigBtnType) => {
            delete btn.edit;
            return btn;
          });
        const params = {
          areviewCriteria: answerInput,
          isStepTwo: rateConfig,
          qreviewCriteria: problemInput,
          scoreButtonInfoList: buttons,
          scoreReviewCriteria: rateInput,
          taskId: taskData?.taskId || "",
        };
        setReviewConfigInfo(params).then((res) => {
          if (res.data?.code === 200) {
            OnClose();
          }
        });
        messageApi.success("配置成功");
        const userId = sessionStorage.getItem("id");
        if (userId && taskData?.taskId) {
          getAllocateQA(taskData?.taskId, userId!);
        }
        navigate(`/main/task/review/${taskData?.taskId}`);
      }
    };
   
      
    
    return (
      <>
        <Modal
          centered
          title="审核配置"
          keyboard={false}
          maskClosable={false}
          styles={{ body: { height: "650px" } }}
          width={"870px"}
          open={visible}
          onOk={OnClose}
          onCancel={OnClose}
          destroyOnClose
          footer={
            <div className={classes["confirm-modal-footer"]}>
              <Button
                size="large"
                type="primary"
                shape="round"
                className={classes["confirm-btn"]}
                onClick={() => {
                  onConfirm();
                }}
              >
                确认配置
              </Button>
            </div>
          }
        >
          <div className={classes["config-modal-container"]}>
            <div className={classes["default-label"]}>
              在完成可见范围设置前，需要先完成生成数据的审核标准的配置
            </div>
            <div className={classes["default-label"]}>
              任务名称：{taskData?.taskName}
            </div>
            <div className={classes["default-label"]}>
              可见范围：
              <VisableRangeSelect
                defaultSelectUser={verifyUsers}
                className={classes["modal-range-select"]}
                onChange={(users: string[]) => {
                  console.log(users);
                }}
                taskId={taskData?.taskId || ""}
              />
            </div>
            <div className={classes["default-label"]}>审核配置:</div>
            <div className={classes["step-label"]}>Step1</div>
            <div className={classes["qreviewCriteria-container"]}>
              <div className={classes["title-label"]}>
                问题审核标准：
                <Button
                  className={classes["restore-default-btn"]}
                  type="link"
                  onClick={() =>
                    setProblemInput(
                      "请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除"
                    )
                  }
                >
                  恢复默认
                </Button>
              </div>
              <div>
                <Input
                  size="large"
                  value={problemInput}
                  onChange={(e) => {
                    setProblemInput(e.target.value);
                  }}
                />
              </div>
            </div>
            <div className={classes["areviewCriteria-container"]}>
              <div className={classes["title-label"]}>
                答案审核标准：
                <Button
                  className={classes["restore-default-btn"]}
                  type="link"
                  onClick={() =>
                    setAnswerInput(
                      "请审核生成的答案内容是否正确，如不正确请对其进行编辑修改"
                    )
                  }
                >
                  恢复默认
                </Button>
              </div>
              <div>
                <Input
                  size="large"
                  value={answerInput}
                  onChange={(e) => {
                    setAnswerInput(e.target.value);
                  }}
                />
              </div>
            </div>
            <div className={classes["step-label"]}>
              <Space>Step2</Space>
            </div>
            {/* <div className={classes["step-label"]}><Space>Step2 <Switch checked={rateConfig} onChange={(e) => setRateConfig(e)} />
                    </Space></div> */}
            {
              // rateConfig ?
              <>
                <div className={classes["scoreReviewCriteria-container"]}>
                  <div className={classes["title-label"]}>
                    打分评价标准：
                    <Button
                      className={classes["restore-default-btn"]}
                      type="link"
                      onClick={() =>
                        setRateInput(
                          "请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价"
                        )
                      }
                    >
                      恢复默认
                    </Button>
                  </div>
                  <Input
                    size="large"
                    value={rateInput}
                    onChange={(e) => {
                      setRateInput(e.target.value);
                    }}
                  />
                </div>
                <div className={classes["scoreButtonInfo-container"]}>
                  <div className={classes["title-label"]}>
                    <div className={classes["title-label"]}>
                      打分评价按键：
                      <label className={classes["btn-label"]}>
                        请注意，审核一旦开始，不能修改打分选项
                      </label>
                    </div>
                    <Button
                      type="link"
                      className={classes["restore-default-btn"]}
                      onClick={() =>
                        setButtons(JSON.parse(JSON.stringify(DefaultBtnConfig)))
                      }
                    >
                      恢复默认
                    </Button>
                  </div>
                  <ReviewConfigBtn
                    ref={reviewBtnRef}
                    DefaultBtnConfig={buttons}
                  />
                </div>
              </>
              // : <></>
            }
          </div>
        </Modal>
      </>
    );
  }
);

export default ReviewConfigModal;
