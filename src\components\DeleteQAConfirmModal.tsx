import { Button, Checkbox, Modal } from "antd";
import { useEffect, useState } from "react";
import { deleteQA } from "../api/qa";
interface DeleteQAConfirmModalProp {
  visible: boolean;
  OnClose: (isDelete: boolean, ignore?: boolean) => void;
}
const DeleteQAConfirmModal: React.FC<DeleteQAConfirmModalProp> = ({
  visible,
  OnClose,
}) => {
  const [ignoreConfirm, setIgnoreConfirm] = useState<boolean>(false);
  useEffect(() => {
    if (visible) setIgnoreConfirm(false);
  }, [visible]);
  return (
    <Modal
      centered
      title="删除提示"
      keyboard={false}
      maskClosable={false}
      width={"540px"}
      styles={{body:{ height: "50" }}}
      open={visible}
      onCancel={() => OnClose(false)}
      footer={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            padding: "2rem 0 0 0",
            gap: "8px",
          }}
        >
          <Checkbox
            checked={ignoreConfirm}
            onChange={(e) => {
              console.log(e);
              setIgnoreConfirm(e.target.checked);
            }}
          >
            今日不再提示
          </Checkbox>
          <Button type="text" onClick={() => OnClose(false)} shape="round">
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => {
              OnClose(true, ignoreConfirm);
            }}
            shape="round"
            className="review-btn-default"
            style={{ width: "120px" }}
          >
            确认删除
          </Button>
        </div>
      }
    >
      <div className="default-info" style={{ color: "black" }}>
        确定要删除本条数据吗？
      </div>
      <div className="upload-error-label" style={{ marginTop: "8px" }}>
        请注意删除数据后无法恢复。
      </div>
    </Modal>
  );
};

export default DeleteQAConfirmModal;
