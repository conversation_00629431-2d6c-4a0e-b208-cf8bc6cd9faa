import { message } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import Scrollbar from 'react-scrollbars-custom';
import '@/css/AdjustmentView.css';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { maxInfo, repeatitionInfo, split, topkInfo, toppInfo } from '@/utils/conts';
import {
  iteratSetValueMap,
  maxLenMap,
  repeatValueMap,
  topkValueMap,
  toppValueMap,
  evaluate,
} from './utils/trainProps';
import { ChatMessageType } from '../type';
import { getModelSets, GetModlesParams, modelconfig } from '@/api/modle';
import { TrainSet } from '../../../training/CreateTrainingView/type';
import Cookies from 'js-cookie';
import HeaderSpace from './components/HeaderSpace';
import TestView from './components/TestView';
import ParameterConfig from './components/ParameterConfig';
interface TrainSelectProp {
  modelData?: TrainSet;
}

const AdjustmentView = forwardRef((prop: TrainSelectProp, ref) => {
  const { task_id } = useParams<string>();
  const [OPTIONS, setOPTIONS] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState('');
  const [placeholder, setPlaceholder] = useState('请选择');
  const [modelConfig, setModelConfig] = useState<string | number>('自动配置');
  const { splitLevel, topkValue, toppValue, maxLen, repeatValue } = prop.modelData || {};
  const [isShow, setIsShow] = useState(false);
  const configString = sessionStorage.getItem('config');
  const config = configString ? JSON.parse(configString) : {};
  const [trainingSettings, setTrainingSettings] = useState<TrainingSettings>({
    splitLevel: splitLevel
      ? iteratSetValueMap(splitLevel)
      : config?.temperature
        ? evaluate('temperature', config?.temperature)
        : 50,
    topkValue: topkValue
      ? topkValueMap(topkValue)
      : config?.topK
        ? evaluate('topK', config?.topK)
        : 50,
    toppValue: toppValue
      ? toppValueMap(toppValue)
      : config?.topP
        ? evaluate('topP', config?.topP)
        : 50,
    maxLen: maxLen
      ? maxLenMap(maxLen)
      : config?.maxTokens
        ? evaluate('maxTokens', config?.maxTokens)
        : 50,
    repeatValue: repeatValue
      ? repeatValueMap(repeatValue)
      : config?.frequencyPenalty
        ? evaluate('frequencyPenalty', config?.frequencyPenalty)
        : 50,
  });
  const [messageList, setMessageList] = useState<ChatMessageType[]>([]);
  const [messageInput, setMessageInput] = useState<string>('');
  const scrollbarRef = useRef(null);
  const [historyMessages, setHistoryMessages] = useState<ChatMessageType[]>([]);
  const [isLocked, setIsLocked] = useState(false);

  const scrollChatToBottom = () => {
    if (scrollbarRef.current) {
      (scrollbarRef.current as any).scrollToBottom();
    }
  };

  const onSendMessage = async () => {
    if (isLocked) {
      console.log('等待回答完成后再发送新消息！');
      return;
    }
    if (!messageInput.trim()) return;

    setIsLocked(true); // 锁定发送功能

    const token = Cookies.get('token');
    const params = {
      modelId: selectedItems,
      temperature: split[trainingSettings.splitLevel / 25],
      topK: topkInfo[trainingSettings.topkValue / 25],
      topP: toppInfo[trainingSettings.toppValue / 25],
      maxTokens: maxInfo[trainingSettings.maxLen / 25],
      frequencyPenalty: repeatitionInfo[trainingSettings.repeatValue / 25],
      question: messageInput,
    };

    // 添加用户消息到消息列表
    setMessageList((prev) => [...prev, { role: 'user', content: messageInput }]);

    const response = await fetch(`/api/fine-tuning/model/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'ACCESS-KEY': token || '',
      },
      body: JSON.stringify(params),
    });

    if (!response.body) {
      console.error('Response body is null');
      setIsLocked(false);
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // 解码接收到的字节流
      buffer += decoder.decode(value, { stream: true });

      // 按行分割，处理每一行
      const lines = buffer.split('\n');
      // 保留最后一个未完成的行片段
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data:')) {
          const content = line.replace('data:', '').trim();

          // 更新消息列表
          setMessageList((prev) => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage?.role === 'ai') {
              lastMessage.content += content; // 拼接消息
              return [...prev];
            } else {
              return [...prev, { role: 'ai', content: content }];
            }
          });
        }
      }
    }

    // 清空输入框
    setIsLocked(false);
    setMessageInput('');
  };

  const handleNewConversation = () => {
    if (messageList.length > 0) {
      // 将当前对话添加到历史对话列表
      setHistoryMessages([...historyMessages, ...messageList]);
      // 清空当前对话
      setMessageList([]);
    }
  };
  const saveSet = () => {
    if (selectedItems) {
      setIsShow(false);
      const params = {
        modelId: selectedItems,
        temperature: split[trainingSettings.splitLevel / 25],
        topK: topkInfo[trainingSettings.topkValue / 25],
        topP: toppInfo[trainingSettings.toppValue / 25],
        maxTokens: maxInfo[trainingSettings.maxLen / 25],
        frequencyPenalty: repeatitionInfo[trainingSettings.repeatValue / 25],
        question: messageInput,
      };
      modelconfig(params).then((res) => {
        if (res.data?.code === 200) {
          message.success('保存配置成功');
        }
      });
    } else {
      setIsShow(true);
    }
  };
  const getModel = () => {
    const params: GetModlesParams = {
      page: 1,
      size: 100,
      category: 1,
      sortAttribute: 'modelName',
      sortDirection: 'asc',
      modelName: '',
      status: '7',
      modelCategory: 'FINE_TUNING',
    };
    getModelSets(params).then((res) => {
      if (res.data?.code === 200) {
        const options = res.data.data.map((item) => ({
          value: item.id,
          label: item.modelName,
        }));
        setOPTIONS(options);
        const defaultItem = options.find((item) => String(item.value) === task_id);
        if (defaultItem) {
          setPlaceholder(defaultItem.label);
          setSelectedItems(defaultItem.value); // 默认存储匹配的 id
        }
      }
    });
  };
  useEffect(() => {
    scrollChatToBottom();
  }, [messageList]);
  useEffect(() => {
    getModel();
  }, []);
  useEffect(() => {
    if (!prop.modelData && config) {
      setTrainingSettings({
        splitLevel: evaluate('temperature', config?.temperature),
        topkValue: evaluate('topK', config?.topK),
        toppValue: evaluate('topP', config?.topP),
        maxLen: evaluate('maxTokens', config?.maxTokens),
        repeatValue: evaluate('frequencyPenalty', config?.frequencyPenalty),
      });
      setModelConfig('自动配置');
    }
  }, [configString]);
  return (
    <Scrollbar>
      <div className="adjustmentDetail">
        <HeaderSpace saveSet={saveSet}></HeaderSpace>
        <div className="adjustment-detail-info">
          <div className="container-left-right">
            <ParameterConfig
              modelConfig={modelConfig}
              setModelConfig={setModelConfig}
              trainingSettings={trainingSettings}
              setTrainingSettings={setTrainingSettings}
            ></ParameterConfig>
            <TestView
              messageList={messageList}
              handleNewConversation={handleNewConversation}
              isLocked={isLocked}
              messageInput={messageInput}
              setMessageInput={setMessageInput}
              onSendMessage={onSendMessage}
            />
          </div>
        </div>
      </div>
    </Scrollbar>
  );
});

export default AdjustmentView;
