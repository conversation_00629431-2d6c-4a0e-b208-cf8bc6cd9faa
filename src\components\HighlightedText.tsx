import React from 'react';

interface HighlightedTextProps {
  text: string;
  highlights: { start: number; end: number, score: number }[];
}

const HighlightedText: React.FC<HighlightedTextProps> = ({ text, highlights }) => {
  let currentIndex = 0;
  // 定义正则表达式，匹配 [IMG](url)
  const imageRegex = /!\[IMG\]\((.*?)\)/g;
  // 使用正则表达式替换文本中的图片标记为React组件
  const parsedText = (targetText: string) => targetText.replace(imageRegex, (match, url) => (
    `<img style="width:100%" key=${url} src=${url} alt="图片" />`
  ));

  const highlightedText = highlights.map((highlight, index) => {
    console.log('highlight', highlight);
    
    const { start, end,score } = highlight;
    const beforeText = parsedText(text.slice(currentIndex, start));
    console.log('beforeText', beforeText);
    const highlightedPart = text.slice(start, end);
    currentIndex = end;
    let bgColor;
    if (score >= 0.9) {
        bgColor = 'rgb(153, 215, 202)';
    } else if (score >= 0.8 && score < 0.9) {
        bgColor = 'rgba(153, 215, 202,.6)';
    } else if (score >= 0.7 && score < 0.8) {
        bgColor = 'rgba(153, 215, 202,.3)';
    } else {
        bgColor = '#fff';
    }
    return (
      <React.Fragment key={index}>
        <div dangerouslySetInnerHTML={{ __html: beforeText }}></div>
        <span style={{ backgroundColor: bgColor }}>{highlightedPart}</span>
      </React.Fragment>
    );
  });

  const remainingText = text.slice(currentIndex);

  return <div>{highlightedText} 
    <div dangerouslySetInnerHTML={{ __html: parsedText(remainingText) }}></div>
  </div>;
};

export default HighlightedText;
