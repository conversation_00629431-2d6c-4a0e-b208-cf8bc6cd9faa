import React, { useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { Scrollbar } from 'react-scrollbars-custom';
import { TaskType } from '@/types';
import {
  deleteTask,
  GetTaskParams,
  getTaskProblemDetail,
  getTasks,
  retryTask,
  setReviewConfigInfo,
} from '@/api/task';
import { getAllocateQA } from '@/api/qa';
import UploadErrorModal from '@/components/UploadErrorModal';
import DatasetExportModal from '@/components/DatasetExportModal';
import TablePagination from '@/components/TablePagination';
import ReviewConfigModal from '@/components/ReviewConfigModal';
import SearchFilter from './components/SearchFilter';
import ActionButtons from './components/ActionButtons';
import TaskTable from './components/TaskTable';
import { useTaskSelection } from './hooks/useTaskSelection';
import { PaginationState, SearchFilterState, ModalState } from './types';
import { DEFAULT_BTN_CONFIG } from './constants/task';
const TaskView: React.FC = () => {
  const { confirm } = Modal;
  const navigate = useNavigate();

  // 基础状态
  const [taskData, setTaskData] = useState<TaskType[]>([]);
  const [currentTask, setCurrentTask] = useState<TaskType>();
  const [problemList, setProblemList] = useState<any[]>([]);
  const [apiFlg, setApiFlg] = useState(false);
  const [renameList, setRenameList] = useState<Set<string>>(new Set<string>());
  const [tableIndex, setTableIndex] = useState(0);

  // 分页状态
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    size: 10,
    total: 0,
  });

  // 搜索筛选状态
  const [searchFilter, setSearchFilter] = useState<SearchFilterState>({
    filterAttribute: 'taskName',
    sortAttribute: 'createTime',
    sortDirection: 'desc',
    filterTaskName: '',
    filterInput: '',
  });

  // 模态框状态
  const [modals, setModals] = useState<ModalState>({
    uploadErrorModal: false,
    exportModal: false,
    reviewConfigModal: false,
  });

  // 任务选择状态管理
  const {
    selectedRowKeys,
    allSelectedRows,
    handleSelectionChange,
    clearSelection,
    removeFromSelection,
  } = useTaskSelection(taskData);

  const intervalRef = useRef<NodeJS.Timer>();

  // 审核配置
  const handleReviewConfig = (id: string) => {
    const params = {
      areviewCriteria: '请审核生成的答案内容是否正确，如不正确请对其进行编辑修改',
      qreviewCriteria: '请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除',
      scoreReviewCriteria: '请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价',
      taskId: id,
      isStepTwo: true,
      scoreButtonInfoList: DEFAULT_BTN_CONFIG,
    };
    setReviewConfigInfo(params);
    const userId = sessionStorage.getItem('id');
    if (userId) {
      getAllocateQA(id, userId);
    }
  };
  // 人工审核功能
  const handleApply = (e: React.MouseEvent<HTMLElement>, rowdata: any) => {
    // 阻止默认
    e.preventDefault();

    // 如果创建者审核完了一次，则直接跳转到下一步审核页面
    if (rowdata.reviewConfigId === null) {
      handleReviewConfig(rowdata?.taskId);
    }
    const userId = sessionStorage.getItem('id');
    if (userId && rowdata?.taskId) {
      getAllocateQA(rowdata?.taskId, userId!);
    }
    navigate(`/main/task/review/${rowdata?.taskId}`);
    // 新需求，不需要配置直接开发权限

    // 创建者没有审核完成的话，协作者不能点击人工审核按钮
    // 创建者审核过的话，协作者就能点击人工审核按钮跳转到下一页面
  };

  // 获取任务列表
  const getTasksList = () => {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetTaskParams = {
        ...pagination,
        sortAttribute: searchFilter.sortAttribute,
        sortDirection: searchFilter.sortDirection,
        filterAttribute: searchFilter.filterAttribute,
        taskName: searchFilter.filterTaskName || '',
      };
      getTasks(params).then((res) => {
        setApiFlg(false);
        if (res?.data?.code === 200) {
          const storedName = sessionStorage.getItem('name');
          const updateData = res.data.data.map((task: any) => {
            let buttonDisabled = false;
            if (storedName !== task.creator.userName) {
              buttonDisabled = task.reviewConfigId === null;
            }
            return {
              ...task,
              buttonDisabled,
            };
          });

          setTaskData(updateData);
          if (res.data.data.length > 0) {
            setPagination({ ...pagination, total: res.data.totalCount });
            setTableIndex((pagination.page - 1) * pagination.size);
          }
        }
      });
    }
  };

  // 事件处理函数
  const handleSearch = () => {
    setSearchFilter((prev) => ({ ...prev, filterTaskName: prev.filterInput }));
  };

  const handleFilterInputChange = (value: string) => {
    setSearchFilter((prev) => ({ ...prev, filterInput: value }));
  };

  const handleFilterAttributeChange = (value: string) => {
    setSearchFilter((prev) => ({ ...prev, filterAttribute: value }));
  };

  const handleSortDirectionChange = (value: string) => {
    setSearchFilter((prev) => ({ ...prev, sortDirection: value }));
  };

  // 操作处理函数
  const handleDelete = () => {
    const modal = confirm({
      centered: true,
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      width: 540,
      content: (
        <div className="default-info" style={{ color: 'black' }}>
          确定要删除所选任务吗？
        </div>
      ),
      onOk() {
        const idList = allSelectedRows.map((item) => item.taskId);
        setTaskData((pre) => pre.filter((item) => !idList.includes(item.taskId)));
        deleteTask(idList).then((res: any) => {
          if (res.data.code === 200) {
            getTasksList();
            clearSelection();
          } else {
            message.error(res.data.message);
          }
        });
      },
    });
  };

  const handleExport = () => {
    setModals((prev) => ({ ...prev, exportModal: true }));
  };

  const handleUpload = () => {
    navigate('/main/task/uploadDataset');
  };

  const handleCreate = () => {
    navigate('/main/task/create');
  };

  const handleRename = (taskId: string) => {
    const renameSet = new Set(renameList);
    renameSet.add(taskId);
    setRenameList(renameSet);
  };

  const handleSingleDelete = (taskId: string) => {
    const modal = confirm({
      centered: true,
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      width: 540,
      content: (
        <div className="default-info" style={{ color: 'black' }}>
          确定要删除所选任务吗？
        </div>
      ),
      onOk() {
        setTaskData((pre) => pre.filter((item) => item.taskId !== taskId));
        deleteTask([taskId]).then((res: any) => {
          if (res.data.code === 200) {
            getTasksList();
          } else {
            message.error(res.data.message);
          }
        });
      },
    });
  };

  const handleProblemDetail = (taskId: string) => {
    getTaskProblemDetail(taskId).then((res) => {
      if (res.data?.code === 200) {
        setProblemList(res.data.data);
        setModals((prev) => ({ ...prev, uploadErrorModal: true }));
      }
    });
  };

  const handleRetry = (taskId: string) => {
    retryTask(taskId).then((res) => {
      if (res.data?.code === 200) {
        message.info(res.data.msg);
      } else {
        message.error(res.data.msg);
      }
    });
  };

  const handleDetail = (taskId: string) => {
    navigate(`/main/task/detail/${taskId}`);
  };

  const handleVisibleRangeChange = (taskId: string, users: string[]) => {
    console.log('Visible range changed:', taskId, users);
  };

  useEffect(() => {
    clearInterval(intervalRef.current as NodeJS.Timeout);
    getTasksList();
    intervalRef.current = setInterval(getTasksList, 5000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout);
    };
  }, [
    pagination.page,
    pagination.size,
    searchFilter.sortDirection,
    searchFilter.filterAttribute,
    searchFilter.sortAttribute,
    searchFilter.filterTaskName,
  ]);

  useEffect(() => {
    const timerId = setTimeout(() => {
      setSearchFilter((prev) => ({ ...prev, filterTaskName: prev.filterInput }));
    }, 500);
    return () => clearTimeout(timerId);
  }, [searchFilter.filterInput]);

  return (
    <Scrollbar>
      <div className="createTaskContent">
        <div
          style={{
            fontSize: '28px',
            lineHeight: '36px',
            fontWeight: '500',
            marginLeft: '2rem',
          }}
          className="mediumText"
        >
          训练数据
        </div>
        <div className="createTaskArea">
          <div
            style={{
              textAlign: 'start',
              justifyContent: 'space-between',
              display: 'inline-flex',
              marginBottom: '1rem',
              width: '100%',
            }}
          >
            <SearchFilter
              filterAttribute={searchFilter.filterAttribute}
              sortDirection={searchFilter.sortDirection}
              filterInput={searchFilter.filterInput}
              onFilterAttributeChange={handleFilterAttributeChange}
              onSortDirectionChange={handleSortDirectionChange}
              onFilterInputChange={handleFilterInputChange}
              onSearch={handleSearch}
            />
            <ActionButtons
              selectedRows={allSelectedRows}
              onDelete={handleDelete}
              onExport={handleExport}
              onUpload={handleUpload}
              onCreate={handleCreate}
            />
          </div>
          <TaskTable
            taskData={taskData}
            selectedRowKeys={selectedRowKeys}
            onSelectionChange={handleSelectionChange}
            tableIndex={tableIndex}
            renameList={renameList}
            setRenameList={setRenameList}
            getTasksList={getTasksList}
            onRename={handleRename}
            onDelete={handleSingleDelete}
            onApply={handleApply}
            onProblemDetail={handleProblemDetail}
            onRetry={handleRetry}
            onDetail={handleDetail}
            onVisibleRangeChange={handleVisibleRangeChange}
          />
          <TablePagination
            total={pagination.total}
            pageSize={pagination.size}
            page={pagination.page}
            OnChange={(page, pageSize) => {
              setPagination({ total: pagination.total, page, size: pageSize });
            }}
          />
        </div>
        <UploadErrorModal
          rowData={problemList}
          visible={modals.uploadErrorModal}
          OnClose={(rows) => {
            console.log(rows);
            setModals((prev) => ({ ...prev, uploadErrorModal: false }));
          }}
        />
        <DatasetExportModal
          visible={modals.exportModal}
          OnClose={() => setModals((prev) => ({ ...prev, exportModal: false }))}
          exportTaskData={allSelectedRows}
        />
        <ReviewConfigModal
          visible={modals.reviewConfigModal}
          OnClose={() => setModals((prev) => ({ ...prev, reviewConfigModal: false }))}
          taskData={currentTask}
        />
      </div>
    </Scrollbar>
  );
};

export default TaskView;
