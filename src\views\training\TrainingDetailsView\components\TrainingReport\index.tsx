import {
  Button,
  Descriptions,
  DescriptionsProps,
  Drawer,
  Space,
  Tooltip,
} from "antd";
import * as echarts from "echarts";
import { useEffect, useRef, useState } from "react";
import classes from "./index.module.css";
import { TariningSampleType, TrainingType } from "../../type";
import { QuestionCircleFilled } from "@ant-design/icons";
import DifficultSamplesItem from "../DifficultSamplesItem";
import { title } from "process";
import { tooltip } from "@antv/g2/lib/interaction/tooltip";

interface TrainingReportProp {
  trainingData: TrainingType;
}

const TrainingReport: React.FC<TrainingReportProp> = ({ trainingData }) => {
  const chartRef = useRef<HTMLDivElement | null>(null);
  const [open, setOpen] = useState(false);
  const [samples, setSamples] = useState<TariningSampleType[]>([
    {
      question:
        "歼-20战斗机的隐身能力和战略地位如何体现,以及其在中国空军中的历史意义？",
      text: "取值为0时任务随机选择1条数据用于测试，且无论比例取值多少，任务最多拆分100条数据用于测试。",
      answer:
        '歼-20,被称为"威龙",是中国解放军自主研发的第五代制空占战斗机,具备高隐身性、高态势感知和高机动XXXXXXXXXXXXX……',
      score: 32.9,
    },
  ]);

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const items: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "模型评分",
      children: trainingData.name,
    },
    {
      key: "2",
      label: "PPL",
      children: trainingData.data?.predictRougeL?.toFixed(2) || "-",
    },
    {
      key: "3",
      label: "ROUGE-1",
      children: trainingData.data?.predictRouge1?.toFixed(2) || "-",
    },
    {
      key: "4",
      label: "ROUGE-2",
      children: trainingData.data?.predictRouge2?.toFixed(2) || "-",
    },
    {
      key: "5",
      label: "BLEU-1",
      children: trainingData.data?.predictBleu2?.toFixed(2) || "-",
    },
    {
      key: "6",
      label: "BLEU-2",
      children: trainingData.data?.predictBleu3?.toFixed(2) || "-",
    },
    {
      key: "7",
      label: "BERTScore",
      children: trainingData.data?.predictBleu4?.toFixed(2) || "-",
    },
  ];
  const initChart = () => {
    if (chartRef.current) {
      const dataChart = echarts.init(chartRef.current);

      const option = {
        title: {
          text: `—————  ${trainingData.name}\n${""}`,
          subtext: `—————  ${trainingData.modelBaseName}`,
          left: "right",
          textAlign: "left",
          textStyle: {
            color: "rgba(57, 208, 181, 1)",
            fontSize: 14,
          },
          subtextStyle: {
            color: "rgba(41, 85, 231, .8)",
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: (params: any) => {
            return params;
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(208, 241, 255, 1)",
          },
        },
        color: ["#39D0B5"],
        radar: {
          // shape: 'circle',
          axisName: {
            show: true,
            color: "#000",
            fontSize: 14,
          },
          indicator: [
            { name: "PPL\n衡量模型好坏程度", max: 100 },
            {
              name: "ROUGE-1\n衡量单个词在生成文本与\n参考文本之间的覆盖率",
              max: 100,
            },
            {
              name: "ROUGE-2\n衡量两个词在生成文本与\n参考文本之间的覆盖率",
              max: 100,
            },
            {
              name: "BESTSCore\n衡量生成文本与参考文本句子之间语义\n相似度",
              max: 100,
            },
            {
              name: "BLEU-2\n衡量两个词在生成文本与\n参考文本之间的准确率",
              max: 100,
            },
            {
              name: "BLEU-1\n衡量单个词在生成文本与\n参考文本之间的准确率",
              max: 100,
            },
          ],
          axisLine: {
            // 添加这部分来设置坐标轴样式，隐藏圆点
            show: true,

            symbol: ["none", "none"], // 设置起点和终点的符号为无，即去掉圆点
          },
        },
        series: [
          {
            name: `${trainingData.name}`,
            type: "radar",
            symbol: "none",
            data: [
              {
                value: [
                  trainingData.data?.predictRougeL.toFixed(2),
                  trainingData.data?.predictRouge1.toFixed(2),
                  trainingData.data?.predictRouge2.toFixed(2),
                  trainingData.data?.predictBleu2.toFixed(2),
                  trainingData.data?.predictBleu3.toFixed(2),
                  trainingData.data?.predictBleu4.toFixed(2),
                ],
                name: "XZmodel",
              },
            ],
            areaStyle: {
              // 添加这部分来设置区域填充颜色

              color: "rgba(57, 208, 181, 0.6)", // 这里设置具体的颜色及透明度，可按需调整
            },
            lineStyle: {
              color: "rgba(57, 208, 181, .6)",
            },
          },
          {
            name: `${trainingData.modelBaseName}`,
            type: "radar",
            symbol: "none",
            data: [
              {
                value: [
                  trainingData.data?.basePredictRougeL.toFixed(2),
                  trainingData.data?.basePredictRouge1.toFixed(2),
                  trainingData.data?.basePredictRouge2.toFixed(2),
                  trainingData.data?.basePredictBleu2.toFixed(2),
                  trainingData.data?.basePredictBleu3.toFixed(2),
                  trainingData.data?.basePredictBleu4.toFixed(2),
                ],
                name: "XZmodel",
              },
            ],
            areaStyle: {
              // 添加这部分来设置区域填充颜色

              color: "rgba(41, 85, 231,.8)", // 这里设置具体的颜色及透明度，可按需调整
            },
            lineStyle: {
              color: "rgba(41, 85, 231, .8)",
            },
          },
        ],
      };

      dataChart.setOption(option);
      return () => {
        dataChart.dispose();
      };
    }
    return undefined; // 明确返回 undefined 当 chartRef.current 不存在时
  };
  useEffect(() => {
    initChart();
  });
  return (
    <>
      <div>
        <div
          style={{
            fontSize: "16px",
            fontWeight: "500",
            height: "40px",
            margin: "8px",
            padding: "0 16px",
          }}
        >
          评估报告
        </div>
        {/* <Button type="link" onClick={() => showDrawer()}>查看难点样本</Button> */}
      </div>
      <div
        ref={chartRef}
        className={classes["report-chart"]}
        style={{ width: "100%", height: "450px" }}
      ></div>
      <div className={classes["report-desc"]}>
        <Descriptions
          size="small"
          layout="vertical"
          bordered
          items={items}
          column={7}
        />
      </div>
      <Drawer
        title={
          <Space>
            <label>难点样本</label>
            <Tooltip
              title={
                "集中展示倒数20个测试集中对应的问题，模型生成的答案与真实答案的对比。"
              }
            >
              <QuestionCircleFilled
                style={{ color: "rgba(195, 202, 213, 1)" }}
              />
            </Tooltip>
          </Space>
        }
        onClose={onClose}
        open={open}
        closeIcon={false}
        size="large"
      >
        {samples.map((sample) => {
          return (
            <DifficultSamplesItem
              question={sample.question}
              text={sample.text}
              answer={sample.answer}
              score={sample.score}
            />
          );
        })}
      </Drawer>
    </>
  );
};
export default TrainingReport;
