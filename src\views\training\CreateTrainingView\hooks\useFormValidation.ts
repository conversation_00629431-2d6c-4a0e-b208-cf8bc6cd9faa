import { RefObject } from 'react';

interface FormRefs {
  modelSelectRef: RefObject<any>;
  taskSelectRef: RefObject<any>;
  trainingSetSelectRef: RefObject<any>;
  resourceSetSelectRef: RefObject<any>;
}

export const useFormValidation = (refs: FormRefs) => {
  const validateAllSteps = async (): Promise<{
    isValid: boolean;
    firstInvalidStepIndex?: number;
  }> => {
    const { modelSelectRef, taskSelectRef, trainingSetSelectRef, resourceSetSelectRef } = refs;

    // Step 0: ModelSelect
    if (modelSelectRef.current) {
      const modelData = modelSelectRef.current.getModelSelectData();
      if (!modelData.Name || !modelData.Description || !modelData.Dataset) {
        modelSelectRef.current.validateFields?.();
        return { isValid: false, firstInvalidStepIndex: 0 };
      }
    }

    // Step 1: TaskSelect
    if (taskSelectRef.current) {
      const taskData = taskSelectRef.current.getTaskSelectData();
      if (!taskData.Task || taskData.Task.length === 0) {
        return { isValid: false, firstInvalidStepIndex: 1 };
      }
    }

    // Step 2: TrainingSetSelect
    if (trainingSetSelectRef.current) {
      const trainingSetData = trainingSetSelectRef.current.getTaskSelectData();
      if (!trainingSetData.Srategy || trainingSetData.Srategy.length === 0) {
        return { isValid: false, firstInvalidStepIndex: 2 };
      }
    }

    // Step 3: Overview (ResourceSetSelect)
    if (resourceSetSelectRef.current) {
      const overviewData = resourceSetSelectRef.current.getOverviewData();
      if (
        !overviewData.Framework ||
        overviewData.Framework.length === 0 ||
        overviewData.ServerSelection === undefined ||
        !overviewData.ArithmeticConfiguration ||
        overviewData.ArithmeticConfiguration.length === 0 ||
        !Number.isInteger(overviewData.ServerConfigId)
      ) {
        resourceSetSelectRef.current.validateFields?.();
        return { isValid: false, firstInvalidStepIndex: 3 };
      }
    }

    return { isValid: true };
  };

  return {
    validateAllSteps,
  };
};
