import React, { useEffect, useState } from "react";
import { Button, Input, Form, message, Space } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { LeftOutlined } from "@ant-design/icons";
import { updateServer, queryServerById } from "../../../../../api/server";
import type { ServerConfiguration } from "../data";
import type { Rule } from "antd/es/form";
import { deleteServer } from "../../../../../api/server";
import "../../../../../css/uploadBaseModel.css";
import DeleteConfirmModal from "../../../../../components/DeleteConfirmModal";
const FormInput = ({
  name,
  label,
  rules,
  placeholder,
}: {
  name: keyof ServerConfiguration;
  label: string;
  rules?: Rule[];
  placeholder?: string;
}) => (
  <Form.Item name={name} label={label + ":"} rules={rules}>
    <Input
      placeholder={placeholder}
      size="large"
      allowClear
      className="form-field-input"
    />
  </Form.Item>
);

const UpDateServer: React.FC = () => {
  const navigate = useNavigate();
  const { server_id } = useParams<{ server_id: string }>();
  const [form] = Form.useForm<ServerConfiguration>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await queryServerById(Number(server_id));
        data && form.setFieldsValue(data);
      } catch {
        message.error("数据加载失败");
      }
    };
    loadData();
  }, []);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const res = await updateServer({ ...values, id: Number(server_id) });
      if (res.code === 200) {
        message.success("上传成功");
        navigate(-1);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error("提交失败，请检查表单");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const id = Number(server_id);
      const res = await deleteServer(id);
      if (res) {
        navigate(-1);
        message.success("删除成功");
      } else {
        message.error("删除失败");
      }
    } catch (error) {
      message.error("删除");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="uploadBaseModel">
      {/* 头部导航部分保持不变 */}
      <Space size={20} style={{ display: "inline-flex", alignItems: "center" }}>
        <Button
          style={{ fontSize: "12px", width: "36px", height: "36px" }}
          shape="circle"
          icon={<LeftOutlined />}
          onClick={() => navigate(-1)}
        />
        <div
          className="mediumText"
          style={{ fontSize: "20px", lineHeight: "36px", fontWeight: "500" }}
        >
          服务器配置
        </div>
      </Space>
      <div className="uploadModelArea">
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18, offset: 2 }}
        >
          <FormInput
            name="serverName"
            label="服务器名称"
            rules={[{ required: true, message: "请输入名称" }]}
          />
          <FormInput
            name="resourceUrl"
            label="服务器IP"
            rules={[{ required: true, message: "请输入IP" }]}
          />
          <FormInput name="cpuInfo" label="CPU核数" />
          <FormInput name="gpuInfo" label="GPU信息" />
          <FormInput name="memoryInfo" label="内存信息" />

          <Form.Item name="description" label="服务器描述:">
            <Input.TextArea rows={4} className="form-field-input" />
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 5 }}>
            <Button
              shape="round"
              className="cancalBut"
              size="large"
              style={{ marginRight: "60px" }}
              onClick={() => navigate(-1)}
            >
              取消
            </Button>
            <Button
              shape="round"
              htmlType="submit"
              className="submBut"
              size="large"
              style={{ marginLeft: "60px" }}
              loading={loading}
              onClick={handleSubmit}
            >
              提交
            </Button>
            <Button
              shape="round"
              size="large"
              style={{
                marginLeft: "60px",
                color: "white",
                backgroundColor: "rgba(231, 82, 82, 1)",
              }}
              loading={loading}
              onClick={() => setVisible(true)}
            >
              服务器删除
            </Button>
          </Form.Item>
        </Form>
      </div>
      <DeleteConfirmModal
        visible={visible}
        onCancel={() => setVisible(false)}
        onDelete={handleDelete}
        title="提示"
        content="确认删除该服务器吗？"
      />
    </div>
  );
};

export default UpDateServer;
