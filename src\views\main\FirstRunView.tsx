import { FunctionComponent, useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "../../css/FirstRunView.css";
import { But<PERSON> } from "antd";
import { Scrollbar } from "react-scrollbars-custom";
import { RightOutlined } from "@ant-design/icons";
import ScrollableImageCarousel from "../../components/ScrollableImageCarousel";
import arrowWhite from "../../assets/img/arrow-white.svg";
import arrowBlack from "../../assets/img/arrow-black.svg";
import banner1 from "../../assets/img/banner-1.png";
import banner2 from "../../assets/img/banner-2.png";
import banner3 from "../../assets/img/banner-3.png";
import banner4 from "../../assets/img/banner-4.png";
import banner5 from "../../assets/img/banner-5.png";
import banner6 from "../../assets/img/banner-6.png";
import taskManagement from "../../assets/img/task-management.png";
import dataExport from "../../assets/img/data-export.png";
import taskReview from "../../assets/img/task-review.png";
import createTask from "../../assets/img/create-task.png";
const FirstRunView: FunctionComponent = () => {
  const navigate = useNavigate();
  const [activeIndex, setActiveIndex] = useState(0);
  const images: string[] = [
    banner1,
    banner2,
    banner3,
    banner4,
    banner5,
    banner6,
  ];

  const setCarouselVal = (index: number) => {
    setActiveIndex(index);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % 4);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const onText17Click = useCallback(() => {
    navigate("/");
  }, [navigate]);

  const onText18Click = useCallback(() => {
    navigate("/1");
  }, [navigate]);

  const getActiveImg = (): string => {
    if (activeIndex === 0) {
      return createTask;
    } else if (activeIndex === 1) {
      return taskManagement;
    } else if (activeIndex === 2) {
      return taskReview;
    } else if (activeIndex === 3) {
      return dataExport;
    } else {
      return "";
    }
  };

  return (
    // <div
    //   className="div648"
    // >
    <Scrollbar className="div648">
      <div
        className="scroll-snap-item"
        style={{
          // width: "100vw",
          maxHeight: "calc(100vh - 4rem)",
          overflow: "hidden",
          minHeight: "849px",
        }}
      >
        <div className="card-area group-parent">
          <div className="div649 lightText">企业级大模型训练数据生成工具</div>
          {/* <Button type="primary" className="start-btn boldText">
            立即使用
          </Button> */}
          <div className="div650 boldText">#行至智能</div>
          <div className="logo-parent1">
            <img
              style={{ width: "42px", height: "55.125px" }}
              src="/logo.png"
            />
            <b className="one-click-upload boldText">Pollux</b>
          </div>
        </div>
      </div>
      <div className="scroll-snap-item" style={{ width: "100vw" }}>
        <div className="card-area child81">
          <Button
            className="vector-parent2 div654 frame-child296 primary-btn boldText"
            type="primary"
            style={{ color: "white" }}
            onClick={() => {
              navigate("/main/sourcedata");
            }}
          >
            <div className="upload-btn">
              上传本地数据集
              <img className="frame-child297" alt="" src={arrowWhite} />
            </div>
          </Button>
          <div className="frame-parent11">
            <div className="rectangle-parent58">
              <div className="frame-child298" />
              <div className="frame-child299" />
              <div className="frame-child300" />
            </div>
            <b className="one-click-upload boldText">One-click upload</b>
          </div>
          <div className="div652 lightText">
            <p className="p">支持多源异构数据集</p>
            <p className="p">一键上传解析</p>
          </div>
        </div>
      </div>
      <div className="scroll-snap-item" style={{ width: "100vw" }}>
        <div className="card-area child87">
          <div className="frame-parent11">
            <div className="rectangle-parent58">
              <div className="frame-child298" />
              <div className="frame-child299" />
              <div className="frame-child300" />
            </div>
            <b className="one-click-upload boldText" style={{ color: "white" }}>
              Purpose-built
            </b>
          </div>
          <div className="div653 lightText">
            <p className="p">专用问题推理</p>
            <p className="p">大模型基座</p>
          </div>
          <div className="inner3">
            <ScrollableImageCarousel images={images} scrollSpeed={30} />
          </div>
        </div>
      </div>
      <div className="scroll-snap-item" style={{ width: "100vw" }}>
        <div className="card-area child90">
          <div className="parent125">
            <b
              onClick={() => setCarouselVal(0)}
              className={activeIndex === 0 ? "b63" : "div656"}
            >
              任务创建 {activeIndex === 0 ? <RightOutlined /> : null}
            </b>
            <b
              onClick={() => setCarouselVal(1)}
              className={activeIndex === 1 ? "b63" : "div656"}
            >
              任务管理{activeIndex === 1 ? <RightOutlined /> : null}
            </b>
            <b
              onClick={() => setCarouselVal(2)}
              className={activeIndex === 2 ? "b63" : "div656"}
            >
              数据审核{activeIndex === 2 ? <RightOutlined /> : null}
            </b>
            <b
              onClick={() => setCarouselVal(3)}
              className={activeIndex === 3 ? "b63" : "div656"}
            >
              数据导出{activeIndex === 3 ? <RightOutlined /> : null}
            </b>
          </div>
          <div className="div655 lightText">
            <p className="p">高效任务管理&数据</p>
            <p className="p">审核平台</p>
          </div>
          <img className="carousel-img" src={getActiveImg()} width="100%" />
          <Button
            type="primary"
            className="div654 frame-child296 primary-btn boldText"
            onClick={() => {
              navigate("/main/task/create");
            }}
            style={{ backgroundColor: "#CCFFA3", border: "#CCFFA3" }}
          >
            <div className="upload-btn boldText">
              创建第一个任务
              <img className="frame-child297" alt="" src={arrowBlack} />
            </div>
          </Button>
          <div className="frame-parent11">
            <div className="rectangle-parent58">
              <div className="frame-child298" />
              <div className="frame-child299" />
              <div className="frame-child300" />
            </div>
            <b className="one-click-upload boldText" style={{ color: "white" }}>
              Efficient & Data Audit
            </b>
          </div>
        </div>
      </div>
    </Scrollbar>
  );
};

export default FirstRunView;
