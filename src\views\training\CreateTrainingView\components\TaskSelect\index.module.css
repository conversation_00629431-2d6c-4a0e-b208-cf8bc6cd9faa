.step-label {
  font-size: 24px;
  line-height: 40px;
  font-weight: bold;
  letter-spacing: 0em;
  text-align: left;
  width: 15% !important;
  font-family: 'HarmonyOS Sans SC', sans-serif;
}
.step-container {
  flex-flow: unset;
}
.step-content {
  width: 100% !important;
}

.step-label .ant-col-4 {
  flex: 0 0 15%;
  max-width: 15%;
}

.step-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;

  label {
    line-height: 40px;
    font-size: 14px;
  }
}

.dataset-container {
  width: 50%;
}

.dataset-title {
  width: 117px;
}

.slider-mark {
  width: 10;
  height: 10;
  margin-top: 10;
  z-index: 10;
  font-size: 12px;
  color: rgba(142, 152, 167, 1);
}
