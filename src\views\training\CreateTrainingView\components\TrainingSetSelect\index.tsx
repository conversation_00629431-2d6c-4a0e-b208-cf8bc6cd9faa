import { Flex, Form, Segmented, Select, Tooltip } from 'antd';
import classes from './index.module.css';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import group151 from '@/assets/img/group-151.svg';
import { iteratInfo, learnInfo, batchInfo } from '@/utils/conts';
import { TariningType, ModelFailData } from '../../type';
import ParameterConfig from './ParameterConfig';
import { getInitialTrainingSettings, resetToDefaultConfig } from './utils';
import '@/css/AdjustmentView.css';

interface TrainSelectProp {
  modelData: TariningType;
}
interface MyObject {
  modelId: number;
  modelConfigData: ModelFailData;
}
const TrainingSetSelect = forwardRef((prop: TrainSelectProp, ref) => {
  const { srategy, learningValue, iterationLevel, batchValue, trainSetConfig } = prop.modelData;
  const [form] = Form.useForm();
  const training: string = localStorage.getItem('trainingData') || '{}';
  const objectArray: MyObject[] = JSON.parse(training) as MyObject[];
  const [modelFailProp, setModelFailProp] = useState<ModelFailData>(
    objectArray[0]?.modelConfigData
  );

  const [selectedItems, setSelectedItems] = useState<string>(
    srategy || modelFailProp?.properties.trainConfig.trainStrategy
  );
  const OPTIONS = ['LoRA'];
  const [modelConfig, setModelConfig] = useState<string | number>(trainSetConfig || '自动配置');
  const [trainingSettings, setTrainingSettings] = useState(
    getInitialTrainingSettings(iterationLevel, learningValue, batchValue)
  );

  // 处理配置模式切换
  const handleConfigModeChange = (value: string | number) => {
    setModelConfig(value);
    // 当切换回自动配置时，重置为默认配置
    if (value === '自动配置') {
      setTrainingSettings(resetToDefaultConfig());
    }
  };

  // 处理训练设置变化
  const handleTrainingSettingsChange = (newSettings: any) => {
    setTrainingSettings(newSettings);
  };

  // 处理从自动配置切换到手动配置
  const handleSwitchToManual = () => {
    setModelConfig('手动配置');
  };

  useImperativeHandle(ref, () => ({
    getTaskSelectData: () => {
      return {
        TrainSetConfig: modelConfig,
        Srategy: selectedItems,
        IterationLevel: iteratInfo[trainingSettings.iterationLevel / 25],
        LearningValue: learnInfo[trainingSettings.learningValue / 25],
        BatchValue: batchInfo[trainingSettings.batchValue / 25],
      };
    },
  }));

  const initForm = () => {
    if (prop.modelData) {
      form.setFieldsValue({
        iteration: trainingSettings.iterationLevel,
        learningrate: trainingSettings.learningValue,
        batchprocessing: trainingSettings.batchValue,
      });
    }
  };

  useEffect(() => {
    initForm();
  });

  return (
    <>
      <div className={classes['training-content']}>
        <div className={classes['step3']} style={{ width: '15%' }}>
          {/* Step 3 */}
          训练配置
        </div>
        <div className={classes['operatearea']}>
          <div className={classes['strategy']}>
            训练策略:
            <Flex style={{ width: '340px', margin: '-10px 0 0 55px' }}>
              <Select
                size="large"
                placeholder="选择要训练的策略"
                value={selectedItems}
                onChange={setSelectedItems}
                style={{ flex: 1 }}
                //open={isTextVisible && !isDropdownDisabled}
                options={OPTIONS.map((item) => ({
                  value: item,
                  label: item,
                }))}
              />
            </Flex>
            <Tooltip
              placement="rightTop"
              title="在固定预训练大模型本身的参数的基础上，在保留自注意力模块中原始权重矩阵的基础上，
                    对权重矩阵进行低秩分解，训练过程中只更新低秩部分的参数。"
            >
              <img className="frame-child179" style={{ marginLeft: '30px' }} src={group151} />
            </Tooltip>
          </div>
          <div className={classes['parameter']}>
            参数配置:
            <div style={{ margin: '-10px 0 0 55px' }}>
              <Form.Item<CreateTaskFormType>>
                <Segmented
                  className="createtask-segmented"
                  size="large"
                  options={[
                    {
                      label: (
                        <Tooltip title="自动配置">
                          <a
                            onClick={() => handleConfigModeChange('自动配置')}
                            className={
                              modelConfig === '自动配置' ? 'model-config-active' : 'model-config'
                            }
                          >
                            自动配置
                          </a>
                        </Tooltip>
                      ),
                      value: '自动配置',
                    },
                    {
                      label: (
                        <Tooltip title="手动配置">
                          <a
                            onClick={() => handleConfigModeChange('手动配置')}
                            className={
                              modelConfig === '手动配置' ? 'model-config-active' : 'model-config'
                            }
                          >
                            手动配置
                          </a>
                        </Tooltip>
                      ),
                      value: '手动配置',
                    },
                  ]}
                  value={modelConfig}
                  onChange={handleConfigModeChange}
                />
              </Form.Item>
            </div>
          </div>
          <div className="creative">
            <ParameterConfig
              form={form}
              modelFailProp={modelFailProp}
              trainingSettings={trainingSettings}
              isAutoConfig={modelConfig === '自动配置'}
              onTrainingSettingsChange={handleTrainingSettingsChange}
              onConfigModeChange={handleSwitchToManual}
            />
          </div>
        </div>
      </div>
    </>
  );
});
export default TrainingSetSelect;
