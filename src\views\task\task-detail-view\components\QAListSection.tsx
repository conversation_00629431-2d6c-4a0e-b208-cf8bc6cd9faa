import React, { useState } from 'react';
import { List, Checkbox, message } from 'antd';
import { QADocument, HighlightIdx } from '../../../../types';
import { QAInfoType, getFileContent, getTaglList } from '../../../../api/qa';
import { change<PERSON><PERSON>l<PERSON>ist, QaUpdateParams } from '../../../../api/task';
import HighlightText from '../../../../components/HighlightText';
import EditableTagGroup from '../../../../components/EditableTagGroup';
import { ReviewConfigBtnType } from '../../../../components/ReviewConfigModal/components/ReviewConfigBtn/type';
import q from '../../../../assets/img/q.svg';
import a from '../../../../assets/img/a.svg';

interface QAListSectionProps {
  displayQaList: QADocument[];
  showCheckbox: boolean;
  expendList: number;
  filterVal?: string;
  tagsVisible: boolean;
  taskId: string;
  scoreButtonInfo: ReviewConfigBtnType[];
  checkedQASet: QAInfoType[];
  checkedFileSet: Set<string>;
  excludeQASet: QAInfoType[];
  globalSelectedQAIds: Set<string>;
  globalSelectedQADetails: Map<string, { fileId: string; id: string }>;
  onExpendListChange: (index: number) => void;
  onHighlightChange: (highlights: HighlightIdx[]) => void;
  onSourceDataChange: (data: string) => void;
  onGlobalSelectedChange: (
    ids: Set<string>,
    details: Map<string, { fileId: string; id: string }>
  ) => void;
  onTreeCheckKeysChange: (updateFn: (prev: React.Key[]) => React.Key[]) => void;
  onCheckedQASetChange: (qaSet: QAInfoType[]) => void;
  onExcludeQASetChange: (qaSet: QAInfoType[]) => void;
  onTagListUpdate: () => void;
  onFileLabelRefresh?: () => void; // 触发FileLabelView刷新
}

const QAListSection: React.FC<QAListSectionProps> = ({
  displayQaList,
  showCheckbox,
  expendList,
  filterVal,
  tagsVisible,
  taskId,
  scoreButtonInfo,
  checkedQASet,
  checkedFileSet,
  excludeQASet,
  globalSelectedQAIds,
  globalSelectedQADetails,
  onExpendListChange,
  onHighlightChange,
  onSourceDataChange,
  onGlobalSelectedChange,
  onTreeCheckKeysChange,
  onCheckedQASetChange,
  onExcludeQASetChange,
  onTagListUpdate,
  onFileLabelRefresh,
}) => {
  const QADocumentItem: React.FC<{
    QAItem: QADocument;
    index: number;
  }> = ({ QAItem, index }) => {
    const getCheckState = () => {
      // 优先检查全局选中状态
      if (globalSelectedQAIds.has(QAItem.id)) {
        return true;
      }

      if (
        checkedQASet.some((item) => item.fileId === QAItem.fileId && item.ids.includes(QAItem.id))
      ) {
        return true;
      } else if (checkedFileSet.has(QAItem.fileId)) {
        if (
          excludeQASet.some((item) => item.fileId === QAItem.fileId && item.ids.includes(QAItem.id))
        ) {
          return false;
        }
        return true;
      }
      return false;
    };

    const [expanded, setExpanded] = useState<boolean>(expendList === index);
    const [checked, setChecked] = useState<boolean>(getCheckState());

    const getBorderStyle = (item: QADocument) => {
      let color = 'white';
      scoreButtonInfo?.forEach((info) => {
        if (info.value === item.score) {
          color = info.color;
        }
      });
      return `3px solid ${color}`;
    };

    const handleCheckboxChange = (e: any) => {
      const _checked = e.target.checked;
      setChecked(_checked);

      // 更新全局选中状态
      const newGlobalSelected = new Set(globalSelectedQAIds);
      const newGlobalDetails = new Map(globalSelectedQADetails);

      if (_checked) {
        newGlobalSelected.add(QAItem.id);
        newGlobalDetails.set(QAItem.id, { fileId: QAItem.fileId, id: QAItem.id });
      } else {
        newGlobalSelected.delete(QAItem.id);
        newGlobalDetails.delete(QAItem.id);
      }

      onGlobalSelectedChange(newGlobalSelected, newGlobalDetails);

      if (_checked) {
        // 选中逻辑
        onTreeCheckKeysChange((pre: React.Key[]) => {
          if (pre.includes(QAItem.fileId)) {
            return pre;
          }
          return [...pre, QAItem.fileId];
        });

        if (checkedFileSet.has(QAItem.fileId)) {
          // 当前QA所属文件树被选中，从反选数组里删除当前QA
          onExcludeQASetChange(
            excludeQASet
              .map((qaInfo) => {
                if (qaInfo.fileId === QAItem.fileId) {
                  const updatedIds = qaInfo.ids.filter((id) => id !== QAItem.id);
                  if (updatedIds.length === 0) {
                    return null;
                  }
                  return { ...qaInfo, ids: updatedIds };
                }
                return qaInfo;
              })
              .filter(Boolean) as QAInfoType[]
          );
        } else {
          // 当前QA所属文件树未被选中，将QA数据添加到单选数组
          const newCheckedQASet = [...checkedQASet];
          const existingIndex = newCheckedQASet.findIndex((item) => item.fileId === QAItem.fileId);

          if (existingIndex >= 0) {
            newCheckedQASet[existingIndex].ids.push(QAItem.id);
          } else {
            newCheckedQASet.push({
              fileId: QAItem.fileId,
              ids: [QAItem.id],
            });
          }
          onCheckedQASetChange(newCheckedQASet);
        }
      } else {
        // 取消选中逻辑
        if (checkedFileSet.has(QAItem.fileId)) {
          // 当前QA所属文件树被选中，当前QA加入反选数组
          const newExcludeQASet = [...excludeQASet];
          const existingIndex = newExcludeQASet.findIndex((item) => item.fileId === QAItem.fileId);

          if (existingIndex >= 0) {
            newExcludeQASet[existingIndex].ids.push(QAItem.id);
          } else {
            newExcludeQASet.push({
              fileId: QAItem.fileId,
              ids: [QAItem.id],
            });
          }
          onExcludeQASetChange(newExcludeQASet);
        } else {
          // 检查是否需要取消选择当前文件树
          const flg = checkedQASet.some(
            (item) =>
              item.fileId === QAItem.fileId &&
              item.ids.filter((id) => id !== QAItem.id).length === 0
          );
          if (flg) {
            onTreeCheckKeysChange((pre: React.Key[]) => {
              return pre.filter((item) => item !== QAItem.fileId);
            });
          }

          // 将QA从单选数组删除
          const updatedCheckedQASet = checkedQASet
            .map((qaInfo) => {
              if (qaInfo.fileId === QAItem.fileId) {
                const updatedIds = qaInfo.ids.filter((id) => id !== QAItem.id);
                if (updatedIds.length === 0) {
                  return null;
                }
                return { ...qaInfo, ids: updatedIds };
              }
              return qaInfo;
            })
            .filter(Boolean) as QAInfoType[];

          onCheckedQASetChange(updatedCheckedQASet);
        }
      }
    };

    const handleQAClick = () => {
      onHighlightChange(QAItem.highlightIdxList);
      onExpendListChange(expendList === index ? -1 : index);
      setExpanded(true);

      getFileContent(taskId, QAItem.id).then((res) => {
        if (res.data?.code === 200) {
          onSourceDataChange(res.data.data.content);
        }
      });
    };

    const handleTagsChange = async (currentTags: string[], availableTags: string[]) => {
      const params: QaUpdateParams = {
        answer: QAItem.answer,
        id: QAItem.id,
        question: QAItem.question,
        taskId: taskId,
        tags: currentTags,
        optionalTags: availableTags,
      };

      try {
        const res = await changeLabelList(params);
        if (res.data?.code === 200) {
          message.success('标签更新成功');

          // 延迟更新标签列表
          setTimeout(() => {
            onTagListUpdate();
            // // 触发FileLabelView刷新
            // if (onFileLabelRefresh) {
            //   onFileLabelRefresh();
            // }
          }, 800);
        } else {
          message.error('标签更新失败');
        }
      } catch (error) {
        message.error('标签更新失败');
        console.error('标签更新错误:', error);
      }
    };

    return (
      <List.Item
        key={QAItem.id}
        className={expendList === index ? 'task-list-active' : 'task-list-item'}
        style={{
          borderLeft: getBorderStyle(QAItem),
        }}
      >
        <div className={'qa-item-container'}>
          {showCheckbox && <Checkbox checked={checked} onChange={handleCheckboxChange} />}
          <div className="qaListItemDiv" onClick={handleQAClick}>
            <div>
              <div style={{ marginBottom: '0.5rem', width: '90%' }} className="qa-list-item">
                <img src={q} alt="q" />
                <div className={expanded ? '' : 'qa-list-label'}>
                  <HighlightText text={QAItem.question} searchKeyword={filterVal || ''} />
                </div>
              </div>
              <div className="qa-list-item">
                <img src={a} alt="a" />
                <div className={expanded ? '' : 'qa-list-label'}>
                  <HighlightText text={QAItem.answer} searchKeyword={filterVal || ''} />
                </div>
              </div>
            </div>
            {tagsVisible && (
              <div className="qa-tag" onClick={(e) => e.stopPropagation()}>
                <EditableTagGroup
                  tags={QAItem?.tags?.slice(0, 3) || []}
                  availableTags={QAItem?.optionalTags || []}
                  maxTags={3}
                  qaId={QAItem.id}
                  onTagsChange={handleTagsChange}
                />
              </div>
            )}
          </div>
        </div>
      </List.Item>
    );
  };

  return (
    <List
      size="small"
      bordered={false}
      dataSource={displayQaList}
      renderItem={(item, index) => (
        <QADocumentItem key={'QADocumentItem' + index} QAItem={item} index={index} />
      )}
    />
  );
};

export default QAListSection;
