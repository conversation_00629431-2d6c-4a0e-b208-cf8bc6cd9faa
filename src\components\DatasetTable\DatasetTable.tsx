import { forwardRef, useImperativeHandle, useRef } from 'react';
import useDatasetTable from './hooks/useDatasetTable';
import DatasetTableHeader from './DatasetTableHeader';
import DatasetTableContent from './DatasetTableContent';
import UploadErrorModal from '../UploadErrorModal';
import PreviewFolderModal from '../PreviewFolderModal';
import TablePagination from '../TablePagination';
import { DataSetType } from '../../types';

export interface DatasetTableProps {
  ShowActionColumn?: boolean;
  noEditTag?: boolean;
  onSelectRows?: (data: DataSetType[]) => void;
  Extra?: React.ReactNode;
}

const DatasetTable = forwardRef<any, DatasetTableProps>((props, ref) => {
  const {
    headerProps,
    tableProps,
    paginationProps,
    uploadErrorModalProps,
    previewFolderModalProps,
    imperativeMethods,
  } = useDatasetTable(props);

  useImperativeHandle(ref, () => imperativeMethods);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
      <DatasetTableHeader {...headerProps} />
      <DatasetTableContent {...tableProps} />
      <TablePagination {...paginationProps} />
      <UploadErrorModal {...uploadErrorModalProps} />
      <PreviewFolderModal {...previewFolderModalProps} />
    </div>
  );
});

export default DatasetTable;
