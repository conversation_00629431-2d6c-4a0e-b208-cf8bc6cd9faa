# 使用Node.js镜像作为基础镜像
FROM node:18-alpine AS build-env

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package.json和pnpm-lock.yaml文件到工作目录
COPY package.json pnpm-lock.yaml ./

# 安装项目依赖
RUN pnpm install --frozen-lockfile

# 复制React项目文件到工作目录
COPY . .

# 构建React项目（使用vite构建到dist目录）
RUN pnpm run build

# 使用Nginx作为基础镜像
FROM nginx:alpine

# 将React项目静态资源复制到Nginx的默认站点目录（从dist目录）
COPY --from=build-env /app/dist /usr/share/nginx/html

# 暴露Nginx容器的80端口
EXPOSE 80

# 启动Nginx服务
CMD ["nginx", "-g", "daemon off;"]