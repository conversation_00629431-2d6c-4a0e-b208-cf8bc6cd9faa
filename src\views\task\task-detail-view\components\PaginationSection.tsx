import React from 'react';
import { Col } from 'antd';
import TablePagination from '../../../../components/TablePagination';

interface PaginationSectionProps {
  pagination: {
    page: number;
    size: number;
    total: number;
  };
  treeData: any[];
  onPaginationChange: (page: number, size: number) => void;
}

const PaginationSection: React.FC<PaginationSectionProps> = ({
  pagination,
  treeData,
  onPaginationChange,
}) => {
  // 计算span值
  const getSpanValue = () => {
    return treeData.length > 0 &&
      ((treeData[0] as any)?.name.endsWith('.csv') ||
        (treeData[0] as any)?.name.endsWith('.json'))
      ? 24
      : 14;
  };

  return (
    <Col span={getSpanValue()}>
      <TablePagination
        total={pagination?.total}
        pageSize={pagination?.size}
        page={pagination?.page}
        OnChange={onPaginationChange}
      />
    </Col>
  );
};

export default PaginationSection;
