import React, { Children, ReactNode, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Input<PERSON><PERSON><PERSON>, Modal } from 'antd';
import '../css/FineTuneView.css'
interface OpenDrawPop {
  visible: boolean;
  OnClose: (rows?: any[]) => void;
  OnChange?: () => void;
  titltext: string;
  buttontext: string;
  children: React.ReactNode;
  timetext: string;
  onButtonClick: () => void;
  onValueChange?: (value: number | null) => void;
  value?: number | null;
}

const TimeModal: React.FC<OpenDrawPop> = ({
  visible,
  OnClose,
  buttontext,
  titltext,
  children,
  timetext,
  OnChange,
  onButtonClick,
  onValueChange,
  value
}) => {
  const onOk = () => {
    OnClose();
  };
  const onCancel = () => {
    OnClose();
  };

  const [exceedMax, setExceedMax] = useState(false);
  const handleValueChange = (newValue: number | null) => {
    if (newValue) {
      setExceedMax(newValue > 24);
      onValueChange && onValueChange(newValue)
    }
  }
  const drawerContent = (
    <div style={{ display: "flex", justifyContent: "center", alignItems: "center", height: "277px", fontSize: "22px" }}>
      <div style={{ position: "absolute", marginBottom: "52%", marginLeft: "20px", width: "400px" }}>{titltext}</div>
      <Button
        className={exceedMax ? "buttongay" : "buttonblock"}
        // onClick={onButtonClick}
        onClick={() => onButtonClick()} 
        disabled={exceedMax}
      >
        {buttontext}
      </Button>
      <div className={exceedMax ? "timebox" : "timebox2"}>
        <span style={{ width: "96px", margin: "0 30px 0 30px" }}>{timetext}上线时间</span>
        <InputNumber
          style={{ width: "66px", margin: "0 20px 0 0" }}
          min={0.5}
          value={value}
          step={0.5}
          defaultValue={1}
          onChange={handleValueChange}
        />
        <span style={{ width: "32px", marginRight: "35px" }}>小时</span>
        {exceedMax && <span style={{ fontSize: "11px", color: "#F44932", width: "101px" }}>最高不能超过24小时</span>}
      </div>
    </div>
  );

  return (
    <Modal
      width={429}
      centered
      open={visible}
      onCancel={onCancel}
      footer={null}
      maskClosable={false}
    >
      {drawerContent}
      {children}

    </Modal>
  );
}
export default TimeModal;