import { Descriptions } from "antd";
export interface DescriptionsWrapperPrpps {
  children: any;
}

const DescriptionsWrapper: React.FC<DescriptionsWrapperPrpps> = ({
  children,
}) => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <div className="descriptions">
        <Descriptions
          column={1}
          style={{
            padding: "1rem 0rem 2rem 0.6rem",
          }}
          size="small"
          items={children}
        />
      </div>
      <div style={{ position: "absolute", bottom: "46px", right: "25%" }}></div>
    </div>
  );
};
export default DescriptionsWrapper;
