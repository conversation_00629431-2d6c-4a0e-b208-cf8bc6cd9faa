import { AxiosResponse } from "axios";
import { CustomResponseType } from "../types";
import request from "../utils/request";
interface UserInfoResp extends CustomResponseType {
  data: UserInfoData;
}

interface UserInfoData {
  resultMap: ResultMap;
}

export interface ResultMap {
  day: AdditionalProperties;
  month: AdditionalProperties;
  quarter: AdditionalProperties;
  week: AdditionalProperties;
  year: AdditionalProperties;
}

interface AdditionalProperties {
  dateList: string[];
  numList: number[];
}

interface OverviewDataResp extends CustomResponseType {
  data: OverviewData;
}

export interface OverviewData {
  dataGenerateNum: number;
  dataReviewNum: number;
  onlineUserNum: number;
  totalUserNum: number;
}
/**
 * 获取用户信息
 * @returns
 */
export function getAnalyseUserInfo(): Promise<AxiosResponse<UserInfoResp>> {
  const url = `/analyse/userinfo`;
  return request.get(url);
}

/**
 * 获取用户信息
 * @returns
 */
export function getOverview(): Promise<AxiosResponse<OverviewDataResp>> {
  const url = `/analyse/overview`;
  return request.get(url);
}

/**
 * 获取审核信息
 * @returns
 */
export function getAnalysDataReview(): Promise<AxiosResponse<UserInfoResp>> {
  const url = `/analyse/data/review`;
  return request.get(url);
}

/**
 * 获取数据信息
 * @returns
 */
export function getAnalysDataGenerate(): Promise<AxiosResponse<UserInfoResp>> {
  const url = `/analyse/data/generate`;
  return request.get(url);
}

/**
 *
 * @returns
 */
export function getDomain(): Promise<AxiosResponse<any>> {
  const url = `/analyse/domain`;
  return request.get(url);
}
