export const EXAMPLE_MESSAGE = [
  '“你是一个军工领域算法科学家，你要使用资料中的武器装备信息训练领域问答大模型”',
  '“你是一位军事情报分析算法科学家，你要使用资料中的军事情报信息训练领域问答大模型”',
  '“你是一位网络安全算法科学家，你要使用资料中的网络威胁和安全漏洞信息训练领域问答大模型”',
  '“你是一位医学研究算法科学家，你要使用资料中的临床试验数据和医疗诊断信息训练领域问答大模型”',
  '“你是一位生物安全领域的算法科学家，你要使用资料中的生物危害数据信息训练领域问答大模型”',
  '“你是一位医疗设备优化算法科学家，你要使用资料中的医疗设备性能和技术规格信息训练领域问答大模型”',
];

// 工具提示常量
export const TOOLTIP_MESSAGES = {
  REQUIREMENT_DESCRIPTION: '需求描述内的文本均可更改，字数限制600字',
  PARAGRAPH_FINENESS:
    '该参数决定推理过程中每次输入的上下文长度，精细度越高，上下文长度越小，每篇文档分割出的文本段落就越多。',
  QUESTION_DENSITY:
    '该参数决定每段文本推理的问答对数量，密度越大，模型会尽量从每个段落中推理越多的问题和答案。',
};

// 默认值常量
export const DEFAULT_VALUES = {
  TASK_NAME: '',
  MODEL_CONFIG: '自动配置' as const,
  SPLIT_LEVEL: 50,
  QUESTION_DENSITY: 50,
  LABEL_TEXT: '装备维修，运用参考，性能参考',
  AUTO_LABEL: false,
  SELECTED_INDEX: -1,
  SCROLLING: true,
  CURRENT_EXAMPLE: 0,
};

// 验证规则常量
export const VALIDATION_RULES = {
  TASK_NAME: {
    MAX_LENGTH: 32,
    PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9,——()""_]+$/,
    MESSAGE: '仅允许中文、英文、数字及符号 ,——()""_',
  },
  LABEL: {
    PATTERN: /^(?!.*(?:^|[,，])\d+(?:[,，]|$))[\u4e00-\u9fa5a-zA-Z0-9,，]+$/,
    MESSAGE: '标签格式不正确，请勿输入特殊符号或纯数字标签',
    MAX_COUNT: 20,
  },
  DESCRIPTION: {
    MAX_LENGTH: 600,
  },
};

// 滑块配置常量
export const SLIDER_CONFIG = {
  STEP: 25,
  MIN: 0,
  MAX: 100,
  MARKS_COUNT: 5,
};

// 样式常量
export const STYLES = {
  SLIDER: {
    RAIL: {
      height: '6px',
      background: '#F1F6F9',
      borderTop: '1px solid #E1EAEF',
      borderBottom: '1px solid #E1EAEF',
    },
    TRACK: {
      height: '6px',
      background: '#0FB698',
      borderTop: '1px solid #0CA287',
      borderBottom: '1px solid #0CA287',
    },
    WIDTH: '39.25rem',
  },
  TEXTAREA: {
    BACKGROUND_COLOR: 'rgb(242, 246, 249)',
    FONT_SIZE: '16px',
    WIDTH: '58rem',
  },
  SELECTED_ITEM: {
    BACKGROUND_COLOR: 'rgb(234, 247, 245)',
  },
};

// 任务配置映射
export const TASK_CONFIG_MAP = {
  AUTO: {
    splitLevel: 3,
    questionDensity: 3,
  },
};

// 时间间隔常量
export const INTERVALS = {
  SCROLL_INTERVAL: 2000, // 2秒
};

// 按钮文本常量
export const BUTTON_TEXTS = {
  BACK: '返回',
  SELECT_FROM_PLATFORM: '在平台库中选择',
  AUTO_CONFIG: '自动配置',
  MANUAL_CONFIG: '手动配置',
  APPLY: '应用',
  AI_GENERATE: 'AI智能生成',
  START_TASK: '开始任务',
  CREATE_TASK: '创建任务',
};

// 标签常量
export const LABELS = {
  TASK_NAME: '任务名称',
  SOURCE_DATASET: '源数据集',
  MODEL_PARAMS: '模型参数输入',
  PARAGRAPH_FINENESS: '段落精细度',
  QUESTION_DENSITY: '提问密度',
  REQUIREMENT_DESCRIPTION: '描述你的需求',
  TEMPLATE_LIST: '描述模板',
  REQUIREMENT_DESC: '需求描述',
  AUTO_LABEL: '自动标签',
  GENERATE_TRAINING_DATA: '生成训练数据',
};

// 占位符文本常量
export const PLACEHOLDERS = {
  TASK_NAME: '请输入任务名称',
  REQUIREMENT_DESCRIPTION: '请输入需求描述',
  LABEL_INPUT: '请输入分类标签，以逗号分隔。示例:飞机，电磁，坦克..……，请勿使用特殊符号',
};

// 消息文本常量
export const MESSAGES = {
  SUCCESS: {
    LABEL_GENERATED: '标签生成成功！',
  },
  WARNING: {
    SELECT_DATASET_FIRST: '请先选择源数据集',
    LABEL_COUNT_LIMIT: '生成的标签数量不能超过20个',
    NO_RECOMMENDED_LABELS: '暂无推荐标签数据',
  },
  ERROR: {
    LABEL_GENERATION_FAILED: '标签生成失败，请重试',
    INVALID_LABEL_FORMAT: '标签格式不正确，请勿输入特殊符号或纯数字标签',
  },
};
