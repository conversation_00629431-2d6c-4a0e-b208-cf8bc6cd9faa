import { Button, Input, Pagination, Select, Space } from 'antd';
import { useEffect, useState } from 'react';
interface TablePaginationProp {
  total: number;
  pageSize: number;
  page: number;
  OnChange: (page: number, pageSize: number) => void;
}
const TablePagination: React.FC<TablePaginationProp> = ({ total, pageSize, page, OnChange }) => {
  const [pSize, setPSize] = useState(10);
  const [jumper, setJumper] = useState('');
  const [current, setCurrent] = useState(1);
  const [pTotal, setPTotal] = useState(0);
  useEffect(() => {
    setPTotal(total);
  }, [total]);

  useEffect(() => {
    setPSize(pageSize);
  }, [pageSize]);

  useEffect(() => {
    setCurrent(page);
  }, [page]);

  useEffect(() => {
    setJumper(jumper);
  }, [jumper]);

  return (
    <>
      <div className="table-pagination">
        <Space size={12} style={{ marginRight: '48px' }}>
          每页显示
          <Select
            // defaultValue={10}
            value={pSize}
            onChange={(e) => {
              setPSize(e);
              OnChange(1, e);
            }}
            style={{ width: 100, borderRadius: 0 }}
            size="large"
            options={[
              { value: 10, label: '10' },
              { value: 20, label: '20' },
              { value: 50, label: '50' },
              { value: 100, label: '100' },
            ]}
          />
        </Space>
        <Pagination
          showSizeChanger={false}
          pageSize={pSize}
          current={current}
          total={pTotal}
          style={{ marginRight: '10px' }}
          onChange={(page, pageSize) => {
            setCurrent(page);
            OnChange(page, pageSize);
          }}
        />
        {/* <Space>
          跳转至
          <Space.Compact>
            <Input
              style={{ width: 56, borderRadius: 0 }}
              value={jumper}
              onChange={(e) => setJumper(e.target.value)}
              size="large"
            />
            <Button size="large" className="boldText" onClick={() => {
                const p = Number(jumper);
              if (!Number.isNaN(p)) {
                const lastPage = (pTotal / pSize) + (pTotal % pSize > 0 ? 1 : 0);
                const updatePage = p > lastPage ? lastPage : p;
                setCurrent(updatePage);
                OnChange(updatePage, pageSize);
              }
            }}>
              GO
            </Button>
          </Space.Compact>
        </Space> */}
      </div>
    </>
  );
};

export default TablePagination;
