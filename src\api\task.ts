import { Axios, AxiosResponse } from "axios";
import { AlluserInfoType, CreateTaskFieldType, CustomResponseType, ProgressAllInfo, TaskDetailType, TaskFilter, TaskType, deleteillationTaskType, qATaskRetryRequest } from "../types";
import request from "../utils/request";
import { ReviewConfigBtnType, TaskConfigInfo } from "../components/ReviewConfigModal/components/ReviewConfigBtn/type";
import { promises } from "dns";

interface CreateTaskResp extends CustomResponseType {
  taskId: string;
}

export interface GetTaskParams {
  page?: number;
  size?: number;
  sortAttribute?: string;
  sortDirection?: string;
  filterAttribute?: string;
  taskName?: string;
  startTime?: Date;
  endTime?: Date;
}

export interface GetAlluser {
  createTime: string,
  gender: number,
  id: string,
  isDelete: number,
  updateTime: string,
  userAccount: string,
  userAvatar: string,
  userName: string,
  userPassword: string,
  userRole?: string
}

export interface GetViewProgress {
  taskName?: string;
  total?: number;
  totalReviewed?: number;
  curAllocated?: number;
  curReviewed?: number;
  totalProgress?: number;
  curProgress?: number;
  taskId: string;
  userId: string;
}
export interface ProgressId {
  taskId: string;
  userId: string;
}

interface TaskResp extends CustomResponseType {
  data: TaskType[];
  totalCount: number;
}

interface AlluserInfo extends CustomResponseType {
  data: AlluserInfoType[];
}
interface TaskDetail extends CustomResponseType {
  data: TaskDetailType;
}
export interface QaUpdateParams {
  answer: string;
  id: string;
  question: string;
  taskId: string;
  tags: string[];
  optionalTags: string[];
}

export interface TaskProblemDetail {
  "childFileInfoList": TaskProblemFile[],
  "complete": number;
  "id": string;
  "taskCreatorId": string;
  "taskName": string;
  "taskStatus": string;
  "total": number;
}

export interface TaskProblemFile {
  "chileFileName": string;
  "fileId": string;
  "fileStatus": string;
  "fileType": string;
  "id": string;
}

export interface isSetReview extends CustomResponseType{
  data: boolean;
}

interface TaskConfigInfoResp extends CustomResponseType {
  data: TaskConfigInfo;
}

export interface FileTreeNode {
  fileId: string;
  name: string;
  qaCount: number;
  type: string;
  children?: FileTreeNode[];
}

interface FileTreeResp extends CustomResponseType {
  data: {
    datasetId: string;
    fileTreeNodeForTask: FileTreeNode;
  }[];
}

interface TaskIsSetReviewConfigResp extends CustomResponseType {
  data: boolean;
}

/**
 * 新建任务
 * @param params 新建任务
 * @returns
 */
export function createTask(
  params: CreateTaskFieldType
): Promise<AxiosResponse<CreateTaskResp>> {
  const url = `/task`;
  return request.post(url, params);
}

/**
 * 获取任务
 * @param params 新建任务
 * @returns
 */
export function getTasks(
  params: GetTaskParams
): Promise<AxiosResponse<TaskResp>> {
  if (params.page && params.size) {
    let url = `/task/list?page=${params.page}&size=${params.size}`;
  url = params.sortAttribute ? url + `&sortAttribute=${params.sortAttribute}` : url;
  url = params.sortDirection ? url + `&sortDirection=${params.sortDirection}` : url;
  url = params.taskName ? url + `&taskName=${params.taskName}` : url;
  url = params.startTime ? url + `&startTime=${params.startTime}` : url;
  url = params.endTime ? url + `&endTime=${params.endTime}` : url;
  return request.get(url);
  }else{
    return request.get(`/task/list?size=${params.size}`);
  }
}

/**
 * 获取审核进度
 * @param params
 * @returns
 */
export async function querygetProgress(
  //params: ProgressId
  taskId: string,
): Promise<AxiosResponse<ProgressAllInfo> | undefined> {
  try {
    const url = `/review/progress/all?taskId=${taskId}`;
    const res = await request.get(url);
    return res.data;
  } catch (e) {
    return undefined;
  }

}

/**
 * 获取所有用户信息
 * @param taskId 需要获取详情的任务ID
 * @returns
 */
export async function queryUserDetail(
  taskId: string
): Promise<AxiosResponse<AlluserInfo>> {
  const url = (`/review/reviewer/list?taskId=${taskId}`);
  const res = await request.get(url)
  return res.data;
}

/**
 * 删除任务
 * @param deleteIds 需要删除的任务ID
 * @returns
 */

export function deleteTask(deleteIds: string[]) {
  return request.delete("/task", { data: { deleteIds } });
}

/**
 * 获取任务详情
 * @param taskId 需要获取详情的任务ID
 * @returns
 */
export function getTaskDetail(
  taskId: string
): Promise<AxiosResponse<TaskDetail>> {
  return request.get(`/task?taskId=${taskId}`);
}

/**
 * 任务重命名
 * @param taskId 需要重命名任务ID
 * @param taskName 需要重命名任务名字
 * @returns
 */
export function renameTask(
  taskId: string,
  taskName: string
): Promise<AxiosResponse<any>> {
  const url = `/task?taskId=${taskId}&taskName=${taskName}`;
  return request.put(url, {});
}

/**
 * 任务问题详情
 * @param taskId 需要获取任务问题详情的任务ID
 * @returns
 */
export function getTaskProblemDetail(
  taskId: string
): Promise<AxiosResponse<any>> {
  return request.get(`/task/problem?taskId=${taskId}`);
}

/**
 * 重新解析
 * @param taskId 需要重新解析任务ID
 * @returns
 */
export function retryTask(
  taskId: string
): Promise<AxiosResponse<any>> {
  const url = `/task/${taskId}/retry`;
  return request.get(url);
}

/**
 * 获取审核配置信息
 * @param taskId 任务ID
 * @returns
 */
export function getReviewConfigInfo(
  taskId: string
): Promise<AxiosResponse<TaskConfigInfoResp>> {
  const url = `/task/review-config?taskId=${taskId}`;
  return request.get(url);
}

/**
 * 是否配置
 * @param taskId 任务ID
 * @returns
 */
export function isSetReviewConfigInfo(
  taskId: string
): Promise<AxiosResponse<isSetReview>> {
  const url = `/task/review-config/is-set?taskId=${taskId}`;
  return request.get(url);
}

/**
 * 更新审核配置信息
 * @param params 任务信息
 * @returns
 */
export function setReviewConfigInfo(
  params: any
): Promise<AxiosResponse<CustomResponseType>> {
  const url = `/task/review-config`;
  return request.post(url, params);
}

/**
 * 获取任务文件树
 * @param taskId 任务Id
 * @returns
 */
export function getQATaskFileTree(
  taskId: string
): Promise<AxiosResponse<FileTreeResp>> {
  const url = `/task/tree?taskId=${taskId}`;
  return request.get(url);
}

// 获取标签
// export function getTaglList(taskId:string):Promise<AxiosResponse>{
//   const url = `/task/tags-statistics/${taskId}`;
//   return request.get(url)
// }

export function flattenFileTree(fileTree: FileTreeNode[]): FileTreeNode[] {
  const flattenedTree: FileTreeNode[] = [];

  function flatten(node: FileTreeNode) {
    flattenedTree.push({
      fileId: node.fileId,
      name: node.name,
      qaCount: node.qaCount,
      type: node.type,
    });

    if (node.children) {
      node.children.forEach(flatten);
    }
  }

  fileTree.forEach(flatten);

  return flattenedTree;
}

/**
 * 获取任务是否配置审核信息
 * @param taskId 任务ID
 * @returns
 */
export function getTaskIsSetReviewConfig(
  taskId: string
): Promise<AxiosResponse<TaskIsSetReviewConfigResp>> {
  const url = `/task/review-config/is-set?taskId=${taskId}`;
  return request.get(url);
}

/**
 * 重新推理
 * @param param 需要推理解析任务
 * @returns
 */
export function reillationTask(
  param: qATaskRetryRequest
):Promise<AxiosResponse<CreateTaskResp>>{
  const url = `/task/problem`
  return request.post(url, param);
}

/**
 * 删除错误任务
 * @param param 需要删除错误任务
 * @returns
 */
export function deleteillationTask(
  param:deleteillationTaskType
  ): Promise<AxiosResponse<CreateTaskResp>> {
    const url = `/task/problem`;
    return request.delete(url,{ data: param });
  }

 /**
 * 获取所有模板
 */
export function getAllTemplates():Promise<AxiosResponse>{
const url = `/templates/all`;
return request.get(url)
}
/* 
下载模板文件
*/
export function downloadTemplate():Promise<AxiosResponse>{
  const url = `/task/download-template`
  return request.get(url,{
    responseType:"blob"
  })
}
/* 
上传文件
*/
export interface uploadFileParams{
  taskName?:String,
  importQAs:any
}
export function uploadFileText(
  param:uploadFileParams
){
const formData = new FormData();
formData.append('importQAs',param.importQAs);
if (param.taskName) {
  const taskName = param.taskName.replace(/^"|"$/g, "")
  formData.append("taskName",taskName);
}
 
return request.post('/task/import',formData,{
  headers:{
    "Content-Type":"multipart/form-data"
  },
})
}
// 获取任务进度
export function getTaskProcess(taskId:string):Promise<AxiosResponse>{
  const url = `/task/qaTask/progress/${taskId}`;
  return request.get(url)
}

//获取标签
export function getLabelList(datasetId:string[]):Promise<AxiosResponse>{
  const url = `/dataset/generateTags?fileIds=${datasetId}`;
  return request.post(url)
}

//修改标签
export function changeLabelList(params:QaUpdateParams):Promise<AxiosResponse>{
  const url = `/qa`;
  return request.put(url,params)
}