import React from 'react';
import { Form, Card, List, Button, Tooltip, Input } from 'antd';
import { TemplateType, CreateTaskFormType } from '../types';
import { LABELS, TOOLTIP_MESSAGES, BUTTON_TEXTS, PLACEHOLDERS, STYLES } from '../constants';
import infoIcon from '../../../../assets/img/info-icon.svg';

const { TextArea } = Input;

interface RequirementDescriptionSectionProps {
  exampleText: TemplateType[];
  selectedIndex: number;
  textValue: string;
  onItemClick: (index: number) => void;
  onApplyTemplate: (item: TemplateType, index: number) => void;
  onTextChange: (text: string) => void;
}

const RequirementDescriptionSection: React.FC<RequirementDescriptionSectionProps> = ({
  exampleText,
  selectedIndex,
  textValue,
  onItemClick,
  onApplyTemplate,
  onTextChange,
}) => {
  return (
    <Form.Item<CreateTaskFormType> name="domain" label={LABELS.REQUIREMENT_DESCRIPTION}>
      <div>
        <Card style={{ display: 'flex', margin: '0', padding: '0' }}>
          <List
            header={<div>{LABELS.TEMPLATE_LIST}</div>}
            className="listStyle"
            bordered
            dataSource={exampleText}
            renderItem={(item, index) => (
              <List.Item
                key={index}
                style={{
                  backgroundColor:
                    selectedIndex === index ? STYLES.SELECTED_ITEM.BACKGROUND_COLOR : 'initial',
                }}
                onClick={() => onItemClick(index)}
              >
                {item.templateName}
                {selectedIndex === index && (
                  <Button
                    type="link"
                    size="small"
                    className="show-btn"
                    onClick={() => onApplyTemplate(item, index)}
                  >
                    {BUTTON_TEXTS.APPLY}
                  </Button>
                )}
              </List.Item>
            )}
          />
          <div className="context">
            <p>
              {LABELS.REQUIREMENT_DESC}{' '}
              <Tooltip title={TOOLTIP_MESSAGES.REQUIREMENT_DESCRIPTION}>
                <img src={infoIcon} style={{ width: '16px', height: '16px' }} />
              </Tooltip>
            </p>
            <TextArea
              value={textValue}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                onTextChange(e.target.value);
              }}
              placeholder={PLACEHOLDERS.REQUIREMENT_DESCRIPTION}
              autoSize={{ minRows: 10, maxRows: 13 }}
              showCount
              maxLength={600}
              style={{
                width: STYLES.TEXTAREA.WIDTH,
                backgroundColor: STYLES.TEXTAREA.BACKGROUND_COLOR,
                fontSize: STYLES.TEXTAREA.FONT_SIZE,
              }}
            />
          </div>
        </Card>
      </div>
    </Form.Item>
  );
};

export default RequirementDescriptionSection;
