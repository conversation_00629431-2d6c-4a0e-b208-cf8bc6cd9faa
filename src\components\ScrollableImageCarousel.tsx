import React, { useState, useEffect, useRef } from 'react';

interface ScrollableImageCarouselProps {
  images: string[];
  scrollSpeed: number;
}

const ScrollableImageCarousel: React.FC<ScrollableImageCarouselProps> = ({ images, scrollSpeed }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrolling, setScrolling] = useState(true);

  useEffect(() => {
    const container = containerRef.current;

    const handleMouseEnter = () => {
      setScrolling(false);
    };

    const handleMouseLeave = () => {
      setScrolling(true);
    };

    if (container) {
      container.addEventListener('mouseenter', handleMouseEnter);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (container) {
        container.removeEventListener('mouseenter', handleMouseEnter);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (scrolling) {
      interval = setInterval(() => {
        if (containerRef.current) {
          containerRef.current.scrollLeft += 1;
          if (containerRef.current.scrollLeft >= containerRef.current.scrollWidth / 2) {
            const firstChild = containerRef.current.parentNode?.firstChild;
            if (firstChild) {
              containerRef.current.parentNode?.appendChild(firstChild);
              containerRef.current.scrollLeft = 0;
            }
          }
        }
      }, scrollSpeed);
    }

    return () => clearInterval(interval);
  }, [scrolling, scrollSpeed]);

  return (
    <div
      ref={containerRef}
      style={{
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        height: '440px',
      }}
    >
      {images.map((image, index) => (
        <img
          key={index}
          src={image}
          alt={`Image ${index}`}
          style={{
            display: 'inline-block',
            width: 'auto',
            height: '100%',
            marginRight: '16px',
          }}
        />
      ))}
            {images.map((image, index) => (
        <img
          key={index + 6}
          src={image}
          alt={`Image ${index}`}
          style={{
            display: 'inline-block',
            width: 'auto',
            height: '100%',
            marginRight: '16px',
          }}
        />
      ))}
    </div>
  );
};

export default ScrollableImageCarousel;
