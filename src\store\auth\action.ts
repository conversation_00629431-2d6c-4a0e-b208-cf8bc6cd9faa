import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { Dispatch, useEffect, useState } from "react";
import { login, register, verifyCodeLogin } from "../../api/auth";
import { CustomResponseType, LoginParams, RegisterParams, UserInfoParams, VerifyCodeLoginParams } from "../../types";
import { SET_TOKEN, SET_USER } from "../Type";
import { message } from "antd";
import request from "../../utils/request";
import Cookies from 'js-cookie';

interface UserInfoType {
  id?: string;
  name: string;
  phone: string;
  job: string;
  avatar: string;
  ponits: number;
  // 积分
}

		
interface UserInfo {
  userName: string;
  email: string;
  role: string;
}

interface ReType extends CustomResponseType {
  token: string;
  user: UserInfoType;
  
}


export function defaultLogin(data: LoginParams, callback?: Function) {
  // 返回一个有默认参数dispatch函数
  return (dispatch: Dispatch<any>) => {
    // 执行登录
    login(data)
      // 返回参数 是AxiosResponse有个泛型是定义data
      // LoginResponseType定义AxiosResponse的data类型
      // LoginResponseType 登录返回类型 AxiosResponse axios返回类型
      .then((res: AxiosResponse<ReType>) => {
        if (res?.data?.code === 200) {
          // 执行本地的存储
          Cookies.set('token', res.data.token);
        
          // 执行reducer
          dispatch({ type: SET_TOKEN, payload: res.data.token });
          //dispatch({ type: SET_USER, payload: res.data.user });
          
          // 实现跳转
          if (callback) {
            callback();
          }
        }else {
          message.error(res?.data?.message);
        }
      });
  };
  // return (dispatch: Dispatch<any>) => {
  //   dispatch({ type: SET_TOKEN, payload: 'token' });
  //   dispatch({ type: SET_USER, payload: { name: 'Starling', phone: '12345678910', job: 'PG', avatar: '/avatar-1.png', point: 10000 } });
  //         sessionStorage.setItem("token", '11111111111');
  //         sessionStorage.setItem("userInfo", JSON.stringify({ name: 'Starling', phone: '12345678910', job: 'PG', avatar: '/avatar-1.png', point: 10000 }));
  //   if (callback) {
  //     callback();
  //   }
  // }
}

export function verifyLogin(data: VerifyCodeLoginParams, callback?: Function) {
  // 返回一个有默认参数dispatch函数
  return (dispatch: Dispatch<any>) => {
    // 执行登录
    verifyCodeLogin(data)
      // 返回参数 是AxiosResponse有个泛型是定义data
      // LoginResponseType定义AxiosResponse的data类型
      // LoginResponseType 登录返回类型 AxiosResponse axios返回类型
      .then((res: AxiosResponse<ReType>) => {
        if (res?.data?.code === 200) {
          // 执行本地的存储
          Cookies.set('token', res.data.token);
          // 执行reducer
          dispatch({ type: SET_TOKEN, payload: res.data.token });
          // dispatch({ type: SET_USER, payload: res.data.user });
          // 实现跳转
          if (callback) {
            callback();
          }
        }
      });
  };
}

export function registerUser(data: RegisterParams, callback?: Function) {
  // 返回一个有默认参数dispatch函数
  return (dispatch: Dispatch<any>) => {
    // 执行登录
    register(data)
      // 返回参数 是AxiosResponse有个泛型是定义data
      // LoginResponseType定义AxiosResponse的data类型
      // LoginResponseType 登录返回类型 AxiosResponse axios返回类型
      .then((res: AxiosResponse<ReType>) => {
        if (res?.data?.code === 200) {
          // 执行本地的存储
          Cookies.set('token', res.data.token);
          // 执行reducer
          dispatch({ type: SET_TOKEN, payload: res.data.token });
          // dispatch({ type: SET_USER, payload: res.data.user });
          // 实现跳转
          if (callback) {
            callback();
          }
        }
      });
  };
}
