import { SET_USER_LIST } from "../Type"
import { UserType } from "../../api/user"
import { ActionType } from "../../types";

const initialState = {
  userList: [],
};
// 仓库数据处理器（state数据=初始化数据,action处理动作）
function reducer(state = initialState, action: ActionType) {
  // 根据动作的类型做不同的数据处理
  switch (action.type) {
    case SET_USER_LIST:
      return { ...state, userList: action.payload };
    default:
      // 默认直接返回state
      return state;
  }
}

export default reducer