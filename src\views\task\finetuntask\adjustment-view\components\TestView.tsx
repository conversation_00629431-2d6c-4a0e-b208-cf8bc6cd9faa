import { useRef, useEffect } from "react";
import { LeftOutlined, SendOutlined } from "@ant-design/icons";
import "../../../../../css/AdjustmentView.css";
import { Button, Space } from "antd";
import Scrollbar from "react-scrollbars-custom";
import TextArea from "antd/es/input/TextArea";
import group310 from "../../../../../assets/img/group-310.svg";
import ChatMessage from "../../ChatMessage";
import { ChatMessageType, ChatType, PlanType } from "../../type";

export interface TestViewProps {
  messageList: ChatMessageType[];
  onSendMessage: () => void;
  setMessageInput: (value: string) => void;
  messageInput: string;
  isLocked: boolean;
  handleNewConversation: () => void;
}

const TestView: React.FC<TestViewProps> = ({
  messageList,
  isLocked,
  setMessageInput,
  onSendMessage,
  messageInput,
  handleNewConversation,
}) => {
  useEffect(() => {
    scrollChatToBottom();
  }, [messageList]);
  const scrollbarRef = useRef(null);
  const scrollChatToBottom = () => {
    if (scrollbarRef.current) {
      (scrollbarRef.current as any).scrollToBottom();
    }
  };
  const handleTextAreaEnter = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (isLocked) {
      e.preventDefault();
      console.log("请等待回答完成后再发送消息！");
      return;
    }

    if (e.key === "Enter") {
      if (e.shiftKey) return; // Shift+Enter默认换行
      e.preventDefault();
      onSendMessage();
      setMessageInput("");
    }
  };
  return (
    <div className="right">
      <div className="headTitle">调试预览</div>
      <Button className="newtalk" type="link" onClick={handleNewConversation}>
        新的对话
      </Button>
      <div className="talkcontent">
        <Scrollbar ref={scrollbarRef}>
          <Space
            size={16}
            // style={{ paddingBottom: "80px" }}
            direction="vertical"
          >
            <div className="chat-message-container">
              <img className="avatar-icon" src={group310} />
              <div style={{ marginLeft: "20px" }}>军事智能专家</div>
            </div>
            <div className="chat-message-ai">
              您好，我是军事智能助手有什么可以帮助您
            </div>
            {messageList?.map((mes) => (
              <>
                {mes.role === "user" ? (
                  <ChatMessage fromUser={true} text={mes.content} />
                ) : (
                  <>
                    <div className="chat-message-container">
                      <img className="avatar-icon" src={group310} />
                      <div style={{ marginLeft: "20px" }}>军事智能专家</div>
                    </div>
                    <div className="chat-message-ai">{mes.content}</div>
                  </>
                )}
              </>
            ))}
          </Space>
        </Scrollbar>  
      </div>
         <div className="chatTalk">
          <TextArea
            style={{
              border: "none",
              boxShadow: "none",
              maxHeight: "20px",
            }}
            placeholder={isLocked ? "正在回答..." : "请输入问题..."}
            autoSize
            onPressEnter={(e) => handleTextAreaEnter(e)}
            onChange={(e) => setMessageInput(e.target.value)}
            value={messageInput}
          />
        </div>
        <Button
          className="sendButton"
          type="text"
          disabled={isLocked}
          onClick={() => {
            onSendMessage();
          }}
        >
          <SendOutlined />
        </Button>
    </div>
  );
};

export default TestView;
