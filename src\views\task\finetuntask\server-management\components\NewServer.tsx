import React, { useState } from "react";
import {
  Button,
  Input,
  Upload,
  UploadProps,
  Space,
  Form,
  InputNumber,
  message,
} from "antd";
import "../../../../../css/uploadBaseModel.css";
import { LeftOutlined, UploadOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { ServerConfiguration } from "../data";
import { createServer } from "../../../../../api/server";
const NewServer: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<ServerConfiguration>();
  const [serverConfig, setServerConfig] = useState<ServerConfiguration>({
    cpuInfo: "",
    description: "",
    gpuInfo: "",
    id: 0,
    memoryInfo: "",
    resourceUrl: "",
    serverName: "",
  });
  const validateIPAddress = (_: unknown, value: string) => {
    if (!value) {
      return Promise.reject(new Error("请输入服务器IP"));
    }

    // IPv4 正则表达式
    const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipPattern.test(value)) {
      return Promise.reject(new Error("请输入有效的IPv4地址"));
    }

    // 检查每个部分是否在 0-255 范围内
    const parts = value.split(".");
    if (
      parts.some((part) => parseInt(part, 10) > 255 || parseInt(part, 10) < 0)
    ) {
      return Promise.reject(new Error("请输入有效的IPv4地址"));
    }

    return Promise.resolve();
  };
  const { TextArea } = Input;

  const updateServerConfig = (key: keyof ServerConfiguration, value: any) => {
    setServerConfig((prevConfig) => ({
      ...prevConfig,
      [key]: value,
    }));
  };

  const handleUpload = async () => {
    try {
      // 触发表单校验
      await form.validateFields();
      // 如果校验通过，调用 createServer 方法并传入 serverConfig
      const res = await createServer(serverConfig);
      if (res.code === 200) {
        message.success("上传成功");
        navigate(-1);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error("上传失败:", error);
    }
  };
  const cancel = () => {
    navigate(-1);
  };

  return (
    <div className="uploadBaseModel">
      <Space size={20} style={{ display: "inline-flex", alignItems: "center" }}>
        <Button
          style={{ fontSize: "12px", width: "36px", height: "36px" }}
          shape="circle"
          icon={<LeftOutlined />}
          onClick={() => navigate(-1)}
        />
        <div
          className="mediumText"
          style={{ fontSize: "20px", lineHeight: "36px", fontWeight: "500" }}
        >
          添加服务器
        </div>
      </Space>
      <div className="uploadModelArea">
        <Form
          form={form}
          className="reqularText"
          name="uploadModelForm"
          autoComplete="off"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18, offset: 2 }}
        >
          <Form.Item<ServerConfiguration>
            name="serverName"
            label="服务器名称:"
            rules={[{ required: true, message: "请选择上传文件" }]}
          >
            <Input
              placeholder="请输入服务器名称"
              value={serverConfig.resourceUrl}
              onChange={(e) => updateServerConfig("serverName", e.target.value)}
              allowClear
              size="large"
              style={{ width: "511px" }}
            />
          </Form.Item>
          <Form.Item<ServerConfiguration>
            name="resourceUrl"
            label="服务器IP:"
            rules={[{ required: true, message: "请输入服务器IP" }]}
          >
            <Input
              placeholder="http://xz1.puhuacloud.com:17305"
              value={serverConfig.resourceUrl}
              onChange={(e) =>
                updateServerConfig("resourceUrl", e.target.value)
              }
              allowClear
              size="large"
              style={{ width: "511px" }}
            />
          </Form.Item>
          <Form.Item<ServerConfiguration>
            name="cpuInfo"
            label="CPU核数:"
            rules={[{ required: false, message: "请输入CPU核数" }]}
          >
            <Input
              placeholder="请输入CPU核数"
              value={serverConfig.cpuInfo}
              onChange={(e) => updateServerConfig("cpuInfo", e.target.value)}
              allowClear
              size="large"
              style={{ width: "511px" }}
            />
          </Form.Item>
          <Form.Item<ServerConfiguration>
            name="gpuInfo"
            label="GPU信息:"
            rules={[{ required: false, message: "请输入GPU信息" }]}
          >
            <Input
              value={serverConfig.gpuInfo}
              onChange={(e) => updateServerConfig("gpuInfo", e.target.value)}
              placeholder="请输入GPU信息"
              size="large"
              style={{ width: "511px" }}
            />
          </Form.Item>
          <Form.Item<ServerConfiguration>
            name="memoryInfo"
            label="内存信息:"
            rules={[{ required: false, message: "请输入内存信息" }]}
          >
            <Input
              placeholder="请输入内存信息"
              value={serverConfig.memoryInfo}
              onChange={(e) => updateServerConfig("memoryInfo", e.target.value)}
              allowClear
              size="large"
              style={{ width: "511px" }}
            />
          </Form.Item>
          <Form.Item<ServerConfiguration>
            name="description"
            label="服务器描述:"
            rules={[{ required: false, message: "请输入服务器描述" }]}
          >
            <TextArea
              value={serverConfig.description}
              onChange={(e) =>
                updateServerConfig("description", e.target.value)
              }
              placeholder="请输入服务器描述"
              rows={4}
              allowClear
              size="large"
              style={{ width: "511px" }}
            />
          </Form.Item>
          <Form.Item style={{ textAlign: "center", marginTop: "40px" }}>
            <Button
              shape="round"
              className="cancalBut"
              size="large"
              style={{ marginRight: "60px" }}
              onClick={cancel}
            >
              取消
            </Button>
            <Button
              shape="round"
              htmlType="submit"
              className="submBut"
              size="large"
              style={{ marginLeft: "60px" }}
              onClick={handleUpload}
            >
              确认添加
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default NewServer;
