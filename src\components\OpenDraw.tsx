import React, { Children, ReactNode, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Toolt<PERSON> } from 'antd';
import infoIcon from "../assets/img/info-icon.svg";
import logoX from "../assets/img/logoX.png";

interface OpenDrawPop {
  rowData?: any[];
  visible: boolean;
  OnClose: (rows?: any[]) => void;
  OnChange?: () => void;
  titltext: string;
  buttontext: string;
  buttontext1?: string;
  moduletext?: string;
  interruptText?: string;
  isInterruptText?: boolean;
  logo?: boolean;
  children: React.ReactNode;
  text?: boolean;
  isbutton?: boolean;
  onButtonClick: () => void;
  onButtonClick2?: () => void;
  deleteClick?: () => void;
  isDelete?: boolean;
  tooltip?: React.ReactNode;
}

const OpenDraw: React.FC<OpenDrawPop> = ({
  rowData,
  visible,
  OnClose,
  OnChange,
  buttontext,
  buttontext1,
  moduletext,
  interruptText,
  titltext,
  children,
  logo,
  text,
  isInterruptText,
  isbutton,
  onButtonClick,
  onButtonClick2,
  deleteClick,
  isDelete,
  tooltip
}) => {
  const onOk = () => {
    OnClose();
  };
  const onCancel = () => {
    OnClose();
  };
  console.log('isDelete', isDelete);

  const drawerContent = (
    <div style={{ display: "flex", justifyContent: "center", alignItems: "center", fontSize: "22px" }}>
      <div style={{ marginLeft: "10px", marginRight: "auto" }}>{titltext}</div>
      <span>
        {logo && <img src={logo ? logoX : ''} alt="Logo" />}
      </span>
      {text && <div className='moduletext'>选择基于 {moduletext} 的大模型进行微调训练</div>}
      {isInterruptText && <div className='moduletextone'> {interruptText}</div>}
      <div style={{ display: "flex", justifyContent: "space-evenly", width: "100%", bottom: "34px", position: "absolute" }}>
        {
          isbutton && <Button
            style={{
              width: "160px",
              height: "40px",
              background: "#787D85",
              borderRadius: "59px 59px 59px 59px",
              fontWeight: "bold",
              color: "#FFFFFF"
            }}
            onClick={onButtonClick2}
          >
            {buttontext1}
          </Button>}

        <Button
          style={{
            width: "160px",
            height: "40px",

            background: "#111111",
            borderRadius: "59px 59px 59px 59px",
            fontWeight: "bold",
            color: "#FFFFFF"
          }}
          onClick={onButtonClick}
        >
          {buttontext}
        </Button>
              {tooltip && <div style={{ position: "absolute", right: "54%" }}>
          <Tooltip title={tooltip}>
            <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
          </Tooltip>
        </div>}
        {isDelete && <Button
          style={{
            width: "160px",
            height: "40px",

            background: "#111111",
            borderRadius: "59px 59px 59px 59px",
            fontWeight: "bold",
            color: "#FFFFFF"
          }}
          onClick={deleteClick}
        >
          模型删除
        </Button>
        }
      </div>
    </div>
  );

  return (
    <Drawer
      width={"35%"}
      closable={false}
      open={visible}
      onClose={onOk}
    >
      {drawerContent}
      {children}

    </Drawer>
  );
}
export default OpenDraw;