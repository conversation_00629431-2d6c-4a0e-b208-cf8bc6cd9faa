.config-btn-container .config-btn-add {
    display: none;
}

.config-btn-item {
    position: relative;
}

.config-delte-btn {
    display: none;
    position: absolute;
    right: -2px;
    top: -4px;
}

.config-edit-btn {
    display: none;
    position: absolute;
    right: -32px;
    top: 6px;
}

.config-btn-item:hover .config-delte-btn,
.config-btn-item:hover .config-edit-btn,
.config-btn-container:hover .config-btn-add {
    display: block;
}


.config-btn-item .ant-space-item {
    width: 80px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.config-edit-input {
    width: 150px;
    border-radius: 8px;
    border: 1px solid #D7DDE5;
    background: #FFF;
}

.color-picker-icon {
    padding: 0 4px !important;
}
.config-btn-container .ant-space-item {
    width: 80px !important;
}