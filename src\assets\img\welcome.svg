<svg width="842" height="1040" viewBox="0 0 842 1040" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_566_1333" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="842" height="1040">
<rect width="842" height="1040" rx="24" fill="url(#paint0_angular_566_1333)"/>
</mask>
<g mask="url(#mask0_566_1333)">
<g filter="url(#filter0_b_566_1333)">
<rect width="842" height="1040" rx="24" fill="url(#paint1_angular_566_1333)"/>
</g>
<g filter="url(#filter1_f_566_1333)">
<circle cx="258" cy="943" r="214" fill="url(#paint2_linear_566_1333)"/>
</g>
</g>
<defs>
<filter id="filter0_b_566_1333" x="-36" y="-36" width="914" height="1112" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="18"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_566_1333"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_566_1333" result="shape"/>
</filter>
<filter id="filter1_f_566_1333" x="-80" y="605" width="676" height="676" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="62" result="effect1_foregroundBlur_566_1333"/>
</filter>
<radialGradient id="paint0_angular_566_1333" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(35.9604 552.5) rotate(-89.1815) scale(690.695 21.9041)">
<stop offset="0.203125" stop-color="#111111"/>
<stop offset="0.567708" stop-color="#10816C"/>
<stop offset="0.713542" stop-color="white"/>
<stop offset="0.875" stop-color="#903EF9"/>
<stop offset="0.953125" stop-color="#7A82C1"/>
<stop offset="1" stop-color="#1F2237"/>
</radialGradient>
<radialGradient id="paint1_angular_566_1333" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(210 949) rotate(-98.5876) scale(1099.45 834.445)">
<stop offset="0.00549534" stop-color="#1F2237"/>
<stop offset="0.0259474" stop-color="#1A1B28"/>
<stop offset="0.149044"/>
<stop offset="0.239993" stop-color="#111111"/>
<stop offset="0.444602" stop-color="#108570"/>
<stop offset="0.65625" stop-color="white"/>
<stop offset="0.776052" stop-color="#0F9C82"/>
<stop offset="0.823151" stop-color="#903EF9"/>
<stop offset="0.918658" stop-color="#9198CE"/>
</radialGradient>
<linearGradient id="paint2_linear_566_1333" x1="245.5" y1="756" x2="112" y2="973.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#111111"/>
<stop offset="0.370581" stop-color="#222F52"/>
<stop offset="0.807289" stop-color="#2AC0A5"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
