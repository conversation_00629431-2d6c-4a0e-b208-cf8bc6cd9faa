import { useRef, useState } from 'react';
import '../../css/SourceDatasetView.css';
import DatasetTable from '../../components/DatasetTable/DatasetTable';
import UploadErrorModal from '../../components/UploadErrorModal';
import { Scrollbar } from 'react-scrollbars-custom';
import UploadDatasetModal from '../../components/UploadDatasetModal';
import { DataSetType } from '../../types';
import DatasetTableExtraActions from '@/components/DatasetTable/DatasetTableExtraActions';
const SourceDatasetView: React.FC = () => {
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  const [uploadDatasetModal, setUploadDatasetModal] = useState(false);
  const [selectedRows, setSelectedRows] = useState<DataSetType[]>([]);
  const tableRef = useRef(null);
  return (
    <Scrollbar>
      <div className="createTaskContent">
        <div
          className="mediumText"
          style={{
            fontSize: '28px',
            lineHeight: '36px',
            fontWeight: '500',
            marginLeft: '2rem',
          }}
        >
          源数据集
        </div>
        <div className="createTaskArea">
          <DatasetTable
            ref={tableRef}
            onSelectRows={(data: DataSetType[]) => setSelectedRows(data)}
            ShowActionColumn={true}
            Extra={
              <DatasetTableExtraActions
                selectedRows={selectedRows}
                setUploadDatasetModal={setUploadDatasetModal}
                tableRef={tableRef}
              />
            }
          />
        </div>
        <UploadDatasetModal
          visible={uploadDatasetModal}
          OnClose={() => {
            setUploadDatasetModal(false);
            if (tableRef.current) {
              (tableRef.current as any).onRefresh();
            }
          }}
        ></UploadDatasetModal>
        <UploadErrorModal
          visible={uploadErrorModal}
          OnClose={(rows) => {
            console.log(rows);
            setUploadErrorModal(false);
          }}
        ></UploadErrorModal>
      </div>
    </Scrollbar>
  );
};

export default SourceDatasetView;
