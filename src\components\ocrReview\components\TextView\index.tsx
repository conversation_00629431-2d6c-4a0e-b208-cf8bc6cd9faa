import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
// import Scrollbar from 'react-perfect-scrollbar';
import { Scrollbars } from 'react-custom-scrollbars';
import { OCRFilePath } from '../ResultView/type';
import './index.module.css';
import { minioUrl } from '../ResultView';

interface TextViewProp {
  filePath: any;
}

interface ImgIndexType {
  start: number;
  end: number;
}
const TextView = forwardRef((props: TextViewProp, ref) => {
  const { filePath } = props;
  // let totalHeight: ComputedRef<string>;
  // let visibleImages: ComputedRef<any>;
  const [totalHeight, setTotalHeight] = useState<string>();
  const [visibleImages, setVisibleImages] = useState<OCRFilePath[]>([]);
  // const [startIndex, setStartIndex] = useState<number>(0);
  // const [endIndex, setEndIndex] = useState<number>(1);
  const [imgIndex, setImgIndex] = useState<ImgIndexType>({ start: 0, end: 1 });
  const imageHeight = 794.41;
  const scrollbarRef = useRef<any>();
  const imgHeight = 794.41;
  const imgWeight = 564;
  const canvasRef = useRef<HTMLCanvasElement>(null);
  let ticking = false;
  //   const [curScrollTop, setCurScrollTop] = useState<number>(0);
  let defaulthOffset = 0;
  let curScrollTop = 0;
  let mainScrollTop = 0;
  let onDrawFlag = false;
  let onDrawIndex = 0;
  let onDrawData: Set<{ [index: number]: number[][] }> = new Set<{ [index: number]: number[][] }>();

  useEffect(() => {
    setTotalHeight(`${imageHeight * filePath.length || 0}px`);
    setVisibleImages(filePath?.slice(imgIndex.start, imgIndex.end + 1));
  }, [filePath]);

  useEffect(() => {
    setVisibleImages(filePath?.slice(imgIndex.start, imgIndex.end + 1));
  }, [imgIndex]);

  useEffect(() => {
    const main = document.getElementsByTagName('main')[0];
    // main.addLin|
    main.addEventListener('scroll', onClear);
  }, []);

  const onClear = () => {
    const ctx = canvasRef.current?.getContext('2d');
    ctx?.clearRect(0, 0, imgWeight, imgHeight);
    ctx?.beginPath();
  };

  const handleScroll = (event: any) => {
    scrollbarRef.current!.scrollTop(curScrollTop + event.deltaY);
    console.log(event);
  };

  const onDraw = (data: Set<{ [index: number]: number[][] }>) => {
    if (!Array.from(data)[0]) return;
    const index = Number(Object.keys(Array.from(data)[0])[0]);
    const imgSize = filePath[index].image_size;
    const wScale = imgWeight / imgSize[0];
    const hScale = wScale;
    onDrawFlag = true;
    onDrawData = data;
    onDrawIndex = index;
    if (data.size > 0) {
      const main = document.getElementsByTagName('main');
      if (main[0]) mainScrollTop = main[0].scrollTop;
      defaulthOffset = Array.from(data)[0][index][0][1] * hScale - 0.5 * imageHeight;
      // console.log('hOffset', defaulthOffset);
      scrollbarRef.current!.scrollTop(Math.max(Number(index) * imageHeight + defaulthOffset, 0));
    }
  };

  const highlightText = (vals: number[][], index: number) => {
    // const el = imageRefs[index]
    const imgSize = filePath[index].image_size;
    const wScale = imgWeight / imgSize[0];
    // const _height = _wScale * imgSize[1]
    // const _hScale =
    const hScale = wScale;
    // const el = document.getElementById(`img_${index}`)
    // console.log(el)
    const ctx = canvasRef.current?.getContext('2d');
    if (ctx) {
      ctx?.beginPath();
      ctx.strokeStyle = '#42B893';
      ctx.fillStyle = '#42B893';
      const hOffset = (curScrollTop === 0 || curScrollTop === filePath.length - 1 * imageHeight ? 0 : defaulthOffset) + mainScrollTop;
      // console.log('hOffset', hOffset);
      // console.log('curScrollTop', curScrollTop);
      vals.forEach((v, i) => {
        if (i === 0) ctx.moveTo(v[0] * wScale, v[1] * hScale - hOffset);
        else ctx.lineTo(v[0] * wScale, v[1] * hScale - hOffset);
      });
      ctx.lineTo(vals[0][0] * wScale, vals[0][1] * hScale - hOffset);
      ctx.globalAlpha = 0.4;
      ctx.fill();
      ctx.stroke();
    }
  };

  const handleDraw = (index: number) => {
    onClear();
    onDrawData.forEach((data) => {
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          const val = data[key];
          highlightText(val, index);
        }
      }
    });
  };

  const onScroll = (val: { scrollLeft: number; scrollTop: number }) => {
    // 节流处理
    if (ticking) {
      return;
    }
    ticking = true;
    requestAnimationFrame(() => {
      ticking = false;
    });
    const { scrollTop } = val;
    // setCurScrollTop(scrollTop);
    curScrollTop = scrollTop;
    const newStartIndex = Math.floor(scrollTop / imageHeight);
    const start = newStartIndex >= 1 ? newStartIndex - 1 : newStartIndex;
    const end = newStartIndex < filePath.length - 1 ? newStartIndex + 1 : newStartIndex;
    setImgIndex({ start, end });
    if (onDrawFlag) {
      handleDraw(onDrawIndex);
      onDrawFlag = false;
      onDrawData = new Set<{ [index: number]: number[][] }>();
    } else {
      onClear();
    }
  };

  useImperativeHandle(ref, () => ({
    onDraw,
  }));

  return (
    <>
      <div className={'review-title'}>原文视图</div>
      <div className="img-div">
        <Scrollbars ref={scrollbarRef} onScrollFrame={onScroll} style={{ height: '794.41px' }}>
          <div style={{ height: totalHeight }} className="scroll-container">
            <canvas
              ref={canvasRef}
              width={imgWeight}
              height={imgHeight}
              className="draw-canvas"
              style={{
                zIndex: 4,
                position: 'fixed',
              }}
            ></canvas>
            {visibleImages?.map((img, i) => (
              <div key={img.local_path} className="image-wrapper" style={{ top: `${(i + imgIndex.start) * imageHeight}px`, position: 'absolute' }}>
                <img
                  src={minioUrl + img.minio_filepath}
                  alt={`img_${i + imgIndex.start}`}
                  style={{ width: `${imgWeight}px` }}
                  // decoding="async" loading="lazy"
                />
              </div>
            ))}
          </div>
        </Scrollbars>
      </div>
    </>
  );
});

export default TextView;
