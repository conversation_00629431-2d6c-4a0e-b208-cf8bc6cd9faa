import { Button, Col, Row, Space } from 'antd';
import { UploadFile } from 'antd/es/upload/interface';
import TagList from './TagList';
import { CloseOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import UploadErrorModal from './UploadErrorModal';
import fileaddition from '../assets/img/fileaddition.svg';
import FakeDownload from './FakeDownload';

interface UploadItem {
  file: UploadFile;
  dataSetId?: string;
  tags?: string[];
  status?: 'uploading' | 'done' | 'error';
  parseState?: string;
  name?: string;
}

interface UploadListProps {
  fileList: UploadItem[];
  className?: string;
  noEditTag?: boolean;
  onAddTag: (datasetId: string, tags: string[]) => void;
  onDelFile: (uid: string) => void;
  onReUpload: (uid: string) => void;
}

const statusMap: Record<string, string> = {
  done: '上传成功',
  success: '上传成功',
  error: '上传失败',
  uploading: '上传中',
  removed: '移除',
};

const UploadDatasetList: React.FC<UploadListProps> = ({
  fileList,
  onAddTag,
  className,
  noEditTag,
  onDelFile,
  onReUpload,
}) => {
  const [uploadErrorModal, setUploadErrorModal] = useState(false);

  const DetailBtn: React.FC<{ file: UploadItem }> = ({ file }) => {
    const { parseState, status } = file;
    if (status !== 'uploading' && parseState !== '解析中') {
      if (status === 'error') {
        return (
          <Button type="link" className="reqularText" onClick={() => onReUpload(file.file.uid)}>
            重新上传
          </Button>
        );
      } else if (parseState === '解析成功') {
        return (
          <Button type="link" className="reqularText">
            详情
          </Button>
        );
      } else if (parseState === '解析失败') {
        return (
          <Button type="link" className="reqularText">
            重新解析
          </Button>
        );
      }
    }
    return null;
  };

  return (
    <div className={className}>
      {fileList.map((item, index) => (
        <Row
          key={item.file?.uid || index}
          className="upload-list-item"
          gutter={16}
          style={{ position: 'relative' }}
        >
          <Col span={5}>
            <div style={{ width: '100%', display: 'flex', gap: 12 }}>
              <img src={fileaddition} />
              <div
                className="reqularText"
                style={{
                  fontSize: 14,
                  fontWeight: 500,
                  textDecoration: 'underline',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                }}
              >
                {item.name || item.file.name}
              </div>
            </div>
          </Col>
          <Col span={9}>
            <TagList
              tagList={item.tags || []}
              dataSetId={item.dataSetId || ''}
              onChange={(tags) => {
                if (item.dataSetId) onAddTag(item.dataSetId, tags);
              }}
              showAddBtn={false}
              AddBtnLabel="添加标签"
            />
          </Col>
          <Col span={2}>
            <div style={{ minWidth: '60px' }} className="reqularText color-dimgray">
              {statusMap[item.status || ''] || '平台库'}
            </div>
          </Col>
          <Col span={6}>
            <div style={{ width: '130px' }}>{item.parseState}</div>
            <div style={{ width: '90px' }}>
              <DetailBtn file={item} />
            </div>
          </Col>
          <Col span={2} style={{ textAlign: 'right' }}>
            <Button type="text" size="small" onClick={() => onDelFile(item.file?.uid)}>
              <CloseOutlined />
            </Button>
          </Col>
          {item.status === 'uploading' ? <FakeDownload /> : null}
        </Row>
      ))}
      <UploadErrorModal visible={uploadErrorModal} OnClose={() => setUploadErrorModal(false)} />
    </div>
  );
};

export default UploadDatasetList;
