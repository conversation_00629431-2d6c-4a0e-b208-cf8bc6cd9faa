interface CreateTaskFormType {
  taskName: string;
  fileList: string[];
  splitLevel: number;
  topk: number;
  topp: number;
  maxlen: number;
  repeat: number;
  description: string;
  domain: string;
}

interface trainingSettings {
  splitLevel: number;
  topk: number;
  topp: number;
  maxlen: number;
  repeat: number;
}
// 定义所有可能的滑块key类型
type SliderKey =
  | "splitLevel"
  | "topkValue"
  | "toppValue"
  | "maxLen"
  | "repeatValue";

// 定义滑块配置项类型
 interface SliderProps {
  key: SliderKey;
  label: string;
  tooltipTitle: string;
  SiderMark: SliderMarks;
  formatter: (value: number|undefined) => React.ReactNode;
}

// 定义训练设置类型（与SliderKey对应）
 interface TrainingSettings {
  splitLevel: number;
  topkValue: number;
  toppValue: number;
  maxLen: number;
  repeatValue: number;
}
