import { AxiosResponse } from "axios";
import { GetAllUserResp, UserType, getAllUser } from "../../api/user";
import { SET_USER_LIST } from "../Type";
import type { Dispatch } from "redux";
import { message } from "antd";

let flag = false;
export function setUserList(userList: UserType[]) {
  return (dispath: Dispatch) => {
    dispath({ type: SET_USER_LIST, payload: userList });
  };
}

export function getUserList() {
  // 返回一个有默认参数dispatch函数
  return (dispatch: Dispatch<any>) => {
    if (!flag) {
      flag = true;
      getAllUser()
        // 返回参数 是AxiosResponse有个泛型是定义data
        // LoginResponseType定义AxiosResponse的data类型
        // LoginResponseType 登录返回类型 AxiosResponse axios返回类型
        .then((res: AxiosResponse<GetAllUserResp>) => {
          flag = false;
          if (res?.data?.code === 200) {
            dispatch({ type: SET_USER_LIST, payload: res.data.data });
            // dispatch({ type: SET_USER, payload: res.data.user });
            // 实现跳转
          } else {
            message.error(res?.data?.message);
          }
        });
    }
  };
}