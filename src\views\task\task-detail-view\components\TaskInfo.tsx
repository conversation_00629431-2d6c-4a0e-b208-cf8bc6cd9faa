import CheckProgress from '@/components/CheckProgress';
import useTaskStatusMap from '@/components/CheckProgress/hooks/useTaskStatusMap';
import { TaskDetailType } from '@/types';
import { paragraph, questionDensity } from '@/utils/conts';
import { formattedTime } from '@/utils/formatred';
import { queryUserInfo } from '@/views/login/service';
import { Avatar, Button, Descriptions, DescriptionsProps } from 'antd';
import { useEffect, useState } from 'react';
import avatar1 from '@/assets/img/avatar-1.png';

interface taskInfoProps {
  taskDetail?: TaskDetailType;
}
const TaskInfo: React.FC<taskInfoProps> = ({ taskDetail }) => {
  const [name, setName] = useState('');
  const [avatar, setAvatar] = useState(avatar1);
  const [previewModal, setPreviewModal] = useState(false);

  const UserName = () => {
    useEffect(() => {
      const fetchUserName = async () => {
        const res = await queryUserInfo();
        if (res && res.data) {
          setName(res.data.userName);
        }
      };

      fetchUserName();
    }, []);

    return <b className="namesytle">{name}</b>;
  };
  const descItems: DescriptionsProps['items'] = [
    {
      key: '1',
      label: '任务ID',
      children: taskDetail?.id ? taskDetail.id : 'NULL',
    },

    {
      key: '2',
      label: '创建用户',
      children: (
        <>
          <Avatar className="ellipse-parent29-1" src={avatar} />
          {UserName()}
        </>
      ),
    },

    {
      key: '3',
      label: '段落精细度',
      children: taskDetail?.splitLevel ? paragraph[taskDetail?.splitLevel - 1] : '自动',
    },

    {
      key: '4',
      label: '任务状态',
      children: (
        <>
          {useTaskStatusMap(taskDetail)}（{taskDetail?.complete + ' / ' + taskDetail?.total}）
        </>
      ),
    },

    {
      key: '5',
      label: '创建时间',
      children: taskDetail?.createTime ? formattedTime(new Date(taskDetail?.createTime)) : 'NULL',
      // children: <>{taskDetail?.createTime}</>,
    },

    {
      key: '6',
      label: '提问密度',
      children: taskDetail?.densityLevel ? questionDensity[taskDetail?.densityLevel - 1] : '自动',
    },

    {
      key: '7',
      label: '需求描述',
      children: (
        <div
          style={{
            maxWidth: 230,
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
          }}
        >
          {taskDetail?.description}
        </div>
      ),
    },
    {
      key: '8',
      label: '审核进度',
      children: (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            alignItems: 'start',
          }}
        >
          {taskDetail && taskDetail?.taskStatus !== 'FAILED' ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <label>{taskDetail.reviewCount + ' / ' + taskDetail.qaCount}</label>
            </div>
          ) : (
            <label>-</label>
          )}
          <Button
            style={{ paddingBottom: '15px' }}
            type="link"
            onClick={() => {
              setPreviewModal(true);
            }}
          >
            查看审核进度
          </Button>
        </div>
      ),
    },
  ];
  return (
    <div className="head_info">
      <div
        style={{
          width: '100%',
          textAlign: 'start',
          justifyContent: 'space-between',
          display: 'inline-flex',
          marginBottom: '1rem',
          // marginTop: "1rem",
          paddingBottom: '1rem',
          alignItems: 'center',
          borderBottom: '1px solid #E1EAEF',
        }}
      >
        <label className="mediumText">任务信息</label>
      </div>
      <Descriptions
        column={1}
        style={{
          padding: '1rem',
          width: '100%',
        }}
        size="small"
        items={descItems}
      />

      <CheckProgress
        taskDetail={taskDetail}
        visible={previewModal}
        userName={name}
        userAvatar={avatar}
        OnClose={() => setPreviewModal(false)}
      ></CheckProgress>
    </div>
  );
};

export default TaskInfo;
