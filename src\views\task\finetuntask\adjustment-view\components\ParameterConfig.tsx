import React, { useMemo } from "react";
import { Flex, Form, Segmented, Tooltip } from "antd";
import "../../../../../css/AdjustmentView.css";
import CustomSlider from "./CustomSlider";
import { creativeSliders, correlationSliders } from "./const";
import { OPTIONS_ARRAY } from "./const";
export interface ParameterConfigProps {
  trainingSettings: TrainingSettings;
  modelConfig: string | number;
  setTrainingSettings: React.Dispatch<React.SetStateAction<TrainingSettings>>;
  setModelConfig: React.Dispatch<React.SetStateAction<string | number>>;
}

const ParameterConfig: React.FC<ParameterConfigProps> = ({
  trainingSettings,
  setTrainingSettings,
  setModelConfig,
  modelConfig,
}) => {
  const handleSettingChange = (key: SliderKey, value: number) => {
    setTrainingSettings((prevSettings: any) => ({
      ...prevSettings,
      [key]: value, // 假设newValue是您要设置的新值
    }));
    setModelConfig("手动配置");
  };
  const handeleModelConfig = (value: string | number) => {
    if (value === "自动配置") {
      setTrainingSettings({
        splitLevel: 50,
        topkValue: 50,
        toppValue: 50,
        maxLen: 50,
        repeatValue: 50,
      });
    }
    setModelConfig(value);
  };
  const options = useMemo(() => {
    return OPTIONS_ARRAY.map((option) => ({
      label: (
        <Tooltip title={option}>
          <a
            onClick={() => setModelConfig(option)}
            className={
              modelConfig === option ? "model-config-active" : "model-config"
            }
          >
            {option}
          </a>
        </Tooltip>
      ),
      value: option,
    }));
  }, []);
  return (
    <div className="left">
      <div className="headTitle">参数配置</div>
      <div className="adjiustModel">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            width: "100%",
          }}
        >
          <div
            style={{
              fontSize: "16px",
              fontWeight: "500",
              lineHeight: "16px",
              color: "#000000",
              fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
              marginBottom: "24px",
            }}
          >
            参数配置:
          </div>
          <Flex
            style={{
              display: "flex",
              flexDirection: "column",
              width: "60%",
              marginLeft: "70px",
            }}
          >
            <Form.Item<CreateTaskFormType>>
              <Segmented
                className="createtask-segmented"
                size="large"
                options={options}
                value={modelConfig}
                onChange={handeleModelConfig}
              />
            </Form.Item>
          </Flex>
        </div>
      </div>
      <div className="creative">
        <div className="section-title">创意性：</div>
        {creativeSliders.map((config) => (
          <CustomSlider
            key={config.key}
            label={config.label}
            tooltipTitle={config.tooltipTitle}
            formatter={config.formatter}
            value={trainingSettings[config.key]}
            SiderMark={config.SiderMark}
            onChange={(val) => handleSettingChange(config.key, val)}
            disabled={modelConfig === "自动配置"}
          />
        ))}
      </div>
      <div className="correlation">
        <div className="section-title">相关性：</div>
        {correlationSliders.map((config) => (
          <CustomSlider
            key={config.key}
            label={config.label}
            tooltipTitle={config.tooltipTitle}
            SiderMark={config.SiderMark}
            formatter={config.formatter}
            value={trainingSettings[config.key]}
            onChange={(val) => handleSettingChange(config.key, val)}
            disabled={modelConfig === "自动配置"}
          />
        ))}
      </div>
    </div>
  );
};

export default ParameterConfig;
