import type { ActionType } from "../../types"

const initialState = {
  trainingData: []
};

const reducer = (state = initialState, action: ActionType) => {
  switch (action.type) {
    case "SET_TRAINING_DATA":
      const newState = {
        ...state,
          trainingData: [...state.trainingData, action.payload]
        };
        // 将数据保存到本地存储（这里使用Local Storage）
        localStorage.setItem("trainingData", JSON.stringify(newState.trainingData));
        return newState;

    default:
      return state;
  }
};

export default reducer