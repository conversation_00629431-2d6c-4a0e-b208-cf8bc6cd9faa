import { AxiosResponse } from "axios";
import request from "../utils/request";
import { AllocatedQA } from "../types";

export interface NormalReviewParams {
  // childFileId: string;
  id: string;
  taskId: string;
  score: string;
}

export interface ScoreReviewParams extends NormalReviewParams {
  score: string;
}

export interface ReviewProgress {
  taskId: string;
  userId: string;
}
/**
 * 普通审核
 * @param params 审核信息
 * @returns
 */
export function normalReview(
  params: NormalReviewParams
) {
  const url = `/review/normal`;
  return request.post(url, params);
}

/**
 * 打分审核
 * @param params 审核信息
 * @returns
 */
export function scoreReview(
  params: NormalReviewParams
) {
  const url = `/review/score`;
  return request.post(url, params);
}


/**
 * qa分配
 * @param params
 * @returns
 */
export async function queryQaAllocated(
  //params: ProgressId
  taskId:string,
  userId: string
): Promise<AxiosResponse<AllocatedQA> | undefined> {
  try{
    const url = `/review/allocated/qas?taskId=${taskId}&userId=${userId}`;
    const res = await request.get(url);
    return res.data;
  } catch(e){
    return undefined;
  }
}
/**
 * 添加审核人员
 * @param taskId 任务ID
 * @param userId 用户ID
 * @returns
 */
export function addReviewUser(
  taskId: string, userId: string
) {
  const url = `/review/reviewer`;
  return request.post(url, { taskId, userId });
}

/**
 * 删除审核人员
 * @param taskId 任务ID
 * @param userId 用户ID
 * @returns
 */
export function deleteReviewUser(
  taskId: string, userId: string
) {
  const url = `/review/reviewer`;
  return request.delete(url, { data: { taskId, userId } });
}

/**
 * 获取审核人员列表
 * @param taskId 任务ID
 * @returns
 */
export function getAllUserByTaskId(
  taskId: string
) {
  const url = `/review/reviewer/list?taskId=${taskId}`;
  return request.get(url);
}

/**
 * 获取审核权限
 * @param taskId 任务Id
 * @param userId 用户Id
 * @returns
 */
export function getPermissions(
  taskId: string,
  userId: string
){
  const url = `/review/allocated/qas?taskId=${taskId}&userId=${userId}`;
  return request.get(url);
}