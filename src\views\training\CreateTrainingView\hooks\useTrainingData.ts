import { useState, RefObject } from 'react';
import { TariningType } from '../type';
import { createModel, startTrain, deleteModel } from '@/api/modle';
import { message } from 'antd';

interface FormRefs {
  modelSelectRef: RefObject<any>;
  taskSelectRef: RefObject<any>;
  trainingSetSelectRef: RefObject<any>;
  resourceSetSelectRef: RefObject<any>;
}

interface MyObject {
  modelId: number;
  modelConfigData: any;
}

export const useTrainingData = (refs: FormRefs) => {
  const [trainingData, setTrainingData] = useState<TariningType>(new Object() as TariningType);

  const collectAllTrainingData = () => {
    let collectedData = { ...trainingData };
    const { modelSelectRef, taskSelectRef, trainingSetSelectRef, resourceSetSelectRef } = refs;

    if (modelSelectRef.current) {
      const modelSelectData = modelSelectRef.current.getModelSelectData();
      collectedData = {
        ...collectedData,
        dataset: modelSelectData.Dataset,
        modelIntro: modelSelectData.Description,
        modelName: modelSelectData.Name,
        testSetRatio: modelSelectData.ModelType,
        modelId: modelSelectData.Id,
      };
    }

    if (taskSelectRef.current) {
      const taskSelectData = taskSelectRef.current.getTaskSelectData();
      collectedData = {
        ...collectedData,
        tasks: taskSelectData.Task,
        testSet: taskSelectData.TestSet,
        testSetConfig: taskSelectData.TestSetConfig,
      };
    }

    if (trainingSetSelectRef.current) {
      const trainingConfigData = trainingSetSelectRef.current.getTaskSelectData();
      collectedData = {
        ...collectedData,
        trainSetConfig: trainingConfigData.TrainSetConfig,
        srategy: trainingConfigData.Srategy,
        iterationLevel: trainingConfigData.IterationLevel,
        learningValue: trainingConfigData.LearningValue,
        batchValue: trainingConfigData.BatchValue,
      };
    }

    if (resourceSetSelectRef.current) {
      const overviewConfigData = resourceSetSelectRef.current.getOverviewData();
      collectedData = {
        ...collectedData,
        framework: overviewConfigData.Framework,
        serverSelection: overviewConfigData.ServerSelection,
        arithmeticConfiguration: overviewConfigData.ArithmeticConfiguration,
        serverConfigId: overviewConfigData.ServerConfigId,
      };
    }

    setTrainingData(collectedData);
    return collectedData;
  };

  const convertPercentageToDecimal = (percentageStr: string) => {
    if (!percentageStr) return 0;
    const numericValue = parseFloat(percentageStr.replace('%', ''));
    return numericValue / 100;
  };

  const handleTrainingSubmit = async (setShowTraining: (show: boolean) => void) => {
    const currentAllData = collectAllTrainingData();

    const params = {
      basicConfigRequest: {
        computeResource: Array.isArray(currentAllData.arithmeticConfiguration)
          ? currentAllData.arithmeticConfiguration
          : [currentAllData.arithmeticConfiguration],
        server: currentAllData.serverSelection,
        serverConfigId: currentAllData.serverConfigId,
        trainingFramework: currentAllData.framework,
      },
      category: currentAllData.testSetRatio === 1 ? 0 : 1,
      modelName: currentAllData.modelName,
      modelId: currentAllData.modelId,
      introduction: currentAllData.modelIntro,
      trainConfig: {
        id:
          currentAllData.tasks?.length > 0
            ? currentAllData.tasks.map((task: any) => task.taskId)
            : ['86fdfeef55b6bcf4768ab9b0bd7a0cba'],
        datasetRadio: convertPercentageToDecimal(currentAllData.testSet),
        trainStrategy: currentAllData.srategy,
        interationNumber: Number(currentAllData.iterationLevel),
        batchSize: Number(currentAllData.batchValue),
        learnRate: currentAllData.learningValue,
        baseModelId: currentAllData.modelId,
      },
    };

    // 清理localStorage中的训练数据
    const trainingFromStorageString = localStorage.getItem('trainingData');
    if (trainingFromStorageString) {
      localStorage.removeItem('trainingData');
      try {
        const trainingFromStorageArray: MyObject[] = JSON.parse(trainingFromStorageString);
        if (trainingFromStorageArray.length > 0 && trainingFromStorageArray[0].modelId) {
          console.log('clear', trainingFromStorageArray[0].modelId);
          deleteModel(trainingFromStorageArray[0].modelId);
        }
      } catch (e) {
        console.error('Failed to parse trainingData from localStorage', e);
      }
    }

    createModel(params).then((res) => {
      if (res?.data?.code === 200) {
        setShowTraining(true);
        startTrain(res.data?.data.modelId);
      } else if (res?.data?.code === 50001) {
        message.error(res?.data?.message);
      }
    });
  };

  return {
    trainingData,
    setTrainingData,
    collectAllTrainingData,
    handleTrainingSubmit,
  };
};
