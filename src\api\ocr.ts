
import { OCRReviewFileStructure } from '../components/ocrReview/components/ResultView/type';
import { CustomResponseType } from '../types';
import request from "../utils/request";

export interface ReviewData {
  file_id: string;
  file_name: string;
  value_structure: OCRReviewFileStructure;
}

export interface GetReviewdataResp extends CustomResponseType {
  data: ReviewData;
}

export interface ReviewDataParams {
  file_id: string;
  file_name: string;
  value_structure: string;
}

export interface UpdateReviewParams {
  file_id: string;
  review_status: ReviewStatus;
}

export enum ReviewStatus {
  UnReview,
  FinishReview,
  InReview,
}
// 开始审核
export const getReviewDataAPI = (fileId: string) => request.get<GetReviewdataResp>(`/review?file_id=${fileId}`);

// 审核完成
export const reviewDataAPI = (params: ReviewDataParams) => request.post<CustomResponseType>('/review', params);

// 修改审核状态
export const updateReviewStatusAPI = (params: UpdateReviewParams) => {
  const formData = new FormData();
  formData.append('review_status', params.review_status.toString());
  formData.append('file_id', params.file_id);
  return request.put<CustomResponseType>('/review/status', formData);
};
