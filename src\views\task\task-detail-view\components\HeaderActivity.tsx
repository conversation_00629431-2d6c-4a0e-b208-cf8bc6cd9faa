import { getAllocateQA, QAInfoType } from '@/api/qa';
import { isSetReviewConfigInfo, setReviewConfigInfo } from '@/api/task';
import { DefaultBtnConfig } from '@/components/ReviewConfigModal';
import { Button, Col, Row, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
interface HeaderActivityProps {
  task_id?: string;
  checkedFileSet: Set<string>;
  checkedQASet: QAInfoType[];
  globalSelectedQAIds: Set<string>;
  resetCheck: () => void;
  exportModal: boolean;
  setExportModal: (val: boolean) => void;
  deleteQaDialog: boolean;
  setDeleteQaDialog: (val: boolean) => void;
  exportAll: boolean;
  setExportAll: (val: boolean) => void;
  onShowCheckbox: (val: boolean) => void;
}

const HeaderActivity: React.FC<HeaderActivityProps> = ({
  task_id,
  checkedFileSet,
  checkedQASet,
  globalSelectedQAIds,
  resetCheck,
  setExportModal,
  setDeleteQaDialog,
  setExportAll,
  onShowCheckbox,
}) => {
  const navigate = useNavigate();

  const [showCheckbox, setShowCheckbox] = useState(false);

  const handleReviewConfig = (id: string) => {
    const params = {
      areviewCriteria: '请审核生成的答案内容是否正确，如不正确请对其进行编辑修改',
      qreviewCriteria: '请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除',
      scoreReviewCriteria: '请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价',
      taskId: id,
      isStepTwo: true,
      scoreButtonInfoList: DefaultBtnConfig,
    };
    setReviewConfigInfo(params);
    const userId = sessionStorage.getItem('id');
    if (userId) {
      getAllocateQA(id, userId);
    }
  };
  const handleApply = (e: React.MouseEvent<HTMLElement>) => {
    // 阻止默认
    e.preventDefault();
    isSetReviewConfigInfo(task_id as string).then((res) => {
      if (res?.data?.code !== 200 || !res?.data?.data) {
        // 如果创建者审核完了一次，则直接跳转到下一步审核页面
        handleReviewConfig(task_id as string);
      }
    });
    const userId = sessionStorage.getItem('id');
    if (userId && task_id) {
      getAllocateQA(task_id, userId!);
    }
    navigate(`/main/task/review/${task_id}`);
    // 新需求，不需要配置直接开发权限

    // 创建者没有审核完成的话，协作者不能点击数据审核按钮
    // 创建者审核过的话，协作者就能点击数据审核按钮跳转到下一页面
  };
  const buttonStyle = {
    width: '124px',
    height: '40px',
    fontSize: '14px',
    justifyContent: 'center',
  };

  useEffect(() => {
    if (onShowCheckbox) {
      onShowCheckbox(showCheckbox);
    }
  }, [showCheckbox]);

  return (
    <div>
      <Row>
        <Col span={12}>
          <div
            style={{
              width: '100%',
              textAlign: 'start',
              marginBottom: '1rem',
            }}
            className="task-title mediumText"
          >
            生成结果预览
            <span
              style={{
                marginLeft: '3rem',
                fontSize: '12px',
                color: '#ccc',
              }}
            >
              预估生成时间: 已完成
            </span>
          </div>
        </Col>
        <Col span={12}>
          <div
            style={{
              width: '100%',
              textAlign: 'start',
              justifyContent: 'flex-end',
              display: 'inline-flex',
              alignItems: 'center',
            }}
          >
            <Space size={10} style={{ marginBottom: '10px' }}>
              {!showCheckbox ? (
                <Button
                  className="default-btn"
                  style={buttonStyle}
                  onClick={() => {
                    setShowCheckbox(true);
                  }}
                >
                  多选
                </Button>
              ) : checkedFileSet.size === 0 &&
                checkedQASet.length === 0 &&
                globalSelectedQAIds.size === 0 ? (
                <Button
                  className="default-btn"
                  style={buttonStyle}
                  onClick={() => {
                    resetCheck();
                    setTimeout(() => {
                      setShowCheckbox(false);
                    });
                  }}
                >
                  取消
                </Button>
              ) : (
                <>
                  <Button
                    className="default-btn"
                    style={buttonStyle}
                    onClick={() => {
                      setDeleteQaDialog(true);
                    }}
                  >
                    删除所选项
                  </Button>
                  <Button
                    className="default-btn"
                    style={buttonStyle}
                    onClick={() => {
                      setExportAll(false);
                      setExportModal(true);
                    }}
                  >
                    导出所选项
                  </Button>
                  <Button
                    className="default-btn"
                    style={buttonStyle}
                    onClick={() => {
                      resetCheck();
                      setTimeout(() => {
                        setShowCheckbox(false);
                      });
                    }}
                  >
                    取消选择
                  </Button>
                </>
              )}

              <Button
                className="default-btn"
                style={buttonStyle}
                onClick={() => {
                  setExportAll(true);
                  setExportModal(true);
                }}
              >
                导出全部
              </Button>

              <Button
                type="primary"
                className="primary-btn boldText review-btn"
                shape="round"
                style={{ width: 120 }}
                onClick={(e) => {
                  handleApply(e);
                  navigate(`/main/task/review/${task_id}`, { state: { fromButton: true } });
                }}
              >
                数据审核
              </Button>
            </Space>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default HeaderActivity;
