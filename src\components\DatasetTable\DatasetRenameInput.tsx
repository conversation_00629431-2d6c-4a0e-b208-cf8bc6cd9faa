import { Input, Button, message, Space } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { DataSetType } from '../../types';
import { renameDataSets } from '../../api/dataset';

export interface DatasetRenameInputProps {
  dataSet: DataSetType;
  datasets: DataSetType[];
  index: number;
  renameRows: Set<string>;
  setRenameRows: (rows: Set<string>) => void;
  setDatasets: (data: DataSetType[]) => void;
  handleRefresh: () => void;
}

const DatasetRenameInput: React.FC<DatasetRenameInputProps> = ({
  dataSet,
  datasets,
  index,
  renameRows,
  setRenameRows,
  setDatasets,
  handleRefresh,
}) => {
  const [renameInput, setRenameInput] = useState(dataSet.datasetName);
  return (
    <Space.Compact>
      <Input
        placeholder="请输入数据集名称"
        value={renameInput}
        onChange={(e) => setRenameInput(e.target.value)}
      />
      <Button
        icon={<CheckOutlined />}
        onClick={() => {
          const newName = renameInput?.trim() || '';
          if (newName && newName.length > 0) {
            const data = datasets;
            const name = (data[index].name = newName);
            const updateRenameSets = renameRows;
            updateRenameSets.delete(dataSet.id);
            setRenameRows(updateRenameSets);
            setRenameInput('');
            setDatasets(data);
            const id = data[index].id;
            renameDataSets({ datasetId: id, datasetName: name }).then(() => {
              handleRefresh();
            });
          } else {
            message.error('数据集名称不合法');
          }
        }}
      />
      <Button
        icon={<CloseOutlined />}
        onClick={() => {
          const updateRenameSets = renameRows;
          updateRenameSets.delete(dataSet.id);
          setRenameRows(updateRenameSets);
          setRenameInput('');
        }}
      />
    </Space.Compact>
  );
};

export default DatasetRenameInput;
