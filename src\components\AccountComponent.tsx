import { FunctionComponent, useEffect, useState } from "react";
import "./AccountComponent.css";
import { useSelector } from "react-redux";
import { State } from "../store";

const AccountComponent: FunctionComponent = () => {
  const [point, setPoint] = useState(0);
  // const userInfo = useSelector((state: State) => state.auth.userInfo);
  const [userInfo, setUserInfo] = useState<any>();
  useEffect(() => {
    const userInfoStr = sessionStorage.getItem('userInfo');
    if(userInfoStr) {
      setUserInfo(JSON.parse(userInfoStr));
    }
  }, [])

  useEffect(() => {
    if (userInfo)
      setPoint(userInfo.point);
  }, [userInfo])
  return (
    <div className="rectangle-parent67">
      <div className="group-child32" />
      <div className="div694">积分</div>
      <b className="b79 boldText">{point}</b>
      <a className="div695">积分明细</a>
      <a className="div696">积分使用规则</a>
      <b className="b80 boldText">我的账户</b>
    </div>
  );
};

export default AccountComponent;
