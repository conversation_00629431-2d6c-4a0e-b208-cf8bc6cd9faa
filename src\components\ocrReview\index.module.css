.review-container {
  padding: 55px 45px;
}

.rounded-border {
  border-radius: 15px;
}

.files-management-header,
.review-title-lf {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.files-management-content {
  height: 85vh;
  margin-top: 2vh;
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
}

.ocr-tag {
  width: 92px;
  height: 32px;
  flex-shrink: 0;
  border-radius: 8px;
  background: linear-gradient(180deg, #1A62B6 0%, #6101AE 100%);
  color: #FFF;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.review-btn {
  width: 124px;
  height: 40px;
  flex-shrink: 0;
  border-radius: 28px;
  border: 2px solid #0050AD;
  background: #FFF;
  color: #0050AD;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.change-btn {
  color: #666;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.review-container-lf {
  padding: 16px 12px;
  border: 1px solid #EEF1F5;
}

.review-container-mid {
  padding: 16px 12px;
}

.review-container-rg {
  padding: 16px 12px;
  border: 1px solid #EEF1F5;
}

.review-title,
.review-title-lf {
  color: #6D6D6D;
  font-family: HarmonyOS Sans SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}