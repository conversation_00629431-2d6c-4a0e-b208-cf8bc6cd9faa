.group-child21,
.group-child22 {
  position: absolute;
  top: 0;
  left: 25.38rem;
  width: 69.25rem;
  height: 46.19rem;
}

.group-child22 {
  left: 47.13rem;
  width: 25.75rem;
}

.group-child23 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-29xl) var(--br-29xl) 0 0;
  background: linear-gradient(180deg, #000, rgba(0, 27, 22, 0));
  width: 120rem;
  height: 67.5rem;
}

.div649 {
  position: absolute;
  top: 32.9%;
  /* left: 21.5rem; */
  left: 50%;
  transform: translate(-50%);
  font-weight: 300;
  width: max-content;
  font-size: 77px;
}

.image-2-icon {
  position: absolute;
  top: 33.75rem;
  left: calc(50% - 803.42px);
  border-radius: var(--br-13xl);
  width: 100.54rem;
  height: 23.93rem;
  object-fit: cover;
}

.logo-child162 {
  position: absolute;
  top: 0.01rem;
  left: 1.94rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-mediumspringgreen);
  width: 0.4rem;
  height: 2.69rem;
  transform: rotate(45deg);
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child163 {
  top: 0.02rem;
  left: 1.22rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 1.2rem;
  transform: rotate(45deg);
}

.logo-child163,
.logo-child164,
.logo-child165,
.logo-child166 {
  position: absolute;
  border-radius: var(--br-26xl);
  width: 0.4rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child164 {
  top: 0;
  left: 0.52rem;
  background-color: var(--color-teal-100);
  height: 0.74rem;
  transform: rotate(45deg);
}

.logo-child165,
.logo-child166 {
  top: 2.19rem;
  left: 1.05rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 1.2rem;
  transform: rotate(-135deg);
}

.logo-child166 {
  top: 2.21rem;
  left: 1.75rem;
  background-color: var(--color-teal-100);
  height: 0.74rem;
}

.logo33 {
  position: relative;
  width: 2.27rem;
  height: 2.21rem;
}

/* .div650,
.logo-parent1 {
  position: absolute;
  text-align: left;
} */

.logo-parent1 {
  top: 20.2%;
  /* left: 54.31rem; */
  /* left: calc(50% - 48px); */
  left: 50%;
  transform: translate(-50%);
  /* width: 11.38rem; */
  height: 3.25rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 28.4px;
  font-size: 61.717px;
  font-weight: 700;
  position: absolute;
width: max-content;
}

.div650 {
  top: 15.7%;
  left: 50%;
  transform: translate(-50%);
  font-size: var(--font-size-5xl);
  font-weight: 700;
  position: absolute;
width: max-content;
}

.group-child24 {
  position: absolute;
  top: 53rem;
  left: 84.63rem;
  border-radius: var(--br-9xs);
  background-color: var(--color-mediumaquamarine-300);
  width: 4.69rem;
  height: 4.69rem;
}

.div651 {
  position: absolute;
  top: 49.94rem;
  left: 85.63rem;
  font-size: 10.38rem;
  font-weight: 500;
}

/* .child81 {
  position: relative;
  left: 0;
  width: 120rem;
  height: 67.5rem;
} */

.card-area {
  text-align: center;
  width: 100%;
  height: calc(100vh - 4rem);
  padding-bottom: 68.2%;
  background-size: cover;
  background-position: center;
  position: relative;
  min-width: 1440px;
  min-height: 900px;
}

.group-parent {
  background-image: url('../assets/img/first-run-1.png');
  padding-bottom: 68.2%;
  left: 0;
}

.child81 {
  border-radius: var(--br-29xl) var(--br-29xl) 0 0;
  background-image: url('../assets/img/first-run-2.svg');
  margin-left: 22px;
  width: calc(100% - 44px);
  padding-bottom: 56.3%;
}

.child82,
.child83,
.child84 {
  position: absolute;
  /* top: 69.44rem; */
  left: 91.44rem;
  border-radius: var(--br-45xl);
  width: 8.06rem;
  height: 63.5rem;
}

.child83,
.child84 {
  left: 33.5rem;
  height: 44.13rem;
}

.child84 {
  top: 36.19rem;
  left: 27.25rem;
  height: 27.75rem;
}

.child85,
.child86 {
  position: relative;
  top: 19.44rem;
  left: 32.1875rem;
  width: 80rem;
  height: 45rem;
}

.child86 {
  top: 124.38rem;
  opacity: 0.1;
}

.child88,
.child89 {
  position: absolute;
  top: 132.94rem;
}

.child87 {
  /* left: 0;
  */
  /* background: linear-gradient(180deg, #005d4d, #00604f); */
  /* width: 1876px;
  height: 1056px; */
  background-image: url('../assets/img/first-run-3.svg');
  margin-left: 22px;
  /* margin-top: -40px; */
  width: calc(100% - 44px);
  padding-bottom: 56.3%;
  border-radius: var(--br-29xl) var(--br-29xl) 0 0;
}

.child88,
.child89 {
  left: 65.88rem;
  border-radius: var(--br-45xl);
  width: 8.06rem;
  height: 63.5rem;
}

.child89 {
  left: 103.38rem;
  width: 6.25rem;
  height: 37.25rem;
}

.p {
  margin: 0;
}

.div652 {
  top: 16.5%;
  font-size: 4rem;
  color: var(--color-gray-400);
  position: absolute;
  left: 7.4%;
  text-align: start;
  font-weight: 300;
}

.div653 {
  font-size: var(--font-size-45xl);
  top: 16.5%;
  font-size: 4rem;
  position: absolute;
  left: 7.4%;
  text-align: start;
  font-weight: 300;
  color: #FFF;
}

.frame-child296 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-22xl);
  width: 15rem;
  height: 3.5rem;
  background-color: #0FB698;
  border-color: #0FB698;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  color: #01120F;
}

.div654 {
  position: absolute;
  left: 7.4%;
  top: 37.1%;
}

.frame-child297 {
  /* top: 1.34rem;
  left: calc(50% + 74.94px); */
  width: 0.91rem;
  height: 0.91rem;
}

.upload-btn {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  width: -webkit-fill-available;
  padding: 0 1rem;
}

.vector-parent2 {
  position: absolute;
  top: 37.1%;
  left: 7.4%;
  width: 240px;
  height: 56px;
  text-align: center;
  font-size: var(--font-size-base);
}

.frame-child298,
.frame-child299,
.frame-child300 {
  position: absolute;
  background-color: var(--color-mediumaquamarine-300);
  width: 0.42rem;
  height: 0.42rem;
}

.frame-child298 {
  top: 0;
  left: 0;
}

.frame-child299,
.frame-child300 {
  top: 0.41rem;
  left: 0.42rem;
}

.frame-child300 {
  top: 0.83rem;
  left: 0.83rem;
}

.rectangle-parent58 {
  position: relative;
  width: 1.25rem;
  height: 1.25rem;
}

.one-click-upload {
  position: relative;
  font-weight: 700;
}

.frame-parent11,
.frame-parent12 {
  /* position: relative; */
  left: 138px;
  height: 1.44rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-5xs);
}

.frame-parent11 {
  top: 9.4%;
  left: 7.4%;
  position: absolute;
  /* width: 11.88rem; */
  color: var(--color-gray-400);
}

.frame-parent12 {
  top: 139.69rem;
  width: 10.06rem;
}

/* .child90,
.child91 {
  position: absolute;
  top: 196.44rem;
  height: 67.5rem;
} */

.child90 {
  left: 0;
  border-radius: var(--br-29xl) var(--br-29xl) 0 0;
  /* background: linear-gradient(180deg, #000706, #00241e);
  width: 120rem; */
  background-image: url('../assets/img/first-run-4.svg');
  width: calc(100% - 44px);
  padding-bottom: 56.3%;
  margin-left: 22px;
}

.child91 {
  left: 39.31rem;
  border-radius: var(--br-45xl);
  width: 9.69rem;
}

.div655 {
  position: absolute;
  top: 16.5%;
  text-align: start;
  left: 7.4%;
  font-size: var(--font-size-45xl);
  font-weight: 300;
}

.child92,
.child93 {
  position: absolute;
  left: 33.5rem;
  border-radius: var(--br-5xl);
  width: 14.5rem;
  height: 14.38rem;
}

.child92 {
  top: 222.63rem;
  background-color: var(--color-black);
}

.child93 {
  top: 220.5rem;
  background-color: #016050;
}

.child94 {
  position: absolute;
  top: 214.69rem;
  left: 53.75rem;
  border-radius: 36px;
  background-color: #092e28;
  width: 84.88rem;
  height: 43.38rem;
}

.b63,
.div656 {
  position: relative;
}

.b63 {
  font-size: var(--font-size-5xl);
  font-family: 'HarmonyOS Sans SC Bold', sans-serif;
  font-weight: 700;
}

.div656 {
  font-weight: 300;
  cursor: pointer;
  pointer-events: all;
  font-family: 'HarmonyOS Sans SC Light', sans-serif;
}

.parent125 {
  position: absolute;
  margin-top: 19.3%;
  left: 28%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 1.38rem;
  width: 232px;
  height: 230px;
  flex-shrink: 0;
  border-radius: 24px;
  border: 1px solid rgba(92, 154, 139, 0.18);
  background: linear-gradient(180deg, #00382F 0%, #005C4D 100%);
  padding-top: 32px;
  padding-left: 40px;
  box-shadow: 0px 34px 0 rgba(0, 0, 0, 0.5);
}

.vector-parent3 {
  position: absolute;
  top: 377px;
  left: 145px;
  width: 15rem;
  height: 3.5rem;
  font-size: var(--font-size-base);
}

.frame-parent13 {
  top: 96px;
  left: 166px;
  width: 14.75rem;
  height: 1.44rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-5xs);
}

.child95,
.frame-parent13,
.icon7 {
  position: absolute;
}

.icon7 {
  top: 218.75rem;
  left: 42.75rem;
  border-radius: 12px;
  width: 64.13rem;
  height: 38.94rem;
  object-fit: cover;
}

.child95 {
  top: 222.81rem;
  left: calc(50% - 277.82px);
  width: 0.61rem;
  height: 1.04rem;
}

.group-child25,
.group-child26 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-13xl);
  width: 22.5rem;
  height: 30rem;
}

.group-child25 {
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #000;
}

.group-child26 {
  object-fit: cover;
}

.qa17,
.top {
  font-weight: 300;
}

.top {
  font-weight: 500;
}

.span11 {
  letter-spacing: -0.17em;
}

.qa-top-container {
  position: absolute;
  top: 2.5rem;
  left: 2.63rem;
  line-height: 2.75rem;
}

.rectangle-parent61 {
  position: relative;
  width: 22.5rem;
  height: 30rem;
}

.div660,
.div663 {
  line-height: 2.75rem;
  font-weight: 300;
}

.div660 {
  position: absolute;
  top: 2.5rem;
  left: 2.63rem;
}

.div663 {
  position: relative;
}

.group-container {
  position: absolute;
  height: 30rem;
}

.group-container {
  top: 0;
  /* left: 10rem; */
  width: 141.25rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 1.25rem;
}

.inner3 {
  position: relative;
  /* left: 7.4%; */
  /* padding-top: 21.4%; */
  top:401px;
}

.hello14 {
  position: absolute;
  top: 1.63rem;
  left: 112.69rem;
  font-size: var(--font-size-sm);
  display: inline-block;
  width: 2.56rem;
}

.logo-child167,
.logo-child168 {
  top: 0.01rem;
  transform: rotate(45deg);
}

.logo-child167 {
  position: absolute;
  left: 1.1rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-white);
  width: 0.23rem;
  height: 1.52rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child168 {
  left: 0.69rem;
  height: 0.68rem;
}

.logo-child168,
.logo-child169,
.logo-child170,
.logo-child171 {
  position: absolute;
  border-radius: var(--br-26xl);
  background-color: var(--color-white);
  width: 0.23rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child169 {
  top: 0;
  left: 0.29rem;
  height: 0.42rem;
  transform: rotate(45deg);
}

.logo-child170,
.logo-child171 {
  top: 1.24rem;
  left: 0.59rem;
  height: 0.68rem;
  transform: rotate(-135deg);
}

.logo-child171 {
  top: 1.25rem;
  left: 0.99rem;
  height: 0.42rem;
}

.logo34 {
  position: absolute;
  top: 1.38rem;
  left: 1.5rem;
  width: 1.28rem;
  height: 1.25rem;
}

.everreachai-pollux17 {
  position: absolute;
  top: 1.38rem;
  left: 3.25rem;
  font-weight: 900;
}

.frame-child309 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  background: linear-gradient(-46.22deg, #0fb698 16.48%, #113932 82.81%);
  width: 2.25rem;
  height: 2.25rem;
}

.e18 {
  position: absolute;
  top: 0.56rem;
  left: 0.81rem;
  font-weight: 900;
}

.ellipse-parent27 {
  position: absolute;
  top: 0.88rem;
  left: 116.25rem;
  width: 2.25rem;
  height: 2.25rem;
}

.frame-child310 {
  position: absolute;
  top: 0;
  left: 17.13rem;
  width: 7.38rem;
  height: 4rem;
}

.div665 {
  font-weight: 500;
}

.div665,
.div666 {
  position: relative;
  cursor: pointer;
}

.parent126 {
  position: absolute;
  top: 1.5rem;
  left: 19.81rem;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-45xl);
}

.hello-parent12 {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(36px);
  width: 120rem;
  height: 4rem;
  font-size: var(--font-size-base);
}

.div648 {
  position: relative;
  background-color: var(--color-black);
  width: 100%;
  height: 265.56rem;
  overflow-y: auto;
  text-align: left;
  font-size: var(--font-size-xl);
  color: var(--color-white);
  overflow: hidden;
}

.start-btn {
  position: absolute;
  width: 240px;
  height: 56px;
  left: 50%;
  transform: translateX(-50%);
  top: 46.4%;
  background: #0FB698;
  border-radius: 41px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  color: #FFFFFF;
}
