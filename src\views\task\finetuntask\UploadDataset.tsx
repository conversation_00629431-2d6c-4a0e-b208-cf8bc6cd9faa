import {
  Button,
  Input,
  Segmented,
  Select,
  Upload,
  UploadProps,
  UploadFile,
  Space,
  Form,
  Divider,
  Slider,
  Tooltip,
  message,
  Spin,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Scrollbar } from "react-scrollbars-custom";
import { LeftOutlined } from "@ant-design/icons";
import UploadList from "../../../components/UploadList";
import uploadcomputer from "../../../assets/img/uploadcomputer.svg";
import {
  downloadTemplate,
  uploadFileText,
  getTaskProcess,
} from "../../../api/task";

const { Dragger } = Upload;
interface UploadDatasetFormType {
  taskName: string;
  importQAs: File[];
}
export interface DatasetFile extends UploadFile {
  parseState?: string; //解析状态
  parseProcess?: number; //解析进度
  tags?: string[];
  name: string;
  dataSetId: string;
  dataSource?: number; //0-平台库/1-用户上传
}

const UploadDatasetView: React.FC = () => {
  const navigate = useNavigate();
  const [taskName, setTaskName] = useState("");
  const [fileList, setFileList] = useState<DatasetFile[]>([]);

  const [isUploading, setIsUploading] = useState(false);

  const [form] = Form.useForm();
  const downloadZip = (e: any) => {
    e.stopPropagation();
    downloadTemplate()
      .then((response) => {
        // 对于zip文件，设置正确的MIME类型为application/zip
        const blob = new Blob([response.data], { type: "application/zip" });
        console.log("Blob", blob);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        // 设置下载文件名，这里假设为template.zip，你可根据实际情况修改
        a.download = "模板.zip";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => {
        message.error("下载模板文件失败：" + error.message);
      });
  };
  const props: UploadProps = {
    name: "file",
    multiple: true,
    action: "/task/import",
    accept: "json,application/json,.csv",
    showUploadList: true,
    fileList: fileList,
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
    customRequest: () => {},
    onChange(info) {
      let newFileList = [...info.fileList];
      setFileList(newFileList as any);
    },
  };
  // 验证成功
  const onFinish = (values: UploadDatasetFormType) => {
    const params: UploadDatasetFormType = {
      taskName: values.taskName,
      importQAs: fileList[0].originFileObj as any,
    };
    setIsUploading(true);
    uploadFileText(params).then((res) => {
      const id = res.data.data;
      message.success("上传成功");
      setIsUploading(false);
      navigate(-1);
    });
  };
  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
    setIsUploading(false);
  };
  return (
    <Scrollbar>
      <div className="uploadDataset">
        <Space
          size={20}
          style={{ display: "inline-flex", alignItems: "center" }}
        >
          <Button
            style={{ fontSize: "12px", width: "36px", height: "36px" }}
            shape="circle"
            icon={<LeftOutlined />}
            onClick={() => navigate(-1)}
          />
          <div
            className="mediumText"
            style={{ fontSize: "28px", lineHeight: "36px", fontWeight: "500" }}
          >
            上传本地数据
          </div>
        </Space>
        <div className="uploadDatasetArea" style={{ marginBottom: "47px" }}>
          <Form
            form={form}
            className="reqularText"
            name="uploadDatasetForm"
            layout="vertical"
            initialValues={{}}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <Form.Item<UploadDatasetFormType>
              name="taskName"
              label="任务名称"
              rules={[
                { required: true, message: "请输入任务名称" },
                {
                  message: "任务名称不符合要求！",
                },
              ]}
            >
              <Input
                placeholder="请输入"
                value={taskName}
                onChange={(e) => setTaskName(e.target.value)}
                style={{ width: "30rem", height: "2.5rem" }}
              />
            </Form.Item>
            <Form.Item<UploadDatasetFormType>
              label="上传本地数据"
              rules={[{ required: true, message: "请上传本地数据" }]}
            >
              <Dragger {...props} className="createTaskDragger">
                <div className="createTaskDraggerInner">
                  <img
                    className="createTaskDraggerIcon"
                    alt=""
                    src={uploadcomputer}
                  />
                  <p className="reqularText">
                    拖入您需要解析的本地数据集文档，或点击进行选择
                  </p>
                </div>
                <div className="createTaskDraggerInfo">
                  <label className="crateTaskDraggerLabel reqularText">
                    请首先
                    <Button
                      type="link"
                      style={{ padding: "0" }}
                      onClick={downloadZip}
                    >
                      下载模板文件
                    </Button>
                    ，按照模板文件格式收集或修改本地数据。
                  </label>
                  <br />
                  <label className="crateTaskDraggerLabel reqularText">
                    支持解析的文档类型有json、csv
                  </label>
                </div>
              </Dragger>
              <div>
                <UploadList
                  fileList={fileList}
                  onAddTag={(dataSetId: string, tags: string[]) => {
                    const updateList = fileList;

                    setFileList(updateList);
                  }}
                  onDelFile={(index) => {
                    // const list = fileList;
                    // list.splice(index, 1);
                    const updatedDataList = fileList.filter(
                      (item, i) => i !== index
                    );
                    setFileList(updatedDataList);
                  }}
                  className="create-task-file-list"
                  onReUpload={(index: number) => {
                    const file = fileList[index];
                    let status = "uploading";
                    const importFile: any = file;
                    importFile.status = status;
                    uploadFileText({ importQAs: file, taskName }).then(
                      (res) => {
                        if (res.data?.code === 200) {
                          importFile.status = "success";
                          importFile.dataSetId = res.data.data;
                        } else {
                          importFile.status = "error";
                        }
                        fileList[index] = importFile;
                        setFileList(fileList);
                      }
                    );
                  }}
                ></UploadList>
              </div>
            </Form.Item>
            <Form.Item style={{ textAlign: "center", marginTop: "120px" }}>
              <Button
                shape="round"
                htmlType="submit"
                className="submitBut"
                disabled={isUploading}
              >
                {isUploading ? <Spin /> : "确认上传"}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </Scrollbar>
  );
};
export default UploadDatasetView;
