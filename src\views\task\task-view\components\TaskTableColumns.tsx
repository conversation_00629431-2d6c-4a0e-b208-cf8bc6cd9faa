import React from 'react';
import { <PERSON><PERSON>, Button, Dropdown, Space, Tooltip, MenuProps } from 'antd';
import { CaretDownOutlined, QuestionCircleFilled } from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { TaskType, TaskStatus } from '@/types';
import { formattedTime } from '@/utils/formatred';
import VisableRangeSelect from '@/components/VisableRangeSelect';
import InputComponent from './InputComponent';
import { ACTION_ITEMS } from '../constants/task';

interface TaskTableColumnsProps {
  taskData: TaskType[];
  renameList: Set<string>;
  tableIndex: number;
  onRename: (taskId: string) => void;
  onDelete: (taskId: string) => void;
  onApply: (e: React.MouseEvent<HTMLElement>, rowData: TaskType) => void;
  onProblemDetail: (taskId: string) => void;
  onRetry: (taskId: string) => void;
  onDetail: (taskId: string) => void;
  onVisibleRangeChange: (taskId: string, users: string[]) => void;
  getTasksList: () => void;
  setRenameList: React.Dispatch<React.SetStateAction<Set<string>>>;
}

export const useTaskTableColumns = ({
  taskData,
  renameList,
  tableIndex,
  onRename,
  onDelete,
  onApply,
  onProblemDetail,
  onRetry,
  onDetail,
  onVisibleRangeChange,
  getTasksList,
  setRenameList,
}: TaskTableColumnsProps): ColumnsType<TaskType> => {
  const handleActionMenuClick =
    (taskId: string): MenuProps['onClick'] =>
    (e: any) => {
      if (e.key === 'rename') {
        onRename(taskId);
      } else if (e.key === 'delete') {
        onDelete(taskId);
      }
    };

  const renderActionButtons = (rowData: TaskType) => {
    const { status, taskId, complete, total } = rowData;
    let actionNode = <></>;

    if (status === TaskStatus.inProgress || status === TaskStatus.success) {
      if ((complete && complete === total) || (complete && complete < total) || complete === 0) {
        actionNode = (
          <Button type="link" className="grid-link-btn" onClick={() => onDetail(taskId)}>
            详情
          </Button>
        );
      } else {
        actionNode = <span style={{ color: '#0fb698' }}>{' ___'}</span>;
      }
    } else if (status === TaskStatus.failed || status === TaskStatus.error) {
      if ((complete && complete === total) || (complete && complete < total)) {
        actionNode = (
          <Button type="link" className="grid-link-btn" onClick={() => onProblemDetail(taskId)}>
            问题详情
          </Button>
        );
      } else {
        actionNode = (
          <Button type="link" className="error-info grid-link-btn" onClick={() => onRetry(taskId)}>
            重新解析
          </Button>
        );
      }
    }

    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {actionNode}
        {status === TaskStatus.success && (
          <Button type="link" className="grid-link-btn" onClick={(e) => onApply(e, rowData)}>
            人工审核
          </Button>
        )}
        <Dropdown
          menu={{
            items: ACTION_ITEMS,
            onClick: handleActionMenuClick(taskId),
          }}
        >
          <a onClick={(e) => e.preventDefault()}>
            <Space>
              更多
              <CaretDownOutlined />
            </Space>
          </a>
        </Dropdown>
      </div>
    );
  };

  const columns: ColumnsType<TaskType> = [
    {
      title: '序号',
      dataIndex: 'index',
      render: (_text, _record, index) => <>{tableIndex + index + 1}</>,
      width: '4.6%',
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      shouldCellUpdate: () => true,
      render: (_, taskInfo) => {
        const { taskName, taskId } = taskInfo;
        return !renameList.has(taskId) ? (
          <a className="dataset-name">{taskName.replace(/"/g, '')}</a>
        ) : (
          <Space.Compact>
            <InputComponent
              taskInfo={taskInfo}
              renameList={renameList}
              setRenameList={setRenameList}
              getTasksList={getTasksList}
            />
          </Space.Compact>
        );
      },
      width: '13.2%',
    },
    {
      title: '创建时间',
      dataIndex: 'createdate',
      width: '9.6%',
      render: (_, { createTime }) => formattedTime(new Date(createTime)),
    },
    {
      title: () => {
        if (taskData.some((item) => item.failedReason)) {
          return (
            <Space>
              任务状态
              <Badge color="#E75252" />
            </Space>
          );
        }
        return '任务状态';
      },
      dataIndex: 'status',
      render: (_, { taskId, status, complete, total }) => {
        if (status === TaskStatus.error) {
          if (complete && complete === total) {
            return (
              <Space>
                <label className="warning-info">任务完成{`(${complete}/${total})`}</label>
                <QuestionCircleFilled
                  style={{ color: '#E75252', cursor: 'pointer' }}
                  onClick={() => onProblemDetail(taskId)}
                />
              </Space>
            );
          } else if (complete && complete < total) {
            return <label className="warning-info">进行中{`(${complete}/${total})`}</label>;
          }
        } else if (status === TaskStatus.failed) {
          return <label className="error-info">任务失败</label>;
        } else if (status === TaskStatus.inProgress) {
          return <label>进行中{`(${complete}/${total})`}</label>;
        } else if (status === TaskStatus.success) {
          if (complete && complete === total) {
            return <label className="default-info">任务完成{`(${complete}/${total})`}</label>;
          }
        }
        return null;
      },
      width: '10.4%',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      render: (_, { creator }) => <label>{creator.userName}</label>,
      width: '8%',
    },
    {
      title: '可见成员',
      dataIndex: 'visiblerange',
      render: (_, { taskId, reviewers }) => (
        <VisableRangeSelect
          className="visableselect"
          defaultSelectUser={reviewers}
          onChange={(users: string[]) => onVisibleRangeChange(taskId, users)}
          taskId={taskId}
        />
      ),
      width: '15%',
    },
    {
      title: '来源',
      dataIndex: 'source',
      render: (_, { description }) => (
        <label>{description === '本地上传' ? description : '训练数据'}</label>
      ),
      width: '8%',
    },
    {
      title: '数据审核进度',
      dataIndex: 'dataprocess',
      render: (_, { reviewCount, qaCount }) => (
        <label>{qaCount || qaCount === 0 ? `${reviewCount} / ${qaCount}` : '-'}</label>
      ),
      width: '9.3%',
    },
    {
      title: '操作',
      dataIndex: 'task',
      render: (_, rowData) => renderActionButtons(rowData),
      width: '20.8%',
    },
  ];

  return columns;
};
