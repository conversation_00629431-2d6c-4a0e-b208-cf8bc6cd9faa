import { Button, Descriptions, DescriptionsProps, Input, Progress, Space, Tooltip, message } from "antd";
import classes from './index.module.css';
import { TrainingType } from "../../type";
import OpenDraw from "../../../../../components/OpenDraw";
import { useEffect, useRef, useState } from "react";
import infoIcon from "../../../../../assets/img/info-icon.svg";
import infoIcon2 from "../../../../../assets/img/info-icon2.svg";
import { useNavigate, useParams } from "react-router-dom";
import { GetModlesParams, getModelOnline, getModelDetail, getModelSets, updataModel } from "../../../../../api/modle";
import { ModelDetailType, ModelSetType } from "../../../../../types";
import edit from "../../../../../assets/img/edit.svg"
import pagination from "antd/es/pagination";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

interface TrainingInfoProp {
    trainingData: TrainingType,
}

const TrainingInfo: React.FC<TrainingInfoProp> = ({ trainingData }) => {
    const { id } = useParams<string>();
    const intervalRef = useRef<NodeJS.Timer>();
    const navigate = useNavigate();
    const [openDrawView, setOpenDrawView] = useState(false);
    const [updataModelName, setUpdataModelName] = useState(trainingData.name);
    const [updataModelIntro, setUpdataModelIntro] = useState(trainingData.modelIntro)
    const [inputClicked, setInputClicked] = useState(false);
    const [inputClick, setInputClick] = useState(false);

    const infoItems: DescriptionsProps['items'] = [
        {
            key: '1',
            label: '模型名称',
            children: (
                <Tooltip title={trainingData.name}>
                    <div className={classes.descriptionText}>
                        {trainingData.name}
                    </div>
                </Tooltip>
            ),
            // children: <>
            //     <Space.Compact>
            //         <Input className={inputClicked ? '' : classes['input']} value={updataModelName === undefined ? trainingData.name : updataModelName}
            //             onClick={() => {
            //                 setInputClicked(true)
            //             }}
            //             onChange={(e) => {
            //                 setUpdataModelName(e.target.value);
            //             }}
            //         />
            //         <Button
            //             icon={<CheckOutlined />}
            //             style={{ display: inputClicked ? 'inline-block' : 'none' }}
            //             onClick={() => {
            //                 setInputClicked(false)
            //                 const newName: string = updataModelName?.trim() || "";
            //                 if (newName && newName.length > 0) {
            //                     const param = {
            //                         modelId: id as string,
            //                         modelName: updataModelName
            //                     }
            //                     // updataModel(param).then((res) => {
            //                     //     setUpdataModelName(newName)
            //                     // })
            //                 } else {
            //                     message.error("数据集名称不合法");
            //                 }
            //             }}
            //         />
            //         <Button
            //             icon={<CloseOutlined />}
            //             style={{ display: inputClicked ? 'inline-block' : 'none' }}
            //             onClick={() => {
            //                 //setUpdataModelName(updataModelName)
            //                 setInputClicked(false);
            //             }}
            //         />
            //         <Button type="text" onClick={() => setInputClicked(true)}>
            //             <img src={edit} />
            //         </Button>
            //     </Space.Compact>
            // </>
        },
        {
            key: '2',
            label: '模型介绍',
            children: (
                <Tooltip title={trainingData.desc}>
                    <div className={classes.descriptionText}>
                        {trainingData.desc}
                    </div>
                </Tooltip>
            ),

            // children: <>
            //     <Space.Compact>
            //         <Input className={inputClick ? '' : classes['input']} value={updataModelIntro === undefined ? trainingData.modelIntro : updataModelIntro}
            //             onClick={() => {
            //                 setInputClick(true)
            //             }}
            //             onChange={(e) => {
            //                 setUpdataModelIntro(e.target.value);
            //             }}
            //         />
            //         <Button
            //             icon={<CheckOutlined />}
            //             style={{ display: inputClick ? 'inline-block' : 'none' }}
            //             onClick={() => {
            //                 setInputClicked(false)
            //                 const newName: string = updataModelIntro?.trim() || "";
            //                 if (newName && newName.length > 0) {
            //                     const param = {
            //                         modelId: id as string,
            //                         modelIntro: updataModelIntro
            //                     }
            //                     // updataModel(param).then((res) => {
            //                     //     setUpdataModelIntro(newName)
            //                     // })
            //                 } else {
            //                     message.error("数据集名称不合法");
            //                 }
            //             }}
            //         />
            //         <Button
            //             icon={<CloseOutlined />}
            //             style={{ display: inputClick ? 'inline-block' : 'none' }}
            //             onClick={() => {
            //                 setInputClick(false);
            //                 //   const updateRenameSets = renameRows;
            //                 //   updateRenameSets.delete(dataSetId);
            //                 //   setRenameRows(updateRenameSets);
            //             }}
            //         />
            //         <Button type="text" onClick={() => setInputClick(true)}>
            //             <img src={edit} />
            //         </Button>
            //     </Space.Compact>
            // </>
        },
        {
            key: '3',
            label: '服务器信息',
            children: (
                <Tooltip title={trainingData.serverName}>
                    <div className={classes.descriptionText}>
                        {trainingData.serverName}
                    </div>
                </Tooltip>
            ),
        },
    ];

    const dataItems: DescriptionsProps['items'] = [
        {
            key: '1',
            label: '数据集',
            children: (
                <Tooltip title={trainingData.datasetName}>
                    <div className={classes.descriptionText}>
                        {trainingData.datasetName}
                    </div>
                </Tooltip>
            ),
        },
        {
            key: '2',
            label: '测试集比例',
            children: trainingData.testSetRadio,
        }
    ];

    const trainingItems: DescriptionsProps['items'] = [
        {
            key: '1',
            label: '基座模型',
            children: (
                <Tooltip title={trainingData.modelBaseName}>
                    <div className={classes.descriptionText}>
                        {trainingData.modelBaseName}
                    </div>
                </Tooltip>
            ),
        },
        {
            key: '2',
            label: '训练策略',
            children: trainingData.trainingStrategy,
        },
        {
            key: '3',
            label: '模型URL',
            children: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    {trainingData.modelUrl === '离线' && (
                        <Tooltip title={'请将模型上线显示URL'}>
                            <img
                                src={infoIcon2}
                                style={{ width: "16px", height: "16px", marginLeft: "-24px", position: "absolute" }}
                            />
                        </Tooltip>
                    )}
                    {trainingData.modelUrl === '离线' ? trainingData.modelUrl : <Tooltip title={trainingData.modelUrl}>
                        <div className={classes.descriptionText}>
                            {trainingData.modelUrl}
                        </div>
                    </Tooltip>}
                </div>
            ),
        }
    ];
    const learnRatio = (num: number) => {
        if (num === 0.00001) {
            return '1e-5';
        } else if (num === 0.00005) {
            return '5e-5';
        } else if (num === 0.0001) {
            return '1e-4';
        } else if (num === 0.0005) {
            return '5e-4';
        } else if (num === 0.001) {
            return '1e-3';
        }
    }
    const descItems: DescriptionsProps["items"] = [
        {
            key: "1",
            label: "模型ID",
            children: trainingData?.id
        },
        {
            key: "2",
            label: "模型介绍",
            children: trainingData?.modelIntro
        },
        {
            key: "3",
            label: "数据集",
            children: trainingData?.datasetName || '-'
        },
        {
            key: "4",
            label: "数据集比例",
            children: trainingData?.testSetRadio || '-'
        },
        {

            key: "5",
            label: "基座模型",
            children: trainingData?.modelBaseName || '-'
        },
        {
            key: "6",
            label: "训练策略",
            children: trainingData?.trainingStrategy || '-'

        },
        {
            key: "7",
            label: "迭代次数",
            children: trainingData?.interationNumber || '-'

        },
        {
            key: "8",
            label: "批次大小",
            children: trainingData?.batchSize || '-'

        },
        {
            key: "9",
            label: "学习率",
            children: learnRatio(trainingData?.learnRate) || '-'

        },
        {
            key: "10",
            label: <span style={{ color: "#000000", fontWeight: "bold" }}>模型状态</span>,
            children: trainingData?.status || '-'
        },
        {
            key: "11",
            label: <span style={{ color: "#000000", fontWeight: "bold" }}>训练进度</span>,
            children: trainingData?.trainProgress
        },
    ];

    const tooltip = (
        <span style={{ lineHeight: "25px" }}>
            增量训练：<br />
            结合该模型之前微调训练<br />
            的基座模型+历史训练<br />
            集数据再加上新数据<br />
            重新开始训练。
        </span>
    );

    return <>
        <Space direction={'vertical'}>
            <Descriptions style={{margin:'0 24px'}} title={<label className={classes['info-title']}>基本信息</label>} items={infoItems} column={1} bordered={false}  />
            <Descriptions style={{margin:'0 24px'}} title={<label className={classes['info-title']}>数据配置</label>} items={dataItems} column={1} bordered={false} />
            <Descriptions style={{margin:'0 24px'}} title={<label className={classes['info-title']}>训练配置</label>} items={trainingItems} column={1} bordered={false} />
            {/* <Button type="link" onClick={() => setOpenDrawView(true)}>更多信息</Button> */}
        </Space>
        <OpenDraw
            visible={openDrawView}
            titltext={trainingData.name}
            moduletext={trainingData.baseModel}
            buttontext={'增量训练'}
            logo={false}
            OnClose={() => {
                setOpenDrawView(false);
            }}
            onButtonClick={() => {
                navigate("/main/finetune/create")
            }}
        >
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                <div className="descriptions">
                    <Descriptions column={1}
                        style={{
                            padding: "1rem 0rem 2rem 0.6rem",
                        }}
                        size="small"
                        items={descItems}
                    />
                </div>
                <div style={{ position: "absolute", bottom: "46px", right: "25%" }}>
                    <Tooltip title={tooltip} >
                        <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
                    </Tooltip>
                </div>
            </div>
        </OpenDraw>
    </>;
}
export default TrainingInfo;