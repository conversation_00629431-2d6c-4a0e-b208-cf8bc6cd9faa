import { AxiosResponse } from "axios";
import {
  CustomResponseType,
  LoginParams,
  UserInfoParams,
  RegisterCodeParams,
  RegisterParams,
  VerifyCodeLoginParams,
  VerifyCodeParams,

} from "../types";
import { getBroswer, getOS } from "../utils/device";
import request from "../utils/request";

const browser = getBroswer();
const os = getOS();
const ip = "";
const device = "";

interface ReType extends CustomResponseType {
  token: string;
  user: any;
}
export interface logoInfoType {
  logoName: string;
  file?:any;
}
/**
 * 用户注册
 * @param registerParams 注册信息
 * @returns
 */
export function register(
  registerParams: RegisterParams
): Promise<AxiosResponse<ReType>> {
  registerParams.fingerPrint = { browser, os, ip, device };
  return request.post("/user/register", registerParams);
}

/**
 * 密码登录
 * @param loginParams 登录信息
 * @returns
 */
export function login(
  loginParams: LoginParams
): Promise<AxiosResponse<ReType>> {
  loginParams.fingerPrint = { browser, os, ip, device };
  return request.post("/user/login", loginParams);
}



/**
 * 验证码登录
 * @param loginParams 登录信息
 * @returns
 */
export function verifyCodeLogin(
  loginParams: VerifyCodeLoginParams
): Promise<AxiosResponse<ReType>> {
  loginParams.fingerPrint = { browser, device,  ip, os };
  return request.post("/user/login/code", loginParams);
}

/**
 * 获取登录验证码
 * @param verifyCodeParams 验证信息
 * @returns
 */
export function loginVerifyCode(
  verifyCodeParams: VerifyCodeParams
): Promise<AxiosResponse<CustomResponseType>> {
  verifyCodeParams.fingerPrint = { browser, device, ip, os };
  return request.post("/user/login/send-code", verifyCodeParams);
}

/**
 * 获取注册验证码
 * @param verifyCodeParams 验证信息
 * @returns
 */
export function registerVerifyCode(
  registerCodeParams: RegisterCodeParams
): Promise<AxiosResponse<CustomResponseType>> {
  registerCodeParams.fingerPrint = { browser, os, ip, device };
  return request.post("/user/register/send-code", registerCodeParams);
}
// 获取logo信息
export function getLogoInfo(): Promise<AxiosResponse> {
  return request.get("/logo/info");
}
// 修改logo信息
export function updateLogoInfo(params: logoInfoType){
  const formData = new FormData();
  formData.append("logoName", params.logoName);
  formData.append("file", params.file);
  return request.post("/logo/upload", formData,{
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
