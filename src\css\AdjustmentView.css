* {
    font-family: "HarmonyOS Sans SC", sans-serif;
}

.adjustmentDetail {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    max-width: 1920px;
    padding-top: 2.5rem;
    text-align: start;
    width: 92.1%;
    position: relative;
}

.headTitle {
    display: flex;
    margin: 2rem;
    width: 100%;
    justify-content: space-around;
    font-size: 18px;
    font-weight: bold;
    line-height: 21px;
    font-family: "HarmonyOS Sans SC, HarmonyOS Sans SC";
}

.adjustment-detail-info {
    /* width: 100%;
    text-align: start;
    justify-content: space-between;
    display: inline-flex;
    margin-bottom: 1rem;
    align-items: center; */
    background: linear-gradient(360deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
    box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
    border-radius: 24px;
    padding: 1rem 2.5rem ;
    margin-bottom: 1.25rem;
    margin-top: 1.25rem
}
.container-left-right {
    width: 100%;
    text-align: start;
    justify-content: space-between;
    display: inline-flex;
    margin-bottom: 1rem;
    align-items: center;
  }

.left {
    width: 50%;
    height: 70vh;
    border-right: 2px solid #F0F3F7;
    justify-content: space-between;
    display: flex;
    flex-direction: column;
    padding: 0 2rem;
}

/* .middle {
    width: 0;
    height: 600px;
    border: 2px solid #F0F3F7;
    position: absolute;
    left: 50%;
    top: 96px;
} */

.right {
    width: 50%;
    height: 70vh;
    padding: 0 2rem;
    justify-content: start;
    display: flex;
    flex-direction: column;
}

.adjiustModel {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
}

.selectModel {}

.talkcontent {
    height: 52vh;
    margin-left: 24px;
}

.chat-message-container {
    display: flex;
    margin-top: 20px;
}

.chat-message-ai,
.chat-message {
    margin: -10px 0 0 42px;
    border-radius: 12px;
    background: #FFFFFF;
    font-size: 15px;
    color: #111111;
    font-weight: 400;
}
.chat-message-ai-one,
.chat-message-one {
    margin: 0px 0 0 42px;
    border-radius: 12px;
    background: #FFFFFF;
    font-size: 15px;
    color: #111111;
    font-weight: 400;
    width: 80%;
    overflow-wrap: break-word;
}

.createtempratrueItem {
    margin: 2rem 0;
}

.temperatruelabel {
    font-size: 14px;
    min-width: 92px;
    margin-right: 6%;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 3rem;
}
.slider-container{
    margin: 2rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.slider-label {
    font-size: 14px;
    min-width: 92px;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 3rem;
}
.create-tasks-slider .ant-slider-handle::after,
.review-slider-hight .ant-slider-handle::after {
    background-image: url("../assets/img/slider-handle.svg");
    background-size: 36px;
    background-position-x: center;
    background-position-y: 28px;
}

.create-tasks-slider span.ant-slider-dot.ant-slider-dot-active,
.review-slider-hight span.ant-slider-dot.ant-slider-dot-active {
    background: #0FB698 !important;
}

.create-tasks-slider span.ant-slider-dot.ant-slider-dot-active,
.review-slider-hight span.ant-slider-dot.ant-slider-dot-active {
    border-color: #0FB698 !important;
}

.create-tasks-slider .ant-slider-handle::after,
.review-slider-hight .ant-slider-handle::after {
    box-shadow: 0 0 0 0 #0FB698 !important;
}

.newtalk {
    font-size: 14px;
    font-weight: 400;
    display: flex;
    color: #0FB698;
    justify-content: end;
    position: absolute;
    right: 20px;
    top: 122px
}

.publishButton {
    width: 126px;
    height: 40px;
    /* margin-left: 62rem; */
    /* position: absolute;
    right: 0;
    top: 22px; */
    background: #111111;
    border-radius: 24px 24px 24px 24px;
    font-size: 16px;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: bold;
    color: #FFFFFF;
}
.headerSpace .ant-space-item:last-child {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}
.chatTalk {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 60px;
    margin: 0 15px;
    padding-right: 15px;
    border-radius: 15px;
    width: 40%;
    height: 50px;
    border: 1px solid #7886AA;
    left: 51%;
    background-color: #FFFFFF;
}

.sendButton {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 70px;
    right: 4%;
    background-color: #FFFFFF;
}