.config-modal-container {
    padding: 26px 33px;
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

.default-label {
    color: #6D7279;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.title-label {
    color: #111;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.step-label {
    color: #6D7279;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.btn-label {
    color: #111;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.confirm-modal-footer {
    display: flex;
    justify-content: flex-end;
}

.confirm-btn {
    width: 124px;
    height: 40px;
    flex-shrink: 0;
    border-radius: 59px;
    background: #111;
}

.restore-default-btn {
    visibility: hidden;
}

.qreviewCriteria-container:hover .restore-default-btn,
.areviewCriteria-container:hover .restore-default-btn,
.scoreReviewCriteria-container:hover .restore-default-btn,
.scoreButtonInfo-container:hover .restore-default-btn {
    visibility: visible;
}

.modal-range-select {
    width: 70%;
}

.confirm-modal-footer-label {
    color: rgba(142, 152, 167, 1);
    text-align: left;
    
}