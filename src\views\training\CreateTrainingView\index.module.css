.training-btn {
    background-color: black;
    font-size: 14px;
    font-weight: 700;
    width: 122px
}

.training-header {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 14%;
}

.step-div {
    width: 57.8%;
}

.buttonstyle {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 350px;
}

.pre-next-div {
    /* position: absolute; */
    bottom: 2rem;
    text-align: center;
    width: 100%;
    margin-top: 50px;
    padding: 20px 50px;
    text-align: right;
    display: flex;
    justify-content: center;
}

.pre-btn {
    width: 106px;
    height: 48px;
    background: rgba(142, 152, 167, 1);
    position: absolute;
    left: 0;
}

.next-btn {
    width: 202px;
    height: 48px;
}

.pre-btn-result {
    width: 106px;
    height: 48px;
    background: rgba(142, 152, 167, 1);
    position: absolute;
    left: 0;
    bottom: 2rem;
}