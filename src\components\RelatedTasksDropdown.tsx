import { Button, Dropdown, Empty, Input, MenuProps, Modal, Popover, Space, message } from 'antd';
import { useEffect, useState } from 'react';
import { CaretDownOutlined, MinusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { DataSetStatus, DataSetType, TaskDeleteReviewType } from '../types';
import React from 'react';
import { CheckOutlined, CloseOutlined, RightOutlined, PlusOutlined } from '@ant-design/icons';
import { changeDataSetsTags, deleteDataset } from '../api/dataset';
import Table, { ColumnsType } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import { deleteTask } from '../api/task';

interface RelatedTasksDropdownProp {
  rowData: DataSetType;
  tags: string[];
  OnRename: () => void;
  OnChange: (tags: string[]) => void;
  OnEdit?: (edit: boolean) => void;
  OnDelete: (idList: string[]) => void;
}

interface Tag {
  key: string;
  label: any;
  edit?: boolean;
}

const RelatedTasksDropdown: React.FC<RelatedTasksDropdownProp> = ({
  rowData,
  tags,
  OnRename,
  // OnTagChange,
  OnChange,
  OnDelete,
  OnEdit,
}) => {
  const navigate = useNavigate();
  const [tagList, setTagList] = useState<Tag[]>([]);
  const [open, setOpen] = useState(false);
  const [actionOpen, setActionOpen] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>();
  const [addVal, setAddVal] = useState('');
  const { confirm } = Modal;
  const [relatedTasks, setRelatedTasks] = useState<MenuProps['items']>([
    // {
    //   key: "defaultItem",
    //   label: "--",
    // },
  ]);

  useEffect(() => {
    let item: MenuProps['items'] = [
      // {
      //   key: "defaultItem",
      //   label: "--",
      // },
    ];
    if (rowData.relatedQATaskList && rowData.relatedQATaskList.length > 0) {
      item = rowData.relatedQATaskList.map((task) => {
        return { key: task.taskId, label: task.taskName };
      });
    }
    setRelatedTasks(item);
  }, []);

  useEffect(() => {
    const _tags: Tag[] = [
      {
        key: 'addTag',
        label: '增加标签',
      },
    ];
    tags.forEach((tag) => {
      _tags.push({ key: tag, label: tag });
    });
    setTagList(_tags);
  }, [tags]);

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    console.log(e);
    navigate(`/main/task/detail/${e.key}`);
  };

  const handleOpenChange = (flag: boolean) => {
    if (flag && relatedTasks && relatedTasks.length) {
      setOpen(flag);
    } else if (!flag) {
      setOpen(flag);
    }
  };

  const handleActionMenuClick: MenuProps['onClick'] = (e) => {
    if (e.key === 'tag') {
      setOpenKeys(['tag']);
    } else if (e.key === 'rename') {
      OnRename();
      setActionOpen(false);
    } else if (e.key === 'delete') {
      setActionOpen(false);
      const hasRelatedTask = rowData.relatedQATaskList.length > 0;
      let relatedTaskList: any[] = [];
      if (hasRelatedTask) {
        relatedTaskList = [rowData]
          .map((item) =>
            item.relatedQATaskList.map((task) => {
              return {
                ...task,
                datasetName: item.name,
                dataSetId: item.id,
              };
            })
          )
          .reduce((pre, cur) => pre.concat(cur));
      }

      const columns: ColumnsType<TaskDeleteReviewType> = [
        {
          title: '数据集名称',
          dataIndex: 'datasetName',
          key: 'datasetName',
          render: (_, taskInfo) => {
            const { datasetName } = taskInfo;
            return <a className="dataset-name">{datasetName}</a>;
          },
        },
        {
          title: '任务名称',
          dataIndex: 'name',
          key: 'name',
          render: (_, taskInfo) => {
            const { taskName, taskId } = taskInfo;
            return (
              <a
                className="dataset-name"
                onClick={(e) => {
                  e.preventDefault();
                  console.log(e);
                  navigate(`/main/task/detail/${taskId}`);
                  modal.destroy();
                }}
              >
                {taskName}
              </a>
            );
          },
        },
        {
          title: 'Action',
          key: 'action',
          render: (_, { taskId }) => (
            <Space>
              <Button
                type="link"
                style={{
                  color: '#0fb698',
                  fontFamily: 'HarmonyOS Sans SC Reqular',
                }}
                onClick={() => {
                  navigate(`/main/task/detail/${taskId}`);
                  modal.destroy();
                }}
              >
                详情
              </Button>
              <Button
                type="link"
                style={{
                  color: '#0fb698',
                  fontFamily: 'HarmonyOS Sans SC Reqular',
                }}
                onClick={() => {
                  const taskModal = confirm({
                    centered: true,
                    title: '删除提示',
                    icon: <ExclamationCircleFilled />,
                    width: 540,
                    content: (
                      <>
                        <div className="default-info" style={{ color: 'black' }}>
                          确定要删除所选任务吗？
                        </div>
                      </>
                    ),
                    footer: [
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                          padding: '2rem 0 0 0',
                          gap: '8px',
                        }}
                      >
                        <Button type="text" onClick={() => taskModal.destroy()} shape="round">
                          取消
                        </Button>
                        <Button
                          type="primary"
                          onClick={() => {
                            relatedTaskList = relatedTaskList.filter(
                              (item) => item.taskId !== taskId
                            );
                            deleteTask([taskId]).then((res) => {
                              if (res.data.code === 200) {
                                taskModal.destroy();
                                modal.destroy();
                              } else {
                                message.error(res.data.message);
                              }
                            });
                          }}
                          shape="round"
                          className="primary-btn"
                          style={{ width: '120px' }}
                        >
                          确认删除
                        </Button>
                      </div>,
                    ],
                    async onOk() {
                      relatedTaskList = relatedTaskList.filter((item) => item.taskId !== taskId);
                      await deleteTask([taskId]).then((res) => {
                        if (res.data.code === 200) {
                          taskModal.destroy();
                          modal.destroy();
                        } else {
                          message.error(res.data.message);
                        }
                      });
                    },
                    onCancel() {
                      taskModal.destroy();
                    },
                  });
                }}
              >
                删除
              </Button>
            </Space>
          ),
        },
      ];
      const modal = confirm({
        centered: true,
        title: '删除提示',
        icon: <ExclamationCircleFilled />,
        width: 540,
        content: (
          <>
            <div className="default-info" style={{ color: 'black' }}>
              确定要删除所选数据集吗？
            </div>
            {hasRelatedTask ? (
              <>
                <div className="upload-error-label" style={{ marginTop: '8px' }}>
                  所选源数据集有关联的推理任务，无法进行删除。如需删除，请先删除关联任务。
                </div>
                <Table
                  scroll={{ y: 400 }}
                  size="small"
                  pagination={false}
                  columns={columns}
                  dataSource={relatedTaskList}
                  style={{ flex: 1 }}
                  tableLayout={'fixed'}
                  rowKey="taskId"
                  className="dataset-table"
                />
              </>
            ) : null}
          </>
        ),
        footer: [
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              padding: '2rem 0 0 0',
              gap: '8px',
            }}
          >
            <Button type="text" onClick={() => modal.destroy()} shape="round">
              取消
            </Button>
            <Button
              disabled={hasRelatedTask}
              type="primary"
              onClick={() => {
                OnDelete([rowData.id]);
                deleteDataset([rowData.id]).then((res) => {
                  modal.destroy();
                });
              }}
              shape="round"
              className="primary-btn"
              style={{ width: '120px' }}
            >
              确认删除
            </Button>
          </div>,
        ],
        onOk() {
          OnDelete([rowData.id]);
          deleteDataset([rowData.id]).then((res) => {
            modal.destroy();
          });
        },
        onCancel() {
          modal.destroy();
        },
      });
    }
  };

  const handleActionOpenChange = (flag: boolean) => {
    setActionOpen(flag);
  };

  let actionItems: MenuProps['items'] = [
    { key: 'rename', label: '重命名' },
    // {
    //   key: "tag",
    //   //   label: "标签",
    //   label: (
    //     <Popover
    //       destroyTooltipOnHide
    //       placement="right"
    //       overlayInnerStyle={{ width: "116px" }}
    //       zIndex={1060}
    //       onOpenChange={(open) => {
    //         if (!open) {
    //           setTagList(
    //             tagList.filter((item) => item.label && item.label.length > 0)
    //           );
    //           // const tags = tagList.map(tag => tag.label);
    //           // changeDataSetsTags({ dataSetId: rowData.dataSetId, tags }).then(() => {
    //           //   OnChange();
    //           // })
    //         }
    //       }}
    //       content={
    //         <div>
    //           {tagList.map((item, index) => {
    //             if (item.key === "addTag") {
    //               return (
    //                 <Button
    //                   key={item.key + 'addBtn'}
    //                   type="text"
    //                   style={{ width: "100%" }}
    //                   size="small"
    //                   onClick={() => {
    //                     const tags = tagList;
    //                     tags[0] = {
    //                       key: `newAdd${tagList.length}`,
    //                       label: "",
    //                       edit: true,
    //                     };
    //                     setTagList(tags);
    //                   }}
    //                 >
    //                   <PlusOutlined />
    //                   {item.label}
    //                 </Button>
    //               );
    //             } else if (item.edit) {
    //               return (
    //                 <Space.Compact
    //                   key={item.key + 'Space'}
    //                   style={{
    //                     padding: "5px 0",
    //                   }}
    //                 >
    //                   <Input
    //                     maxLength={5}
    //                     size="small"
    //                     onChange={(e) => {
    //                       setAddVal(e.target.value);
    //                     }}
    //                     onBlur={() => {
    //                       const tags = tagList;
    //                       tags[0] = {
    //                         key: "addTag",
    //                         label: "增加标签",
    //                       };
    //                       setTagList(tags);
    //                       const updateTags = tags.filter(item => item.key !== 'addTag').map(item => item.label);
    //                       changeDataSetsTags({ dataSetId: rowData.dataSetId, tags: updateTags }).then(() => {
    //                         OnChange(updateTags);
    //                       })
    //                     }}
    //                   />
    //                   <Button
    //                     key={item.key + 'btn'}
    //                     type="text"
    //                     size="small"
    //                     onClick={() => {
    //                       if (addVal && addVal.length > 0) {
    //                         const tags = tagList;
    //                         tags.push({
    //                           key: `newAdd${tagList.length}`,
    //                           label: addVal,
    //                         });
    //                         setTagList(tags);

    //                         const updateTags = tags.filter(item => item.key !== 'addTag').map(item => item.label);
    //                         changeDataSetsTags({ dataSetId: rowData.dataSetId, tags: updateTags }).then(() => {
    //                           OnChange(updateTags);
    //                         })
    //                       }
    //                     }}
    //                   >
    //                     <CheckOutlined />
    //                   </Button>
    //                 </Space.Compact>
    //               );
    //             } else {
    //               return (
    //                 <div
    //                   key={index + "div"}
    //                   style={{
    //                     width: "100%",
    //                     display: "inline-flex",
    //                     justifyContent: "space-between",
    //                     padding: "5px 0",
    //                   }}
    //                 >
    //                   <div className="text-ellipsis">{item.label}</div>
    //                   <Button
    //                     type="text"
    //                     size="small"
    //                     onClick={() => {
    //                       const updateTags = tagList.filter((tag) => tag.key !== item.key);
    //                       setTagList(updateTags);
    //                       const tags = updateTags.filter(item => item.key !== 'addTag').map(item => item.label);
    //                       changeDataSetsTags({ dataSetId: rowData.dataSetId, tags }).then(() => {
    //                         OnChange(tags);
    //                       })
    //                     }}
    //                   >
    //                     <CloseOutlined />
    //                   </Button>
    //                 </div>
    //               );
    //             }
    //           })}
    //         </div>
    //       }
    //     >
    //       标签
    //       <RightOutlined />
    //     </Popover>
    //   ),
    //   //   children: tagList,
    // },
    { key: 'delete', label: '删除' },
  ];

  // if (rowData.relatedTasks) {
  //   const tasks = rowData.relatedTasks.map((item, index) => {
  //     return {
  //       key: item + index,
  //       label: (
  //         <a
  //           target="_blank"
  //           rel="noopener noreferrer"
  //           href="https://www.antgroup.com"
  //         >
  //           {item}
  //         </a>
  //       ),
  //     };
  //   });
  //   if (tasks.length > 0) {
  //     items = tasks;
  //   }
  // }

  if (rowData.datasetStatus === DataSetStatus.sucess) {
    return (
      <div
        style={{
          display: 'inline-flex',
          justifyContent: 'space-around',
          width: '100%',
        }}
      >
        {!relatedTasks || relatedTasks?.length === 0 ? (
          <a>
            <MinusOutlined />
            <MinusOutlined />
          </a>
        ) : (
          <Dropdown
            menu={{
              items: relatedTasks,
              onClick: handleMenuClick,
            }}
            // disabled={!relatedTasks || relatedTasks?.length === 0}
            onOpenChange={handleOpenChange}
            open={open}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                相关任务
                <CaretDownOutlined />
              </Space>
            </a>
          </Dropdown>
        )}

        <Dropdown
          menu={{
            items: actionItems,
            onClick: handleActionMenuClick,
          }}
          onOpenChange={handleActionOpenChange}
          open={actionOpen}
        >
          <a onClick={(e) => e.preventDefault()}>
            <Space>
              更多
              <CaretDownOutlined />
            </Space>
          </a>
        </Dropdown>
      </div>
    );
  } else {
    return (
      <div
        style={{
          display: 'inline-flex',
          justifyContent: 'space-around',
          width: '100%',
        }}
      >
        <a>
          <MinusOutlined />
          <MinusOutlined />
        </a>
        <Dropdown
          menu={{
            items: actionItems,
            onClick: handleActionMenuClick,
          }}
          onOpenChange={handleActionOpenChange}
          open={actionOpen}
        >
          <a onClick={(e) => e.preventDefault()}>
            <Space>
              更多
              <CaretDownOutlined />
            </Space>
          </a>
        </Dropdown>
      </div>
    );
  }
  // else return <></>;
};

export default RelatedTasksDropdown;
