import { FolderType } from './type';
import { DisplayOCRChapters, OCRChapters } from './components/ResultView/type';

export function getParentNodeById(id: string, folders: FolderType[]): FolderType | undefined {
  for (const folder of folders) {
    if (folder.id === id) {
      return;
    }
    const parent = getParentNodeById(id, folder.children);
    if (parent) {
      return parent;
    }
  }
}

export function getChildIndexById(id: string, children: FolderType[]): number {
  for (let i = 0; i < children.length; i++) {
    if (children[i].id === id) {
      return i;
    }
    const indexInChild = getChildIndexById(id, children[i].children);
    if (indexInChild !== -1) {
      return indexInChild;
    }
  }
  return -1;
}

export function filterText(treeData: OCRChapters[]): OCRChapters[] {
  return treeData
    .filter((node) => node.level !== -1)
    .map((filteredNode) => ({
      ...filteredNode,
      children: filteredNode.children && filteredNode.children.length > 0 ? filterText(filteredNode.children) : [],
    }));
}

export function generateId() {
  return Math.random().toString(36).substring(2, 10);
}

export function generateUniqueIds(treeData: OCRChapters[]): DisplayOCRChapters[] {
  return treeData.map((node) => {
    // let uniqueId;
    // if (parentId === '') {
    //   // 如果是根节点，则直接生成一个随机字符串作为ID
    //   uniqueId = generateId();
    // } else {
    //   // 如果不是根节点，将父节点ID与当前子节点的位置拼接起来生成唯一ID
    //   uniqueId = `${parentId}-${treeData.indexOf(node)}`;
    // }

    const newNode: DisplayOCRChapters = { ...node, displayId: generateId() } as DisplayOCRChapters;

    // 如果当前节点有子节点，递归处理它们
    if (Array.isArray(newNode.children)) {
      newNode.children = generateUniqueIds(newNode.children);
    }

    return newNode;
  });
}

export const removeIdsFromTree = (tree: DisplayOCRChapters[]): OCRChapters[] => tree.map((node) => {
  // 设置当前节点的id属性为null，也可以设置为undefined等
  const updatedNode: any = node;
  delete updatedNode.displayId;

  if (node.children && node.children.length > 0) {
    // 如果节点有子节点，递归调用该函数在子节点中删除id属性
    updatedNode.children = removeIdsFromTree(node.children);
  }

  return updatedNode;
});

export const insertNodeInTree = (
  tree: DisplayOCRChapters[],
  targetNodeId: string,
  newNode: DisplayOCRChapters,
  isBefore: boolean
): DisplayOCRChapters[] => tree.map((node) => {
  if (node.displayId === targetNodeId) {
    // 在目标节点之前或之后插入新节点
    if (isBefore) {
      return [newNode, node];
    }
    return [node, newNode];
  }
  if (node.children && node.children.length > 0) {
    // 如果节点有子节点，递归调用该函数在子节点中插入新节点
    return { ...node, children: insertNodeInTree(node.children, targetNodeId, newNode, isBefore) };
  }
  // 如果节点既不是目标节点也没有子节点，保持原样
  return node;
}).flat();

export const updateNodeInTree = (
  tree: DisplayOCRChapters[],
  nodeId: string,
  newNode: DisplayOCRChapters
): DisplayOCRChapters[] => tree.map((node) => {
  if (node.displayId === nodeId) {
    // 如果找到匹配的节点，更新其text属性
    return { ...newNode };
  } if (node.children && node.children.length > 0) {
    // 如果节点有子节点，递归调用该函数在子节点中查找并更新
    return { ...node, children: updateNodeInTree(node.children, nodeId, newNode) };
  }
  // 如果节点既不是目标节点也没有子节点，保持原样
  return node;

});

export const removeNodeFromTree = (
  tree: DisplayOCRChapters[],
  targetNodeId: string
): DisplayOCRChapters[] => tree.map((node) => {
  if (node.displayId === targetNodeId) {
    // 如果找到目标节点，返回空数组表示删除
    return [];
  } if (node.children && node.children.length > 0) {
    // 如果节点有子节点，递归调用该函数在子节点中删除目标节点
    return { ...node, children: removeNodeFromTree(node.children, targetNodeId) };
  }
  // 如果节点既不是目标节点也没有子节点，保持原样
  return node;

}).flat();

export function getTreeParentNodeById(displayId: string, chapters: DisplayOCRChapters[]): DisplayOCRChapters | undefined {
  for (const chapter of chapters) {
    if (chapter.displayId === displayId) {
      return;
    }
    const parent = getTreeParentNodeById(displayId, chapter.children);
    if (parent) {
      return parent;
    }
  }
}

export function getTreeNodeById(displayId: string, chapters: DisplayOCRChapters[]): DisplayOCRChapters | undefined {
  for (const chapter of chapters) {
    if (chapter.displayId === displayId) {
      return chapter;
    }

    const res = getTreeNodeById(displayId, chapter.children);
    if (res) {
      return res;
    }
  }
}

// 查找chapters树列表上任意节点的前一个兄弟节点
export const findPreviousSibling = (
  tree: DisplayOCRChapters[],
  targetNodeId: string,
  parentNodeId?: string | null
): DisplayOCRChapters | null => {
  let previousSibling: DisplayOCRChapters | null = null;

  for (const node of tree) {
    if (node.displayId === targetNodeId && parentNodeId) {
      // 如果找到目标节点且指定了父节点ID，返回前一个兄弟节点
      const siblings = tree.filter((sibling) => sibling.displayId !== targetNodeId && sibling.displayId !== parentNodeId);
      const index = siblings.findIndex((sibling) => sibling.displayId === targetNodeId);
      previousSibling = index > 0 ? siblings[index - 1] : null;
    } else if (node.children && node.children.length > 0) {
      // 如果节点有子节点，递归调用该函数在子节点中查找前一个兄弟节点
      previousSibling = findPreviousSibling(node.children, targetNodeId, node.displayId);
      if (previousSibling) {
        break; // 如果找到了前一个兄弟节点，跳出循环
      }
    }
  }

  return previousSibling;
};


export const flattenTree = (tree: DisplayOCRChapters[]): DisplayOCRChapters[] => {
  const flatArray: DisplayOCRChapters[] = [];

  const flattenTreeData = (_flatArray: DisplayOCRChapters[]) => {
    _flatArray.forEach((item) => {
      const data = JSON.parse(JSON.stringify(item));
      data.children = null;

      flatArray.push(data);
      if (item.children && item.children.length > 0) {
        console.log('data', data, flatArray);
      }
      if (item.children && item.children.length > 0) {
        flattenTreeData(item.children);
      }
    });
  }

  flattenTreeData(tree);

  return flatArray;
};


const getParent = (flatArray: DisplayOCRChapters[],index: number) => {
  let parentNode;
  for (let i = index - 1; i >= 0; i--) {
    const item = flatArray[i];
    if (item.type === "title") {
      parentNode = item;
      break;
    }
  }
  return parentNode;
}

const getChildrenIndex = (flatArray: DisplayOCRChapters[], data: DisplayOCRChapters, index: number): number[] => {
  const childrenIndex: number[] = [];
  if (data.level !== -1) {
    for (let i = index + 1; i < flatArray.length; i++) {
      const item = flatArray[i];
      if (item.level !== -1 && item.level <= data.level) break;
      if (item.level !== -1 && item.level - data.level === 1) {
        childrenIndex.push(i);
      } else if (
        item.level === -1 &&
        getParent(flatArray, i)?.text === data?.text
      ) {
        childrenIndex.push(i);
      }
    }
  }
  return childrenIndex;
}

export const parseChaptersTreeData = (
  flatArray: DisplayOCRChapters[],
  data: number[],
  flag?: boolean
): DisplayOCRChapters[] => {
  // const dataList = data.map((d) => this.list[d]);
  const chapters: DisplayOCRChapters[] = [];
  data.forEach((index) => {
    const item = flatArray[index];
    if (!flag || (flag && item.type === "title" && item.level === 1)) {
      const indexList = getChildrenIndex(flatArray, item, index);
      const chaptersData = item as DisplayOCRChapters;
      if (indexList.length > 0) {
        chaptersData.children = parseChaptersTreeData(flatArray, indexList);
      }
      chapters.push(chaptersData);
    }
  });
  return chapters;
}

export const buildTreeFromFlatArray = (flatArray: DisplayOCRChapters[]): DisplayOCRChapters[] => {
  const list = flatArray
    .map((item, i) => {
      if (item.type === "title" && item.level === 1){ 
        return i;
      }
      if(item.type === "text" && !getParent(flatArray, i)) {
        return i;
      }
      return null;
    }).filter((d) => d !== null);
    const chaptersList: DisplayOCRChapters[] = parseChaptersTreeData(
      flatArray,
      list as number[]
    );
    return chaptersList;
};


