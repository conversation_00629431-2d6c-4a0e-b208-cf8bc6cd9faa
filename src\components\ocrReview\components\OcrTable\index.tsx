import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import jexcel, { CellValue, Column, ToolbarIconItem } from 'jspreadsheet-ce'
import jspreadsheet from 'jspreadsheet-ce';
import './index.module.css';
// import 'jspreadsheet-ce/dist/jspreadsheet.css';

interface OcrTableProp {
    data: any;
    HightlineText: (res: any[]) => void
}
const OcrTable = forwardRef((prop: OcrTableProp, ref) => {
    const { data, HightlineText } = prop;
    // useImperativeHandle(ref, () => ({
    //     HightlineText: () => {},
    //   }));
    const conlumns: Column[] = [];
    const tableData: CellValue[][] = [];
    const mergeCellMap: { [key: string]: [number, number] } = {}
    const excelSheet = useRef<HTMLDivElement>(null);
    const jtalbe = useRef<any>();
    const currentCell = useRef();
    const currentRowIndex = useRef();
    const currentColumnIndex = useRef();
    let mountedFlag = false;

    const initTable = () => {
        data.text_cell[0].forEach((_cell: any) => {
            conlumns.push({
                type: 'text', title: _cell.text,
            })
        });
        for (let i = 0; i < data.text_cell.length; i++) {
            const _cell = data.text_cell[i];
            const rowData = _cell.map((item: { text: any; }) => item.text)
            tableData.push(rowData)
        }
        data.cell_merged.forEach((_merge: any) => {
            const key = String.fromCharCode(65 + _merge[2]) + (_merge[0] + 1)
            const val: [number, number] = [_merge[3] - _merge[2] + 1, _merge[1] - _merge[0] + 1]
            mergeCellMap[key] = val
        });
        if (excelSheet.current && jtalbe.current) {
            try {
                jtalbe.current = jexcel(excelSheet.current, {
                    data: tableData,
                    minDimensions: [data.cols, data.rows], // 最小[列,行]数
                    tableWidth: '1000px', // 表格宽度
                    tableHeight: '100%',
                    defaultColWidth: 80, // 默认列宽
                    mergeCells: mergeCellMap,
                    allowComments: false,
                    // contextMenu(instance, colIndex, rowIndex, event) {
                    //     return []
                    // },
                    // toolbar: [
                    //     {
                    //         type: 'i',
                    //         content: '合并',
                    //         onclick() {
                    //             jtalbe.value.setMerge()
                    //         }
                    //     },
                    //     {
                    //         type: 'i',
                    //         content: '拆分',
                    //         onclick: function () {
                    //             if (currentCell.value) {
                    //                 jtalbe.value.removeMerge(currentCell.value);
                    //             }
                    //         }
                    //     },
                    //     {
                    //         type: 'i',
                    //         content: '添加行',
                    //         onclick: function () {
                    //             if (currentRowIndex.value !== null && currentRowIndex.value !== undefined) {
                    //                 jtalbe.value.insertRow(1, currentRowIndex.value);
                    //             }
                    //         }
                    //     },
                    //     {
                    //         type: 'i',
                    //         content: '删除行',
                    //         onclick: function () {
                    //             if (currentRowIndex.value !== null && currentRowIndex.value !== undefined) {
                    //                 jtalbe.value.deleteRow(currentRowIndex.value, 1);
                    //             }
                    //         }
                    //     },
                    //     {
                    //         type: 'i',
                    //         content: '添加列',
                    //         onclick: function () {
                    //             if (currentColumnIndex.value !== null && currentColumnIndex.value !== undefined) {
                    //                 jtalbe.value.insertColumn(1, currentColumnIndex.value);
                    //             }
                    //         }
                    //     },
                    //     {
                    //         type: 'i',
                    //         content: '删除列',
                    //         onclick: function () {
                    //             if (currentColumnIndex.value !== null && currentColumnIndex.value !== undefined) {
                    //                 jtalbe.value.deleteColumn(currentColumnIndex.value, 1);
                    //             }
                    //         }
                    //     },
                    // ],
                    // text: {
                    //     cellAlreadyMerged: ''
                    // },
                    // onselection: (element, borderLeftIndex, borderTopIndex, borderRightIndex, borderBottomIndex, origin) => {
                    //     if (mountedFlag) {
                    //         currentCell.current = String.fromCharCode(65 + borderLeftIndex) + (borderTopIndex + 1);
                    //         // console.log(borderTopIndex)
                    //         currentRowIndex.current = borderTopIndex;
                    //         currentColumnIndex.current = borderLeftIndex;

                    //         const res = data.text_cell && data.text_cell[borderTopIndex] && data.text_cell[borderTopIndex][borderLeftIndex]
                    //             ? data.text_cell[borderTopIndex][borderLeftIndex].ocr_index : [];
                    //         HightlineText(res)
                    //     }
                    // },
                })
                mountedFlag = true
            } catch (error) {
                console.log(error);
            }
        }
    }
    // useEffect(() => {
    //     initTable();
    // }, []);

    useEffect(() => {
        if (excelSheet.current) {
          // 初始化 jSpreadsheet
          jspreadsheet(excelSheet.current, {
            data: [[]], // 初始数据
            columns: [ // 列定义
              { type: 'text', title: 'Column 1' },
              { type: 'text', title: 'Column 2' },
              // 添加更多列...
            ],
          });
        }
      }, []); // 这里的空数组表示仅在组件挂载时运行一次
    return <>
        <div>
            <div v-if="data.caption.length === 0" className={"no-title"}>---暂无标题---</div>
            <div ref={excelSheet}></div>
        </div></>
})

export default OcrTable;