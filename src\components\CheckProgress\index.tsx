import infoIcon from '@/assets/img/info-icon.svg';
import { formattedTime } from '@/utils/formatred';
import { copyToClipboard } from '@/utils/copytext';
import { Avatar, Button, Descriptions, Empty, Modal, Progress, Tooltip, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { AlluserInfoType, TaskDetailType, TaskStatus, ProgressAllInfo } from '@/types';
import { querygetProgress, queryUserDetail } from '@/api/task';
import { useParams } from 'react-router-dom';
import Table, { ColumnsType } from 'antd/es/table';
import emptyLogo from '@/assets/img/empty-logo.svg';

import useTaskStatusMap from './hooks/useTaskStatusMap';

interface DatasetExportModalProp {
  visible: boolean;
  OnClose: () => void;
  taskDetail?: TaskDetailType;
  OnQADonload?: () => void;
  userName?: String;
  userAvatar?: string;
}

const CheckProgress: React.FC<DatasetExportModalProp> = ({
  visible,
  OnClose,
  taskDetail,
  userName,
  userAvatar,
}) => {
  const [progressDetail, setProgressDetail] = useState<AlluserInfoType[]>();
  const progressNoRef = useRef(progressDetail);
  const [apiFlg, setApiFlg] = useState(false);
  const [filterAttribute, setFilterAttribute] = useState('taskName');
  const [filterTaskName, setFilterTaskName] = useState<string>();
  const [progressInfo, setProgressInfo] = useState<ProgressAllInfo[]>([]);
  const [selectedValue, setSelectedValue] = useState('切换为总数量');
  const [pagination] = useState({ page: 1, size: 10, total: 0 });
  const { task_id } = useParams<string>();
  const intervalRef = useRef<NodeJS.Timeout>();

  const [messageApi, contextHolder] = message.useMessage();
  const info = () => messageApi.info('复制成功');

  const ProgressInfo = (taskId: string) => {
    if (!apiFlg) {
      setApiFlg(true);
      querygetProgress(taskId).then((res) => {
        setApiFlg(false);
        const data = res?.data;
        if (data) {
          const progressData = Array.isArray(data) ? data : [data];
          setProgressInfo(progressData);
        }
      });
    }
  };

  const descItems = [
    { key: '1', label: '任务名称', children: taskDetail?.taskName || 'NULL' },
    {
      key: '2',
      label: '创建用户',
      children: (
        <>
          <span className="namesytle">{userName}</span>
          <Avatar className="ellipse-parent29-1" src={userAvatar} />
        </>
      ),
    },
    { key: '3', label: '任务ID', children: taskDetail?.id || 'NULL' },
    {
      key: '4',
      label: '创建时间',
      children: taskDetail?.createTime ? formattedTime(new Date(taskDetail.createTime)) : 'NULL',
    },
    {
      key: '5',
      label: '任务状态',
      children: (
        <>
          {useTaskStatusMap(taskDetail)}（{taskDetail?.complete + ' / ' + taskDetail?.total}）
        </>
      ),
    },
    {
      key: '6',
      label: '审核进度',
      children:
        taskDetail && taskDetail?.taskStatusEnum !== 'FAILED' ? (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <label>{taskDetail.reviewCount + ' / ' + taskDetail.qaCount}</label>
            <Tooltip title="为保证导出数据质量，请在导出前进行人工审核">
              <img src={infoIcon} style={{ width: '16px', height: '16px' }} />
            </Tooltip>
          </div>
        ) : (
          <label>-</label>
        ),
    },
  ];

  const textId: string = descItems[2].children as string;

  const columns: ColumnsType<ProgressAllInfo> = [
    {
      title: '成员',
      dataIndex: 'userName',
      render: (_, record) => {
        const userNmae = record.user.userName;
        return (
          <>
            <Avatar style={{ display: 'flex', margin: '0 0 0 1.2rem' }} src={userAvatar} />
            <>
              <div style={{ display: 'flex', margin: '-1.6rem 0px 0px 4.7rem' }}>{userNmae}</div>
            </>
          </>
        );
      },
      width: '20.6%',
      align: 'center',
    },
    {
      title: '审核数量',
      dataIndex: 'number',
      render: (_, { curAllocated, curReviewed }) =>
        selectedValue === '切换为总数量' ? curAllocated : curReviewed,
      width: '21.6%',
      align: 'center',
    },
    {
      title: '比例',
      dataIndex: 'reviewCount',
      width: '10.6%',
      align: 'center',
      render: (_, { totalProgress, curProgress }) =>
        selectedValue === '切换为总数量'
          ? (totalProgress * 100).toFixed(2) + '%'
          : (curProgress * 100).toFixed(2) + '%',
    },
    {
      title: ' ',
      dataIndex: 'dataprocess',
      render: (_, { totalProgress, curProgress }) => (
        <Progress
          strokeLinecap="butt"
          showInfo={false}
          size={[220, 15]}
          percent={(selectedValue === '切换为总数量' ? totalProgress : curProgress) * 100}
        />
      ),
      width: '27.3%',
    },
  ];

  const getProgress = async (taskId: string) => {
    try {
      const res = await queryUserDetail(taskId);
      setApiFlg(false);
      const data = res.data;
      setProgressDetail(Array.isArray(data) ? data : undefined);
    } catch {}
  };

  const getProgressDetial = async () => {
    if (task_id) {
      await getProgress(task_id);
      ProgressInfo(task_id);
    }
  };

  useEffect(() => {
    progressNoRef.current = progressDetail;
  });

  useEffect(() => {
    getProgressDetial();
    return () => clearInterval(intervalRef.current);
  }, []);

  useEffect(() => {
    clearInterval(intervalRef.current);
    return () => clearInterval(intervalRef.current);
  }, [pagination.page, pagination.size, filterAttribute, filterTaskName, progressDetail]);

  return (
    <Modal
      centered
      className="preview-folder-modal"
      title="审核进度"
      keyboard={false}
      maskClosable={false}
      styles={{ body: { height: '528px' } }}
      width={'870px'}
      open={visible}
      onCancel={OnClose}
      footer={[]}
    >
      <div className="task-detail-info">
        <div style={{ position: 'absolute', left: '18rem', top: '7.4rem', color: '#0FB698' }}>
          {contextHolder}
          <Button
            type="link"
            size="small"
            onClick={() => {
              info();
              copyToClipboard(textId);
            }}
            style={{ position: 'relative', left: '-4rem', width: '5rem' }}
          />
        </div>
        <div className="line"></div>
        <Descriptions
          column={2}
          style={{ padding: '2rem 1rem', width: '50rem' }}
          size="small"
          items={descItems}
        />
      </div>
      <div style={{ color: '#6D7279' }} className="progress-process">
        进度进程明细
      </div>
      <Table
        virtual
        locale={{
          emptyText: (
            <Empty
              image={emptyLogo}
              description={
                <span className="dataset-table-empty-label">空空如也，去上传本地文件吧~</span>
              }
            />
          ),
        }}
        style={{ flex: 1 }}
        tableLayout={'fixed'}
        rowKey="taskId"
        className="dataset-table"
        columns={columns}
        dataSource={progressInfo}
        pagination={false}
        scroll={{ y: 250 }}
      />
    </Modal>
  );
};

export default CheckProgress;
