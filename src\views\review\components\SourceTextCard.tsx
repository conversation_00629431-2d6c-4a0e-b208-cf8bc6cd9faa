import React from 'react';
import { Card, Typography } from 'antd';
import { FileContent, HighlightIdx } from '../../../types';
import Scrollbars from 'react-custom-scrollbars';
import HighlightedText from '@/components/HighlightedText';

interface SourceTextCardProps {
  fileContent: FileContent[];
  highlightIdxList: HighlightIdx[];
  qaContent?: string;
}

const SourceTextCard: React.FC<SourceTextCardProps> = ({ highlightIdxList, qaContent }) => {
  return (
    <div className="default-review-lf">
      <Card
        title={
          <div className="boldText" style={{ fontSize: 14 }}>
            原文对照
          </div>
        }
        headStyle={{
          height: '52px',
          fontWeight: 500,
          fontFamily: 'HarmonyOS Sans SC Medium',
          minHeight: 'unset',
        }}
        bordered={false}
        style={{ width: '100%', height: '95%', textAlign: 'start' }}
        bodyStyle={{ padding: '20px 24px' }}
      >
        {qaContent ? (
          <>
            <div
              style={{
                height: '680px',
                textAlign: 'start',
                marginTop: '16px',
                lineHeight: '26px',
                color: '#6D7279',
                overflowWrap: 'break-word',
                whiteSpace: 'pre-line',
                width: '100%',
                padding: '5px 10px',
              }}
            >
              <Scrollbars autoHide autoHideTimeout={1000} autoHideDuration={200}>
                <HighlightedText text={qaContent} highlights={highlightIdxList} />
              </Scrollbars>
            </div>
          </>
        ) : null}
      </Card>
      <div className="taskDetailBottom">
        溯源置信度 : &nbsp;
        <span className="span1"> 示例</span>
        {'≥'}90%&nbsp;&nbsp;&nbsp;&nbsp;
        <span className="span2"> 示例</span>
        {'≥'}80%&nbsp;&nbsp;&nbsp;&nbsp;
        <span className="span3"> 示例</span>
        {'≥'}70%
      </div>
    </div>
  );
};

export default SourceTextCard;
