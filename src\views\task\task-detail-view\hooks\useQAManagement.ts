import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { QADocument, HighlightIdx } from '../../../../types';
import {
  QAInfoType,
  getQAList,
  deleteQA,
  exportQA,
  QaDeleteInfo,
  QaExportParams,
} from '../../../../api/qa';
import { saveAs } from 'file-saver';

interface UseQAManagementProps {
  taskId: string;
  fileList: string[];
  filterVal?: string;
  reviewFilterType?: any;
  approvedItems?: any;
  allocatedUser?: string;
  scoreFilter?: string;
  selectedTags: Set<string>;
  pagination: {
    page: number;
    size: number;
    total: number;
  };
}

export const useQAManagement = ({
  taskId,
  fileList,
  filterVal,
  reviewFilterType,
  approvedItems,
  allocatedUser,
  scoreFilter,
  selectedTags,
  pagination,
}: UseQAManagementProps) => {
  const [qaList, setQaList] = useState<QADocument[]>([]);
  const [displayQaList, setDisplayQaList] = useState<QADocument[]>([]);
  const [expendList, setExpendList] = useState<number>(-1);
  const [highlightIdxList, setHighlightIdxList] = useState<HighlightIdx[]>([]);
  const [sourceData, setSourceData] = useState<string>('');
  const [total, setTotal] = useState<number>(0);

  // 获取QA列表
  const getQAs = useCallback(
    (taskId: string) => {
      const qaParams = {
        taskId,
        page: pagination.page - 1,
        pageSize: pagination.size,
        fileIdList: fileList,
        keyword: filterVal,
        isReview:
          reviewFilterType === undefined ? (approvedItems === true ? true : undefined) : false,
        allocateUserId: allocatedUser,
        score: scoreFilter,
        tags: Array.from(selectedTags)?.join(','),
      };

      getQAList(qaParams).then((res) => {
        if (res.data?.code === 200) {
          setQaList(res.data.data.qaDocumentPage);
          setDisplayQaList(res.data.data.qaDocumentPage);
          console.log('scoreFilter', scoreFilter);
          setTotal(res.data.data.total);
        }
      });
    },
    [
      pagination.page,
      pagination.size,
      fileList,
      filterVal,
      reviewFilterType,
      approvedItems,
      allocatedUser,
      scoreFilter,
      selectedTags,
    ]
  );

  // 删除QA
  const handleDeleteQA = useCallback(
    async (
      checkedQASet: QAInfoType[],
      excludeQASet: QAInfoType[],
      checkedFileSet: Set<string>,
      globalSelectedQADetails: Map<string, { fileId: string; id: string }>
    ) => {
      // 构建包含全局选中QA的删除参数
      const globalSelectedQAMap = new Map<string, string[]>();

      Array.from(globalSelectedQADetails.values()).forEach((qaDetail) => {
        if (!globalSelectedQAMap.has(qaDetail.fileId)) {
          globalSelectedQAMap.set(qaDetail.fileId, []);
        }
        globalSelectedQAMap.get(qaDetail.fileId)!.push(qaDetail.id);
      });

      const globalSelectedQAList: QAInfoType[] = Array.from(globalSelectedQAMap.entries()).map(
        ([fileId, ids]) => ({
          fileId,
          ids,
        })
      );

      const params: QaDeleteInfo = {
        excludeInfoList: excludeQASet,
        fileIdList: Array.from(checkedFileSet),
        qaDeleteInfoList: [...checkedQASet, ...globalSelectedQAList],
        taskId: taskId,
      };

      try {
        const res = await deleteQA(params);
        if (res.data.code === 200) {
          return true;
        } else {
          message.error(res.data.message);
          return false;
        }
      } catch (error) {
        message.error('删除失败');
        return false;
      }
    },
    [taskId]
  );

  // 导出QA
  const exportQAFun = useCallback(
    async (params: QaExportParams, exportType?: string, taskName?: string) => {
      try {
        const config = exportType === 'WORD' ? { responseType: 'arraybuffer' as const } : {};
        const res = await exportQA(params, config);

        if (!res.data) return;

        const [mimeType, ext] =
          exportType === 'WORD'
            ? ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'docx']
            : ['application/json', 'json'];

        const blob = new Blob([exportType === 'WORD' ? res.data : JSON.stringify(res.data)], {
          type: mimeType,
        });

        saveAs(blob, `${taskName ?? 'QA数据'}.${ext}`);
      } catch (error) {
        console.error(`${exportType}导出失败:`, error);
      }
    },
    []
  );

  // 导出全部QA
  const exportAllQA = useCallback(
    async (exportType?: string, taskName?: string) => {
      const params: QaExportParams = {
        taskId: taskId ?? '',
        all: true,
        excludeInfoList: [],
        fileIdList: [],
        exportFormat: exportType,
      };
      exportQAFun(params, exportType, taskName);
    },
    [taskId, exportQAFun]
  );

  // 导出选中的QA
  const exportCheckedQA = useCallback(
    async (
      exportType?: string,
      taskName?: string,
      checkedQASet: QAInfoType[] = [],
      excludeQASet: QAInfoType[] = [],
      checkedFileSet: Set<string> = new Set(),
      globalSelectedQAIds: Set<string> = new Set(),
      globalSelectedQADetails: Map<string, { fileId: string; id: string }> = new Map(),
      paginationTotal: number = 0
    ) => {
      // 检查是否为全选状态
      const isSelectAll =
        globalSelectedQAIds.size > 0 && globalSelectedQAIds.size === paginationTotal;

      if (isSelectAll) {
        // 全选状态
        const globalSelectedQAMap = new Map<string, string[]>();
        Array.from(globalSelectedQADetails.values()).forEach((qaDetail) => {
          if (!globalSelectedQAMap.has(qaDetail.fileId)) {
            globalSelectedQAMap.set(qaDetail.fileId, []);
          }
          globalSelectedQAMap.get(qaDetail.fileId)!.push(qaDetail.id);
        });

        const globalSelectedQAList: QAInfoType[] = Array.from(globalSelectedQAMap.entries()).map(
          ([fileId, ids]) => ({ fileId, ids })
        );

        const params: QaExportParams = {
          taskId: taskId ?? '',
          all: true,
          qaExportInfoList: globalSelectedQAList,
          excludeInfoList: excludeQASet,
          fileIdList: checkedFileSet ? Array.from(checkedFileSet) : undefined,
          exportFormat: exportType,
        };
        exportQAFun(params, exportType, taskName);
      } else if (globalSelectedQAIds.size > 0) {
        // 部分选中状态
        const globalSelectedQAMap = new Map<string, string[]>();
        Array.from(globalSelectedQADetails.values()).forEach((qaDetail) => {
          if (!globalSelectedQAMap.has(qaDetail.fileId)) {
            globalSelectedQAMap.set(qaDetail.fileId, []);
          }
          globalSelectedQAMap.get(qaDetail.fileId)!.push(qaDetail.id);
        });

        const globalSelectedQAList: QAInfoType[] = Array.from(globalSelectedQAMap.entries()).map(
          ([fileId, ids]) => ({ fileId, ids })
        );

        const params: QaExportParams = {
          taskId: taskId ?? '',
          all: false,
          qaExportInfoList: globalSelectedQAList,
          excludeInfoList: excludeQASet,
          fileIdList: checkedFileSet ? Array.from(checkedFileSet) : undefined,
          exportFormat: exportType,
        };
        exportQAFun(params, exportType, taskName);
      } else {
        // 没有选中任何QA
        const params: QaExportParams = {
          taskId: taskId ?? '',
          all: false,
          qaExportInfoList: checkedQASet,
          excludeInfoList: excludeQASet,
          fileIdList: checkedFileSet ? Array.from(checkedFileSet) : undefined,
          exportFormat: exportType,
        };
        exportQAFun(params, exportType, taskName);
      }
    },
    [taskId, exportQAFun]
  );

  return {
    qaList,
    displayQaList,
    expendList,
    highlightIdxList,
    sourceData,
    setExpendList,
    setHighlightIdxList,
    setSourceData,
    getQAs,
    total,
    handleDeleteQA,
    exportAllQA,
    exportCheckedQA,
  };
};
