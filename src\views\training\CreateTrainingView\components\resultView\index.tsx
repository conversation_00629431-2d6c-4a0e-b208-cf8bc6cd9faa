import React, { useEffect, useRef } from "react";
import { Col,  Row } from "antd";
import { TariningType } from "../../type";
import classes from "./index.module.css";
interface ResultviewProp {
  trainingData: TariningType;
}
const ResultView: React.FC<ResultviewProp> = ({ trainingData }) => {
  
  return (
    <>
      <Row>
        <Col span={6} className={classes["step-label"] + " boldText"}>
          训练结果
        </Col>

        <Col span={16}>
                <div className={classes['overview-container']}>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>所选训练模型：</div>
                        <label>{trainingData.dataset}</label>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>新模型名称：</div>
                        <label>{trainingData.modelName}</label>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>新模型介绍：</div>
                        <label>{trainingData.modelIntro}</label>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>数据配置：</div>
                        <div className={classes['config-container']}>
                            {
                                trainingData.tasks?.map(task => {
                                    return <label key={task.taskId}>{task.taskName}（{task.qaCount}）</label>
                                })
                            }
                            <label>测试集比例：{trainingData.testSet}</label>
                        </div>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>训练配置：</div>
                        <div className={classes['config-container']}>
                            <label>训练策略：{trainingData.srategy}</label>
                            <label>迭代次数：{trainingData.iterationLevel}</label>
                            <label>批次大小：{trainingData.batchValue}</label>
                            <label>学习率   ： {trainingData.learningValue}</label>
                        </div>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>训练框架：</div>
                        <label>{trainingData.framework}</label>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>服务器选择：</div>
                        <label>{trainingData.serverSelection}</label>
                    </div>
                    <div className={classes['overview-item']}>
                        <div className={classes['overview-title']}>算力配置：</div>
                        <label>{trainingData.arithmeticConfiguration}</label>
                    </div>
                </div>
            </Col>
      </Row>
    </>
  );
};
export default ResultView;
