import CustomReactMarkdown from './CustomReactMarkdown';
import { DatasetFileEnum, ParseDatasetType } from '../../types';
// @ts-ignore
import FileViewer from 'react-file-viewer';
interface PreviewFileProp {
  fileType?: number;
  fileDoc?: string;
  fileParseData?: ParseDatasetType;
  activeBtn: number;
}

const PreviewFile: React.FC<PreviewFileProp> = ({
  fileType,
  fileDoc,
  fileParseData,
  activeBtn,
}) => {
  if (fileType === DatasetFileEnum.Txt) {
    return <iframe srcDoc={fileDoc} width={'100%'} height={'550px'} style={{ border: 'unset' }} />;
  } else if (fileType === DatasetFileEnum.Pdf) {
    if (activeBtn === 1) {
      return (
        <FileViewer
          fileType={'pdf'}
          key="pdf-preview"
          filePath={fileParseData?.srcFilePath!}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      );
    } else {
      return <CustomReactMarkdown>{fileDoc || ''}</CustomReactMarkdown>;
    }
  } else if (fileType === DatasetFileEnum.Doc) {
    if (activeBtn === 1) {
      return (
        <FileViewer
          fileType={'docx'}
          key="docx-preview"
          filePath={fileParseData?.srcFilePath!}
          // onError={this.onError}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      );
    } else {
      return <CustomReactMarkdown>{fileDoc || ''}</CustomReactMarkdown>;
    }
  } else if (fileType === DatasetFileEnum.Csv) {
    if (activeBtn === 1) {
      return (
        <FileViewer
          fileType={'csv'}
          key="csv-preview"
          filePath={fileParseData?.srcFilePath!}
          // onError={this.onError}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      );
    } else {
      return <CustomReactMarkdown>{fileDoc || ''}</CustomReactMarkdown>;
    }
  } else if (fileType === DatasetFileEnum.Xlsx) {
    if (activeBtn === 1) {
      return (
        <FileViewer
          fileType={'xlsx'}
          key="xlsx-preview"
          filePath={fileParseData?.srcFilePath!}
          // onError={this.onError}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      );
    } else {
      return <CustomReactMarkdown>{fileDoc || ''}</CustomReactMarkdown>;
    }
  } else if (fileType === DatasetFileEnum.Json) {
    if (activeBtn === 1) {
      return (
        <div style={{ whiteSpace: 'pre-wrap' }}>
          <code>{typeof fileDoc === 'string' ? fileDoc : JSON.stringify(fileDoc)}</code>
        </div>
      );
    } else {
      return (
        <div style={{ whiteSpace: 'pre-wrap' }}>
          <code>{JSON.stringify(fileDoc, null, 2)}</code>
        </div>
      );
    }
  } else if (fileType === DatasetFileEnum.Markdown) {
    if (activeBtn === 1) {
      return <div>{fileDoc}</div>;
    } else {
      return <CustomReactMarkdown>{fileDoc || ''}</CustomReactMarkdown>;
    }
  } else if (fileType === DatasetFileEnum.Image) {
    if (activeBtn === 1) {
      return <img src={fileParseData?.srcFilePath!} alt="图片预览" />;
    } else {
      return <CustomReactMarkdown>{fileDoc || ''}</CustomReactMarkdown>;
    }
  } else {
    return null;
  }
};

export default PreviewFile;
