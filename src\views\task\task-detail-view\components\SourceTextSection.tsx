import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import { Scrollbar } from 'react-scrollbars-custom';
import { HighlightIdx } from '../../../../types';
import HighlightedText from '../../../../components/HighlightedText';

interface SourceTextSectionProps {
  sourceData: string;
  fileName?: string;
  highlightIdxList: HighlightIdx[];
  treeData: any[];
}

const SourceTextSection: React.FC<SourceTextSectionProps> = ({
  sourceData,
  fileName,
  highlightIdxList,
  treeData,
}) => {
  // 检查是否显示原文对照区域
  const shouldShowSourceText = !(
    treeData.length > 0 &&
    ((treeData[0] as any)?.name.endsWith('.csv') || (treeData[0] as any)?.name.endsWith('.json'))
  );

  if (!shouldShowSourceText) {
    return null;
  }

  return (
    <>
      {/* 原文对照内容区域 */}
      <Col span={10} style={{ borderRight: '1px solid #efefef' }}>
        <div style={{ textAlign: 'start' }}>
          <div
            className="taskDetailLf1"
            style={{
              display: 'flex',
              flexDirection: 'column',
              height: '580px',
            }}
          >
            <label style={{ marginBottom: '1rem' }}>{fileName}</label>
            <div style={{ flex: 1 }}>
              <Scrollbar>
                <div
                  style={{
                    height: 'max-content',
                    textAlign: 'start',
                    marginTop: '16px',
                    lineHeight: '26px',
                    color: '#6D7279',
                    whiteSpace: 'pre-wrap',
                    overflowWrap: 'break-word',
                  }}
                >
                  <HighlightedText text={sourceData} highlights={highlightIdxList} />
                </div>
              </Scrollbar>
            </div>
          </div>
        </div>
      </Col>
    </>
  );
};

export default SourceTextSection;
