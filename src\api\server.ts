import { AxiosResponse } from "axios";
import { CustomResponseType, QADocument } from "../types";
import { ServerConfiguration } from "../views/task/finetuntask/server-management/data";
import request from "../utils/request";
interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
}

export async function createServer(
  ServerConfiguration: ServerConfiguration
): Promise<BaseResponse<boolean>> {
  try {
    const url = `/server`;
    const res = await request.post(url, ServerConfiguration);
    if (res.data.code == 200) {
      return { code: 200, message: "更新成功", data: true };
    } else if (res.data.code == 50001) {
      return { code: 400, message: res.data.message, data: false };
    } else {
      return { code: 400, message: "创建失败", data: false };
    }
  } catch (e) {
    return { code: 400, message: "未知错误", data: false };
  }
}
export async function updateServer(
  ServerConfiguration: ServerConfiguration
): Promise<BaseResponse<boolean>> {
  try {
    const url = `/server`;
    const res = await request.put(url, ServerConfiguration);
    if (res.data.code == 200) {
      return { code: 200, message: "更新成功", data: true };
    } else if (res.data.code == 50001) {
      return { code: 400, message: res.data.message, data: false };
    } else {
      return { code: 400, message: "更新失败", data: false };
    }
  } catch (e) {
    return { code: 400, message: "未知错误", data: false };
  }
}

export async function queryServer(): Promise<ServerConfiguration[]> {
  try {
    const url = `/server/all`;
    const res = await request.get(url);
    if (res.data.code == 200) {
      return res.data.data.servers;
    } else {
      return [];
    }
  } catch (e) {
    return [];
  }
}
export async function queryServerById(
  id: number
): Promise<ServerConfiguration | undefined> {
  try {
    const url = `/server/${id}`;
    const res = await request.get(url);
    if (res.data.code == 200) {
      return res.data.data;
    } else {
      return undefined;
    }
  } catch (e) {
    return undefined;
  }
}

export async function deleteServer(ServerId: number): Promise<boolean> {
  try {
    const url = `/server/${ServerId}`;
    const res = await request.delete(url);
    if (res.data.code == 200) {
      return true;
    } else {
      return false;
    }
  } catch (e) {
    return false;
  }
}
