
export function randomId(currentTime: Date) {
    // const currentYear = new Date().getFullYear().toString();
    // const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
    // const day = (new Date().getDate()).toString().padStart(2, '0');
    // const hours = (new Date().getHours()).toString().padStart(2, '0');
    // const minutes = (new Date().getMinutes()).toString().padStart(2, '0');
    // return currentYear + currentMonth + day + hours + minutes;
    const year = currentTime.getFullYear();
    const month = String(currentTime.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
    const day = String(currentTime.getDate()).padStart(2, '0');
    const hours = String(currentTime.getHours()).padStart(2, '0');
    const minutes = String(currentTime.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}`;
    
  }

  