.child78 {
  position: absolute;
  top: 0;
  left: 55.13rem;
  background-color: var(--color-white);
  width: 64.88rem;
  height: 67.5rem;
}

.b61,
.div641,
.div642 {
  position: absolute;
  left: 74.44rem;
}

.b61 {
  top: 16.5rem;
  font-size: 2.5rem;
  color: var(--color-black);
}

.div641,
.div642 {
  top: 23.19rem;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--color-lightslategray-100);
  text-align: center;
  cursor: pointer;
}

.div642 {
  left: 79.69rem;
  color: var(--color-lightslategray-100);
}

.tab-active {
  top: 22.94rem;
  font-size: var(--font-size-xl);
  color: var(--color-mediumaquamarine-300);
}

.frame-child292 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-5xs);
  width: 26.25rem;
  height: 3.5rem;
  background: #FBFBFC;
}

.div643 {
  position: absolute;
  top: 1.25rem;
  left: 1rem;
}

.frame-child293,
.vector-group {
  position: absolute;
  width: 26.25rem;
  height: 3.5rem;
}

.vector-group {
  top: 26.81rem;
  left: 74.44rem;
}

.frame-child293 {
  top: 0;
  left: 0;
  border-radius: var(--br-40xl);
}

.b62 {
  position: absolute;
  top: 1.31rem;
  left: 10.81rem;
}

.vector-container {
  position: absolute;
  top: 38.06rem;
  left: 74.44rem;
  width: 26.25rem !important;
  height: 3.5rem;
  color: var(--color-white);
}

.span5 {
  color: var(--color-lightslategray-100);
}

.span6 {
  color: var(--color-mediumaquamarine-200);
}

.div644 {
  position: absolute;
  top: 42.56rem;
  left: 74.4rem;
  color: var(--color-gray-300);
}

.frame-child294,
.frame-child295 {
  position: absolute;
  top: 0;
  height: 3.5rem;
}

.frame-child294 {
  left: 0;
  border-radius: var(--br-5xs);
  width: 17.38rem;
  background: #FBFBFC;
}

.frame-child295 {
  left: 18.13rem;
  border-radius: var(--br-40xl);
  width: 8.13rem !important;
}

.div646 {
  position: absolute;
  /* top: 1.25rem;
  left: 20rem; */
  font-weight: 500;
  color: var(--color-mediumaquamarine-300);
}

.child79,
.vector-parent1 {
  position: absolute;
  top: 31.56rem;
  left: 74.44rem;
  width: 26.25rem;
  height: 3.5rem;
}

.child79 {
  top: 42.56rem;
  border-radius: 50%;
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-200);
  box-sizing: border-box;
  width: 1rem;
  height: 1rem;
}

.group-child19 {
  background-image: url(../assets/img/group-266.png);
  -webkit-backdrop-filter: blur(18px);
  backdrop-filter: blur(18px);
  border-radius: 24px;
  width: calc((100vh - 40px) * 0.81);
  height: calc(100vh - 40px);
  background-size: cover;
  background-position: center;
}

.rectangle-15 {
  background: conic-gradient(from -10.57deg at 24.94% 91.25%, #9198CE -29.28deg, #1F2237 1.98deg, #1A1B28 9.34deg, #000000 53.66deg, #111111 86.4deg, #108570 160.06deg, #FFFFFF 236.25deg, #0F9C82 279.38deg, #903EF9 296.33deg, #9198CE 330.72deg, #1F2237 361.98deg);
}


.logo-child147 {
  position: absolute;
  top: 0.22rem;
  left: 32.16rem;
  border-radius: var(--br-44xl);
  border: 1px solid var(--color-darkslategray-300);
  box-sizing: border-box;
  width: 6.63rem;
  height: 44.55rem;
  transform: rotate(45deg);
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child148 {
  top: 0.33rem;
  left: 20.17rem;
  height: 19.89rem;
  transform: rotate(45deg);
}

.logo-child148,
.logo-child149,
.logo-child150,
.logo-child151 {
  position: absolute;
  border-radius: var(--br-44xl);
  border: 1px solid var(--color-darkslategray-300);
  box-sizing: border-box;
  width: 6.63rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child149 {
  top: 0;
  left: 8.61rem;
  height: 12.18rem;
  transform: rotate(45deg);
}

.logo-child150,
.logo-child151 {
  top: 36.3rem;
  left: 17.33rem;
  height: 19.89rem;
  transform: rotate(-135deg);
}

.logo-child151 {
  top: 36.63rem;
  left: 28.89rem;
  height: 12.18rem;
}

.group-child20,
.logo30 {
  position: absolute;
  top: 32.69rem;
  left: 21.13rem;
  width: 37.5rem;
  height: 36.63rem;
}

.group-child20 {
  position: absolute;
  width: 428px;
  height: 428px;
  left: 9px;
  top: 753px;
  /* background: linear-gradient(221.47deg, #111111 26.05%, #222F52 42.26%, #2AC0A5 61.36%, #FFFFFF 69.79%); */
  background: linear-gradient(211.54deg, #111111 24.04%, #222F52 40.1%, #2AC0A5 59.04%, #FFFFFF 67.39%);
  filter: blur(62px);
}

.everreach {
  margin: 0;
  text-align: center;
}

.everreach-container {
  position: fixed;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  /* left: 12.8125rem; */
  white-space: pre-wrap;
  color: rgba(255, 255, 255, 0.40);
  line-height: 20px;
  width: 450px;
  font-weight: 400;
  font-style: normal;
}

.div647,
.pollux {
  position: absolute;
}

.pollux {
  top: 9rem;
  left: 1.94rem;
  font-weight: 500;
}

.div647 {
  top: 11.69rem;
  left: 0;
  font-size: var(--font-size-base);
  color: #FFF;
  text-align: center;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

.logo-child152 {
  position: absolute;
  top: 0.03rem;
  left: 4.83rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-mediumspringgreen);
  width: 1rem;
  height: 6.69rem;
  transform: rotate(45deg);
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child153 {
  top: 0.05rem;
  left: 3.03rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 2.99rem;
  transform: rotate(45deg);
}

.logo-child153,
.logo-child154,
.logo-child155,
.logo-child156 {
  position: absolute;
  border-radius: var(--br-26xl);
  width: 1rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child154 {
  top: 0;
  left: 1.29rem;
  background-color: var(--color-teal-100);
  height: 1.83rem;
  transform: rotate(45deg);
}

.logo-child155,
.logo-child156 {
  top: 5.45rem;
  left: 2.6rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 2.99rem;
  transform: rotate(-135deg);
}

.logo-child156 {
  top: 5.5rem;
  left: 4.34rem;
  background-color: var(--color-teal-100);
  height: 1.83rem;
}

.logo31 {
  position: absolute;
  top: 0;
  left: 4.19rem;
  width: 5.63rem;
  height: 5.5rem;
}

.pollux-parent {
  /* position: absolute;
  top: 23.69rem;
  left: 19.31rem; */
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  width: 14rem;
  height: 12.88rem;
  font-size: var(--font-size-5xl);
}

.logo-child157,
.logo-child158 {
  top: 0.01rem;
  transform: rotate(45deg);
}

.logo-child157 {
  position: absolute;
  left: 1.1rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-mediumspringgreen);
  width: 0.23rem;
  height: 1.52rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child158 {
  left: 0.69rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 0.68rem;
}

.logo-child158,
.logo-child159,
.logo-child160,
.logo-child161 {
  position: absolute;
  border-radius: var(--br-26xl);
  width: 0.23rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}

.logo-child159 {
  top: 0;
  left: 0.29rem;
  background-color: var(--color-teal-100);
  height: 0.42rem;
  transform: rotate(45deg);
}

.logo-child160,
.logo-child161 {
  top: 1.24rem;
  left: 0.59rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 0.68rem;
  transform: rotate(-135deg);
}

.logo-child161 {
  top: 1.25rem;
  left: 0.99rem;
  background-color: var(--color-teal-100);
  height: 0.42rem;
}

.logo32 {
  position: relative;
  width: 1.28rem;
  height: 1.25rem;
}

.everreachai-pollux16 {
  font-style: normal;
  font-weight: 800;
  font-size: 17.8628px;
  line-height: 22px;
  color: #FFFFFF;
}

.logo-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 7px;
  position: absolute;
  height: 20px;
  /* left: 586px; */
  left: 2.7681rem;
  top: 44px;
  gap: var(--gap-xs);
  text-align: left;
  font-size: var(--font-size-base);
}

.rectangle-parent57 {
  /* position: absolute;
    top: 1.25rem;
    left: 1.25rem;
    width: 52.63rem;
    height: 65rem;
    text-align: center; */
  overflow: hidden;
  font-size: var(--font-size-xs);
  color: var(--color-white);
}

.child80 {
  position: absolute;
  top: 24.69rem;
  left: 79.69rem;
  background-color: var(--color-mediumaquamarine-300);
  width: 5rem;
  height: 0.13rem;
}

.div640 {
  position: relative;
  background-color: var(--color-white);
  width: 100%;
  height: 67.5rem;
  overflow: hidden;
  text-align: left;
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}