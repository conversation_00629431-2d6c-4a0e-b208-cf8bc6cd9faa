import { useState } from 'react';
import { Button, Input, message } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { renameTask } from '@/api/task';
const InputComponent = (props: any) => {
  const { taskInfo, renameList, setRenameList, getTasksList } = props;
  const [renameInput, setRenameInput] = useState<string>(taskInfo.taskName);

  return (
    <>
      <Input
        placeholder="请输入任务名称"
        value={renameInput}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          setRenameInput(e.target.value);
        }}
      ></Input>
      <Button
        icon={<CheckOutlined />}
        onClick={() => {
          const newName = renameInput.trim();
          if (newName && newName.length > 0) {
            const data = taskInfo;
            data.taskName = newName;
            const updateSet = renameList;
            updateSet.delete(data.taskId);
            setRenameList(updateSet);
            setRenameInput('');
            renameTask(data.taskId, newName).then(() => {
              getTasksList();
            });
          } else {
            message.error('任务名称不合法');
          }
        }}
      />
      <Button
        icon={<CloseOutlined />}
        onClick={() => {
          const updateSet = renameList;
          updateSet.delete(taskInfo.taskId);
          setRenameList(updateSet);
          setRenameInput('');
        }}
      />
    </>
  );
};

export default InputComponent;
