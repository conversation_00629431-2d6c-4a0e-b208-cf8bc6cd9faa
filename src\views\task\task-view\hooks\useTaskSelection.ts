import { useState, useCallback, useMemo } from 'react';
import { TaskType } from '../../../../types';

export interface UseTaskSelectionReturn {
  selectedRowKeys: Set<string>;
  selectedRows: TaskType[];
  allSelectedRows: TaskType[]; // 新增：所有选中的任务
  handleSelectionChange: (selectedRowKeys: React.Key[], selectedRows: TaskType[]) => void;
  clearSelection: () => void;
  removeFromSelection: (taskIds: string[]) => void;
  isSelected: (taskId: string) => boolean;
}

export const useTaskSelection = (taskData: TaskType[]): UseTaskSelectionReturn => {
  // 使用Set存储选中的任务ID，确保跨页面状态保持
  const [selectedRowKeys, setSelectedRowKeys] = useState<Set<string>>(new Set());

  // 存储所有选中的任务
  const [allSelectedRows, setAllSelectedRows] = useState<TaskType[]>([]);

  // 计算当前页选中的行数据
  const selectedRows = useMemo(() => {
    return taskData.filter((task) => selectedRowKeys.has(task.taskId));
  }, [selectedRowKeys, taskData]);

  const handleSelectionChange = useCallback(
    (newSelectedRowKeys: React.Key[], newSelectedRows: TaskType[]) => {
      const stringKeys = newSelectedRowKeys.map((key) => String(key));

      // 创建新的Set来存储选中的键
      const newSelectedSet = new Set<string>();

      // 保留之前选中但不在当前页面的项目
      selectedRowKeys.forEach((key) => {
        const isInCurrentPage = taskData.some((task) => task.taskId === key);
        if (!isInCurrentPage) {
          newSelectedSet.add(key);
        }
      });

      // 添加当前页面新选中的项目
      stringKeys.forEach((key) => {
        newSelectedSet.add(key);
      });

      // 更新所有选中的行
      setAllSelectedRows((prev) => {
        // 过滤掉当前页中未被选中的任务
        const filteredPrev = prev.filter(
          (task) =>
            !taskData.some((t) => t.taskId === task.taskId) || newSelectedSet.has(task.taskId)
        );

        // 添加当前页中新选中的任务
        const newTasks = newSelectedRows.filter(
          (task) => !prev.some((t) => t.taskId === task.taskId)
        );

        return [...filteredPrev, ...newTasks];
      });

      setSelectedRowKeys(newSelectedSet);
    },
    [selectedRowKeys, taskData]
  );

  const clearSelection = useCallback(() => {
    setSelectedRowKeys(new Set());
    setAllSelectedRows([]); // 清空所有选中的行
  }, []);

  const removeFromSelection = useCallback((taskIds: string[]) => {
    setSelectedRowKeys((prev) => {
      const newSet = new Set(prev);
      taskIds.forEach((id) => newSet.delete(id));
      return newSet;
    });

    setAllSelectedRows((prev) => prev.filter((task) => !taskIds.includes(task.taskId)));
  }, []);

  const isSelected = useCallback(
    (taskId: string) => {
      return selectedRowKeys.has(taskId);
    },
    [selectedRowKeys]
  );

  return {
    selectedRowKeys,
    selectedRows,
    allSelectedRows, // 返回所有选中的任务
    handleSelectionChange,
    clearSelection,
    removeFromSelection,
    isSelected,
  };
};
