import { Table, Empty, Button, Space } from 'antd';
import React from 'react';
import { ColumnsType } from 'antd/es/table';
import { DataSetType } from '../../types';
import TagList from '../TagList';
import RelatedTasksDropdown from '../RelatedTasksDropdown';
import DatasetRenameInput from './DatasetRenameInput';
import emptyLogo from '@/assets/img/empty-logo.svg';

export interface DatasetTableContentProps {
  columns: ColumnsType<DataSetType>;
  datasets: DataSetType[];
  rowSelection: any;
  loading: boolean;
  ShowActionColumn: boolean;
  Extra: React.ReactNode;
  apiFlg: boolean;
  tableIndex: number;
  renameRows: Set<string>;
  setRenameRows: React.Dispatch<React.SetStateAction<Set<string>>>;
  setDatasets: React.Dispatch<React.SetStateAction<DataSetType[]>>;
  handleRefresh: () => void;
  onSelectRows: (selectedRows: DataSetType[]) => void;
  previewModal: boolean;
  setPreviewModal: React.Dispatch<React.SetStateAction<boolean>>;
  selectDataSet: DataSetType | null;
  setSelectDataSet: React.Dispatch<React.SetStateAction<DataSetType | null>>;
  [key: string]: any;
}

const DatasetTableContent: React.FC<DatasetTableContentProps> = ({
  columns,
  datasets,
  rowSelection,
  loading,
  ShowActionColumn,
  Extra,
  apiFlg,
  tableIndex,
  renameRows,
  setRenameRows,
  setDatasets,
  handleRefresh,
  onSelectRows,
  previewModal,
  setPreviewModal,
  selectDataSet,
  setSelectDataSet,
  ...rest
}) => (
  <Table
    style={{ flex: 1 }}
    loading={apiFlg}
    locale={{
      emptyText: (
        <Empty
          image={emptyLogo}
          description={
            <span className="dataset-table-empty-label">
              {apiFlg ? '请求中' : '空空如也，去上传本地文件吧~'}
            </span>
          }
        />
      ),
    }}
    rowKey="id"
    rowSelection={{
      type: 'checkbox',
      ...rowSelection,
    }}
    size="small"
    className="dataset-table"
    columns={columns}
    dataSource={datasets}
    tableLayout={'fixed'}
    pagination={false}
    {...rest}
  />
);

export default DatasetTableContent;
