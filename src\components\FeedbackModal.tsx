import { Badge, Button, Modal, Upload, UploadFile, UploadProps, message } from "antd";
import TextArea from "antd/es/input/TextArea";
import <PERSON>agger from "antd/es/upload/Dragger";
import { useRef, useState } from "react";
import { blobToFile, generateRandomFileName } from "../utils/utils";
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext, PointerSensor, useSensor } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import feedbackIcon from "../assets/img/feedback-icon.svg";
import uploadComputer from "../assets/img/uploadcomputer.svg";

interface FeedbackModalProp {
  visible: boolean;
  OnClose: () => void;
}
interface DraggableUploadListItemProps {
  originNode: React.ReactElement<any, string | React.JSXElementConstructor<any>>;
  file: UploadFile<any>;
}

const DraggableUploadListItem = ({ originNode, file }: DraggableUploadListItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: file.uid,
  });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: 'move',
    height: '100%'
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      // prevent preview event when drag end
      className={isDragging ? 'is-dragging' : ''}
      {...attributes}
      {...listeners}
    >
      {/* hide error tooltip when dragging */}
      {file.status === 'error' && isDragging ? originNode.props.children : originNode}
    </div>
  );
};


const FeedbackModal: React.FC<FeedbackModalProp> = ({ visible, OnClose }) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) =>
    setFileList(newFileList);
  const props: UploadProps = {
    name: "file",
    multiple: true,
    // action: "/api/qa_generator/upload_data",
    accept: ".jpg,.png",
    data: { file_type: "jpg,png" },
    showUploadList: false,
    fileList: fileList,
    maxCount: 9,
    // onChange(info) {
    //   setFileList(info.fileList);
    // const { status } = info.file;
    // if (status !== "uploading") {
    //   console.log(info.file, info.fileList);
    // }
    // if (status === "done") {
    //   message.success(`${info.file.name} 文件上传成功.`);
    // } else
    // if (status === "error") {
    // setUploadErrorModal(true);
    // }
    // },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
    beforeUpload(file) {
      let status = "uploading";
      const importFile: any = file;
      importFile.status = status;
      //mock data
      importFile.status = null;
      importFile.parseState = "解析成功";
      importFile.parseProcess = 1;
      importFile.dataSource = 0;
      importFile.tags = ["军工"];
      const importFile2: any = JSON.parse(JSON.stringify(file));
      importFile2.name = "test.zip";
      importFile2.status = "error";
      importFile2.parseState = "解析成功";
      importFile2.parseProcess = 1;
      importFile2.dataSource = 1;
      importFile2.tags = [];
      setFileList([...fileList, importFile, importFile2]);
      // uploadDataset({ importFile: importFile }).then((res) => {
      //   if (res.data?.code === 200) {
      //     status = "success";
      //   } else {
      //     status = "error";
      //   }
      //   fileList.forEach((file) => {
      //     if (file.uid === importFile.uid) {
      //       file.status = status as UploadFileStatus;
      //     }
      //   });
      //   setFileList([...fileList]);
      // });
      return false;
    },
  };
  const sensor = useSensor(PointerSensor, {
    activationConstraint: { distance: 10 },
  });
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setFileList((prev) => {
        const activeIndex = prev.findIndex((i) => i.uid === active.id);
        const overIndex = prev.findIndex((i) => i.uid === over?.id);
        return arrayMove(prev, activeIndex, overIndex);
      });
    }
  };
  return (
    <>
      <Modal
        centered
        title={
          <div
            style={{
              display: "inline-flex",
              justifyContent: "center",
              alignItems: "center",
              gap: "1rem",
              padding: "35px 12.4% 20px 12.4%",
            }}
          >
            <img src={feedbackIcon} />
            <div style={{ fontSize: "24px", fontWeight: 700 }} className="boldText">意见反馈</div>
          </div>
        }
        width={1840}
        styles={{ body:{padding: "0 12.4%"} }}
        open={visible}
        onOk={OnClose}
        onCancel={OnClose}
        footer={[
          <Button
            type="primary"
            onClick={OnClose}
            shape="round"
            className="primary-btn"
          >
            提交反馈
          </Button>,
        ]}
      >
        <div style={{ color: "#6D7279" }}>
          <Badge color="#0FB698" style={{ marginRight: "0.5rem" }} />
          欢迎您向产品开发组反馈宝贵意见
        </div>
        <TextArea
          placeholder="请输入"
          style={{
            resize: "none",
            border: "1px solid #D7DDE7",
            height: "208px",
            margin: "1rem 0 2rem 0",
          }}
        />
        <div style={{ display: "flex", gap: "40px" }}>
          <div style={{ width: "412px" }}>
            <div style={{ color: "#6D7279" }}>
              <Badge color="#0FB698" style={{ marginRight: "0.5rem" }} />
              您可以在此添加问题界面截屏
            </div>
            <Dragger {...props} className="createTaskDragger">
              <div>
                <img
                  className="createTaskDraggerIcon"
                  alt=""
                  src={uploadComputer}
                />
                <p>可以将图片文件拖入或</p>
                <Button
                  type="default"
                  className="default-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigator.clipboard
                      .read()
                      .then((items) => {
                        if (items) {
                          if (fileList.length < 9) {
                            for (let i = 0; i < items.length; i++) {
                              const item = items[i];
                              item.types.forEach(async (type) => {
                                if (type.indexOf("image") !== -1) {
                                  const blob = await item.getType(type);
                                  const fileName =
                                    generateRandomFileName("png");
                                  const file = blobToFile(blob, fileName);
                                  const reader = new FileReader();
                                  reader.onload = () => {
                                    const uploadFile: UploadFile = {
                                      ...file,
                                      uid: fileName.split(".")[0],
                                      url: reader.result as string,
                                    };
                                    setFileList([...fileList, uploadFile]);
                                  };
                                  reader.readAsDataURL(file);
                                }
                              });
                            }
                          } else {
                            message.error(`最多可以上传9张图片`);
                          }
                        }
                      })
                      .catch((err) => {
                        message.error(`粘贴失败:${err}`);
                      });
                  }}
                >
                  粘贴剪切板内容
                </Button>
              </div>
              <div className="createTaskDraggerInfo">
                <label className="crateTaskDraggerLabel">
                  支持的图片格式有jpg、png
                </label>
              </div>
            </Dragger>
          </div>
          <div style={{ flex: 1 }}>
            <div style={{ marginBottom: "1rem", color: "#8E98A7" }}>
              拖动可以调整图片顺序，最多可以上传9张图片
            </div>

            <DndContext sensors={[sensor]} onDragEnd={onDragEnd}>
              <SortableContext items={fileList.map((i) => i.uid)} strategy={verticalListSortingStrategy}>
                <Upload
                  listType="picture-card"
                  fileList={fileList}
                  showUploadList={{
                    showPreviewIcon: false,
                    showRemoveIcon: true,
                  }}
                  onChange={handleChange}
                  itemRender={(originNode, file) => (
                    <DraggableUploadListItem originNode={originNode} file={file} />
                  )}
                ></Upload>
              </SortableContext>
            </DndContext>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default FeedbackModal;
