import React from 'react';
import { Form, Tooltip, Slider } from 'antd';
import { iteratInfo, learnInfo, batchInfo, trainingSet } from '@/utils/conts';
import { createSliderMarks } from './SliderMarks';
import { ModelFailData } from '../../type';
import { changeLearnValueMap } from './utils';
import group151 from '@/assets/img/group-151.svg';
import classes from './index.module.css';

interface ParameterConfigProps {
  form: any;
  modelFailProp?: ModelFailData;
  trainingSettings: {
    iterationLevel: number;
    learningValue: number;
    batchValue: number;
  };
  isAutoConfig: boolean;
  onTrainingSettingsChange: (settings: any) => void;
  onConfigModeChange?: () => void;
}

interface CreateTaskFormType {
  taskName: string;
  fileList: string[];
  iteration: number;
  learningrate: number;
  batchprocessing: number;
}

const ParameterConfig: React.FC<ParameterConfigProps> = ({
  form,
  modelFailProp,
  trainingSettings,
  isAutoConfig,
  onTrainingSettingsChange,
  onConfigModeChange,
}) => {
  const iterationroundsMarks = createSliderMarks({ infoArray: iteratInfo });
  const learningrateMarks = createSliderMarks({ infoArray: learnInfo });
  const batchprocessingMarks = createSliderMarks({ infoArray: batchInfo });

  const handleSliderChange = (field: string, value: number) => {
    onTrainingSettingsChange({
      ...trainingSettings,
      [field]: value,
    });

    // 如果是自动配置模式下的滑块变化，切换到手动配置
    if (isAutoConfig && onConfigModeChange) {
      onConfigModeChange();
    }
  };

  const sliderStyle = {
    railStyle: {
      height: '6px',
      background: '#F1F6F9',
      borderTop: '1px solid #E1EAEF',
      borderBottom: '1px solid #E1EAEF',
    },
    trackStyle: {
      height: '6px',
      background: '#0FB698',
      borderTop: '1px solid #0CA287',
      borderBottom: '1px solid #0CA287',
    },
    handleStyle: {},
    style: {
      width: '100%',
      display: 'inline-flex',
      margin: 'unset',
    },
  };

  return (
    <Form
      form={form}
      initialValues={{
        iteration: modelFailProp?.properties.trainConfig.interationNumber
          ? (modelFailProp.properties.trainConfig.interationNumber - 1) * 25
          : 0,
        learningrate: modelFailProp?.properties.trainConfig.learnRate
          ? changeLearnValueMap(modelFailProp.properties.trainConfig.learnRate)
          : 0,
        batchprocessing: modelFailProp?.properties.trainConfig.batchSize
          ? (modelFailProp.properties.trainConfig.batchSize / 2) * 25
          : 0,
      }}
    >
      {/* 迭代轮次 */}
      <div className={classes['disposition']}>
        <div className={classes['dispositionlabel']}>
          <label style={{ color: '#000000', lineHeight: '19px' }}>迭代轮次</label>
          <Tooltip title="模型在学习过程中完整遍历训练数据集的次数，每次迭代都涉及模型尝试学习并改进其预测能力">
            <img className="frame-child179" src={group151} />
          </Tooltip>
        </div>
        <Form.Item<CreateTaskFormType> name="iteration" noStyle>
          <Slider
            className="create-tasks-slider"
            dots
            tooltip={{
              formatter: (val) => {
                const q = val ? val : 0;
                return trainingSet[q / 25];
              },
            }}
            step={25}
            marks={iterationroundsMarks}
            value={trainingSettings.iterationLevel}
            onChange={(val) => handleSliderChange('iterationLevel', val)}
            {...sliderStyle}
          />
        </Form.Item>
      </div>

      {/* 学习率 */}
      <div className={classes['disposition']}>
        <div className={classes['dispositionlabel']}>
          <label style={{ color: '#000000', lineHeight: '19px' }}>学习率</label>
          <Tooltip title="在训练过程中调整模型权重的步长大小">
            <img className="frame-child179" src={group151} />
          </Tooltip>
        </div>
        <Form.Item<CreateTaskFormType> name="learningrate" noStyle>
          <Slider
            className="create-tasks-slider"
            dots
            tooltip={{
              formatter: (val) => {
                const q = val ? val : 0;
                return trainingSet[q / 25];
              },
            }}
            step={25}
            marks={learningrateMarks}
            value={trainingSettings.learningValue}
            onChange={(val) => handleSliderChange('learningValue', val)}
            {...sliderStyle}
          />
        </Form.Item>
      </div>

      {/* Top-p采样 */}
      <div className={classes['disposition']}>
        <div className={classes['dispositionlabel']}>
          <label style={{ color: '#000000', lineHeight: '19px' }}>Top-p采样</label>
          <Tooltip title="通过动态调整候选词池的大小，影响文本的创新性和多样性。">
            <img className="frame-child179" src={group151} />
          </Tooltip>
        </div>
        <Form.Item<CreateTaskFormType> name="batchprocessing" noStyle>
          <Slider
            className="create-tasks-slider"
            dots
            tooltip={{
              formatter: (val) => {
                const q = val ? val : 0;
                return trainingSet[q / 25];
              },
            }}
            step={25}
            marks={batchprocessingMarks}
            value={trainingSettings.batchValue}
            onChange={(val) => handleSliderChange('batchValue', val)}
            {...sliderStyle}
          />
        </Form.Item>
      </div>
    </Form>
  );
};

export default ParameterConfig;
