import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
} from "react";
import { ReviewConfigBtnType } from "./type";
import {
  Button,
  ColorPicker,
  Divider,
  Input,
  InputRef,
  Space,
  message,
  Tooltip
} from "antd";
import Icon, {
  EllipsisOutlined,
  PlusCircleFilled,
  CloseCircleFilled,
  DownCircleOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import classes from "./index.module.css";
import { Color } from "antd/es/color-picker";

export const ChecKSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M10.823 4.80804L10.717 4.70217L10.611 4.80807L7.00879 8.40733L5.38479 6.78418L5.27878 6.67822L5.17274 6.78416L3.9361 8.0196L3.8299 8.1257L3.93608 8.23182L6.90274 11.1967L7.00876 11.3026L7.1148 11.1967L12.0597 6.25521L12.1659 6.14908L12.0597 6.04297L10.823 4.80804ZM8.00001 0.85C4.05143 0.85 0.85 4.05144 0.85 8C0.85 11.9486 4.05143 15.15 8.00001 15.15C11.9486 15.15 15.15 11.9486 15.15 8C15.15 4.05144 11.9486 0.85 8.00001 0.85ZM8.00001 13.5375C4.94172 13.5375 2.46251 11.0583 2.46251 8C2.46251 4.94173 4.94172 2.46249 8.00001 2.46249C11.0583 2.46249 13.5375 4.94173 13.5375 8C13.5375 11.0583 11.0583 13.5375 8.00001 13.5375Z"
      fill="black"
      stroke="black"
      strokeWidth="0.3"
    />
  </svg>
);

export const CheckCircleSvg = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Group 464">
      <path
        id="Vector"
        d="M6.52591 0.199625C6.58761 0.136437 6.66132 0.0862252 6.74271 0.0519429C6.8241 0.0176606 6.91152 0 6.99984 0C7.08815 0 7.17558 0.0176606 7.25697 0.0519429C7.33835 0.0862252 7.41207 0.136437 7.47376 0.199625L8.92104 1.68131C8.98365 1.74537 9.05861 1.79608 9.14138 1.83034C9.22415 1.86461 9.31301 1.88173 9.40258 1.88068L11.4738 1.85617C11.5621 1.8551 11.6498 1.87171 11.7316 1.90502C11.8134 1.93833 11.8877 1.98766 11.9502 2.05012C12.0126 2.11258 12.062 2.18689 12.0953 2.2687C12.1286 2.35051 12.1452 2.43815 12.1441 2.52647L12.1196 4.59765C12.1186 4.68722 12.1357 4.77608 12.17 4.85884C12.2042 4.94161 12.2549 5.01657 12.319 5.07918L13.8007 6.52642C13.8638 6.5881 13.9139 6.66177 13.9481 6.7431C13.9824 6.82442 14 6.91177 14 7C14 7.08823 13.9824 7.17558 13.9481 7.2569C13.9139 7.33823 13.8638 7.4119 13.8007 7.47358L12.319 8.92082C12.2549 8.98343 12.2042 9.05839 12.17 9.14116C12.1357 9.22392 12.1186 9.31278 12.1196 9.40235L12.1441 11.4735C12.1452 11.5619 12.1286 11.6495 12.0953 11.7313C12.062 11.8131 12.0126 11.8874 11.9502 11.9499C11.8877 12.0123 11.8134 12.0617 11.7316 12.095C11.6498 12.1283 11.5621 12.1449 11.4738 12.1438L9.40258 12.1193C9.31301 12.1183 9.22415 12.1354 9.14138 12.1697C9.05861 12.2039 8.98365 12.2546 8.92104 12.3187L7.47443 13.8004C7.41273 13.8636 7.33902 13.9138 7.25763 13.9481C7.17624 13.9823 7.08882 14 7.0005 14C6.91219 14 6.82476 13.9823 6.74337 13.9481C6.66198 13.9138 6.58827 13.8636 6.52658 13.8004L5.0793 12.3187C5.01669 12.2546 4.94173 12.2039 4.85896 12.1697C4.77619 12.1354 4.68733 12.1183 4.59776 12.1193L2.52653 12.1438C2.43821 12.1449 2.35056 12.1283 2.26876 12.095C2.18695 12.0617 2.11263 12.0123 2.05017 11.9499C1.98771 11.8874 1.93838 11.8131 1.90507 11.7313C1.87176 11.6495 1.85515 11.5619 1.85621 11.4735L1.88072 9.40235C1.88178 9.31278 1.86466 9.22392 1.83039 9.14116C1.79612 9.05839 1.74542 8.98343 1.68135 8.92082L0.199629 7.47424C0.13644 7.41255 0.0862273 7.33884 0.0519442 7.25745C0.017661 7.17606 0 7.08864 0 7.00033C0 6.91202 0.017661 6.8246 0.0519442 6.74321C0.0862273 6.66182 0.13644 6.58811 0.199629 6.52642L1.68135 5.07918C1.74542 5.01657 1.79612 4.94161 1.83039 4.85884C1.86466 4.77608 1.88178 4.68722 1.88072 4.59765L1.85621 2.52647C1.85515 2.43815 1.87176 2.35051 1.90507 2.2687C1.93838 2.18689 1.98771 2.11258 2.05017 2.05012C2.11263 1.98766 2.18695 1.93833 2.26876 1.90502C2.35056 1.87171 2.43821 1.8551 2.52653 1.85617L4.59776 1.88068C4.68733 1.88173 4.77619 1.86461 4.85896 1.83034C4.94173 1.79608 5.01669 1.74537 5.0793 1.68131L6.52591 0.199625Z"
        fill="black"
      />
      <path
        id="Vector_2"
        d="M9.97422 5.29907C10.0738 5.38512 10.1352 5.50717 10.1449 5.63844C10.1546 5.7697 10.1118 5.89945 10.0259 5.99918L7.1724 9.31094C7.08771 9.40937 6.96784 9.47072 6.83847 9.48186C6.7091 9.49301 6.5805 9.45305 6.48023 9.37055L4.69713 7.89814C4.64507 7.85723 4.60171 7.80632 4.56961 7.7484C4.53751 7.69049 4.51731 7.62674 4.5102 7.56091C4.50309 7.49508 4.50921 7.42849 4.52821 7.36506C4.5472 7.30162 4.57869 7.24263 4.62082 7.19154C4.66294 7.14045 4.71486 7.0983 4.77351 7.06756C4.83216 7.03683 4.89636 7.01813 4.96234 7.01256C5.02833 7.007 5.09476 7.01468 5.15773 7.03516C5.2207 7.05563 5.27894 7.08849 5.32903 7.1318L6.73723 8.29423L9.27344 5.35074C9.35948 5.25113 9.48154 5.18975 9.61281 5.18006C9.74408 5.17037 9.87383 5.21318 9.97356 5.29907H9.97422Z"
        fill="white"
      />
    </g>
  </svg>
);

export const ContrastSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M8 0.8C4.00773 0.8 0.8 4.00773 0.8 8C0.8 11.9923 4.00773 15.2 8 15.2C11.9923 15.2 15.2 11.9923 15.2 8C15.2 4.00773 11.9923 0.8 8 0.8ZM8.2 1.83959C11.498 1.94613 14.1636 4.67737 14.1636 8C14.1636 11.3226 11.498 14.0539 8.2 14.1604V1.83959Z"
      fill="black"
      stroke="black"
      strokeWidth="0.4"
    />
  </svg>
);

export const DislikeSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M10.9458 10.5C10.9758 10.6205 11.0138 10.7824 11.0561 10.9839C11.1572 11.4647 11.2831 12.1711 11.3837 13.0779C11.4961 14.0911 10.7139 15.0882 9.60722 15.199L9.59728 15.2H9.58729H9.38759C8.47978 15.2 7.92391 14.6214 7.60608 13.8787L7.60603 13.8787L7.60422 13.8742L7.00819 12.3819C6.762 11.8893 6.54272 11.4998 6.35035 11.2108C6.15965 10.9244 6.00655 10.754 5.89235 10.6737C5.82116 10.6414 5.68608 10.6091 5.49801 10.5824C5.30767 10.5554 5.08607 10.537 4.87033 10.5247C4.43933 10.5 4.04451 10.5 3.99557 10.5H1.99852C1.30288 10.5 0.8 10.0239 0.8 9.4V1.9C0.8 1.27611 1.30288 0.8 1.99852 0.8H12.0836C12.8846 0.8 13.424 1.07165 13.78 1.55664C14.1233 2.0241 14.2778 2.66681 14.3782 3.36947L15.1766 8.16717L15.1778 8.17451L15.1785 8.18192C15.229 8.73782 15.2121 9.32766 14.8929 9.77814C14.5645 10.2416 13.9612 10.5 12.9823 10.5H10.9458ZM9.59604 10.7595L9.59582 10.7587L9.59551 10.7577L9.5954 10.7574L9.5954 10.7574L9.59539 10.7573L9.19641 9.45874L9.11691 9.2H9.38759H13.182C13.489 9.2 13.6051 9.19318 13.7394 9.05868C13.7793 9.01878 13.8058 8.94109 13.8058 8.7875C13.8058 8.715 13.8002 8.63807 13.7938 8.55409L13.7932 8.54587C13.7877 8.47279 13.7817 8.39318 13.7809 8.31709L12.9856 3.53809C12.961 3.42716 12.9395 3.3195 12.9191 3.21785L12.918 3.21209C12.8968 3.10635 12.8771 3.00776 12.8559 2.91464C12.8132 2.72673 12.7677 2.57524 12.705 2.45491C12.6444 2.33852 12.5697 2.25528 12.4671 2.1983C12.3619 2.13991 12.2109 2.1 11.9837 2.1H5.19409V9.20287C5.78363 9.22053 6.17476 9.32029 6.48609 9.52248C6.78503 9.64979 7.05692 9.93783 7.32906 10.3219C7.61147 10.7204 7.91607 11.2551 8.26566 11.9053L8.27103 11.9153L8.27524 11.9258C8.47538 12.4269 8.62406 12.7991 8.7352 13.0588C8.79082 13.1888 8.83556 13.2871 8.87151 13.3576C8.88947 13.3929 8.90405 13.4189 8.9156 13.4373C8.9258 13.4536 8.93088 13.4595 8.93083 13.4595L8.96334 13.4921L8.97824 13.5368C9.02003 13.6624 9.07663 13.7487 9.14852 13.8047C9.21867 13.8594 9.32239 13.9 9.48744 13.9H9.58729C9.75898 13.9 9.87693 13.8182 9.95948 13.6988C10.0438 13.5768 10.0836 13.4236 10.0861 13.3114C9.98743 12.5246 9.86461 11.8855 9.76649 11.4433C9.71715 11.221 9.67408 11.0485 9.64349 10.9321C9.62819 10.8739 9.61602 10.8297 9.60775 10.8003L9.59836 10.7675L9.59604 10.7595ZM3.79557 2.1H2.19852V9.2H3.79557V2.1Z"
      fill="black"
      stroke="black"
      strokeWidth="0.4"
    />
  </svg>
);

export const ForkSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M12.9412 10.7702C12.5379 10.7702 12.1354 10.8835 11.8026 11.0753L9.57317 8.91778C9.92563 8.42011 10.1588 7.80758 10.1588 7.15339C10.1588 6.54229 9.96543 5.9702 9.65219 5.51062L10.9045 4.17848C11.1396 4.29235 11.4198 4.40383 11.7059 4.40383C12.6673 4.40383 13.4529 3.61542 13.4529 2.65192C13.4529 1.68841 12.6673 0.9 11.7059 0.9C10.7445 0.9 9.95882 1.68841 9.95882 2.65192C9.95882 2.97283 10.0329 3.22362 10.1798 3.46596L8.94034 4.78445C8.47625 4.39337 7.86331 4.16254 7.17647 4.16254C6.48192 4.16254 5.82662 4.39849 5.28644 4.8316L4.21004 3.82648C4.32114 3.583 4.39412 3.3046 4.39412 3.0236C4.39412 2.06009 3.60845 1.27168 2.64706 1.27168C1.68567 1.27168 0.9 2.06009 0.9 3.0236C0.9 3.9871 1.68567 4.77552 2.64706 4.77552C2.96545 4.77552 3.25107 4.70201 3.50094 4.55285L4.65575 5.59899C4.34754 6.0554 4.19412 6.5864 4.19412 7.15339C4.19412 7.88894 4.46672 8.54638 4.89956 9.05066L4.15354 9.83451C3.82777 9.64474 3.46248 9.53127 3.05882 9.53127C1.85038 9.53127 0.9 10.4849 0.9 11.6962C0.9 12.9075 1.85038 13.8611 3.05882 13.8611C4.26727 13.8611 5.21765 12.9075 5.21765 11.6962C5.21765 11.2913 5.10433 10.8845 4.87531 10.5475L5.6298 9.71871C6.08216 9.98991 6.60797 10.1442 7.13529 10.1442C7.74451 10.1442 8.3148 9.9504 8.77298 9.63646L11.0488 11.801C10.8951 12.1302 10.7824 12.5311 10.7824 12.9351C10.7824 14.1464 11.7327 15.1 12.9412 15.1C14.1496 15.1 15.1 14.1464 15.1 12.9351C15.1 11.7238 14.1496 10.7702 12.9412 10.7702ZM11.7059 1.88466C12.1033 1.88466 12.4294 2.21129 12.4294 2.61062C12.4294 3.00995 12.1033 3.33658 11.7059 3.33658C11.3084 3.33658 10.9824 3.00995 10.9824 2.61062C10.9824 2.21129 11.3084 1.88466 11.7059 1.88466ZM1.92353 3.0236C1.92353 2.62427 2.24962 2.29764 2.64706 2.29764C3.04449 2.29764 3.37059 2.62427 3.37059 3.0236C3.37059 3.42293 3.04449 3.74956 2.64706 3.74956C2.24962 3.74956 1.92353 3.42293 1.92353 3.0236ZM3.05882 12.8351C2.41433 12.8351 1.92353 12.3433 1.92353 11.6962C1.92353 11.0491 2.41433 10.5572 3.05882 10.5572C3.24392 10.5572 3.39021 10.5929 3.53667 10.6643L4.08879 11.1785C4.15783 11.3245 4.19412 11.5085 4.19412 11.6962C4.19412 12.3433 3.70332 12.8351 3.05882 12.8351ZM7.17647 9.11829C6.07904 9.11829 5.21765 8.25478 5.21765 7.15339C5.21765 6.052 6.07904 5.1885 7.17647 5.1885C8.27391 5.1885 9.13529 6.052 9.13529 7.15339C9.13529 8.25478 8.27391 9.11829 7.17647 9.11829ZM12.9412 14.074C12.2967 14.074 11.8059 13.5822 11.8059 12.9351C11.8059 12.288 12.2967 11.7962 12.9412 11.7962C13.5857 11.7962 14.0765 12.288 14.0765 12.9351C14.0765 13.5822 13.5857 14.074 12.9412 14.074Z"
      fill="black"
      stroke="black"
      strokeWidth="0.2"
    />
  </svg>
);

export const LikeSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M10.9458 5.5C10.9758 5.37952 11.0138 5.21762 11.0561 5.01614C11.1572 4.53528 11.2831 3.8289 11.3837 2.92205C11.4961 1.90892 10.7139 0.911832 9.60722 0.800995L9.59728 0.8H9.58729H9.38759C8.47978 0.8 7.92391 1.37861 7.60608 2.12131L7.60603 2.12129L7.60422 2.12582L7.00819 3.61808C6.762 4.11071 6.54272 4.50019 6.35035 4.78917C6.15965 5.07565 6.00655 5.24597 5.89235 5.32626C5.82116 5.35856 5.68608 5.39091 5.49801 5.41761C5.30767 5.44463 5.08607 5.46298 4.87033 5.47533C4.43933 5.49999 4.04451 5.5 3.99557 5.5H1.99852C1.30288 5.5 0.8 5.97611 0.8 6.6V14.1C0.8 14.7239 1.30288 15.2 1.99852 15.2H12.0836C12.8846 15.2 13.424 14.9284 13.78 14.4434C14.1233 13.9759 14.2778 13.3332 14.3782 12.6305L15.1766 7.83283L15.1778 7.82549L15.1785 7.81808C15.229 7.26218 15.2121 6.67234 14.8929 6.22186C14.5645 5.75845 13.9612 5.5 12.9823 5.5H10.9458ZM9.59604 5.24047L9.59582 5.24126L9.59551 5.24226L9.5954 5.24262L9.5954 5.24265L9.59539 5.24266L9.19641 6.54126L9.11691 6.8H9.38759H13.182C13.489 6.8 13.6051 6.80682 13.7394 6.94132C13.7793 6.98122 13.8058 7.05891 13.8058 7.2125C13.8058 7.285 13.8002 7.36193 13.7938 7.44591L13.7932 7.45413C13.7877 7.52721 13.7817 7.60682 13.7809 7.68291L12.9856 12.4619C12.961 12.5728 12.9395 12.6805 12.9191 12.7822L12.918 12.7879C12.8968 12.8936 12.8771 12.9922 12.8559 13.0854C12.8132 13.2733 12.7677 13.4248 12.705 13.5451C12.6444 13.6615 12.5697 13.7447 12.4671 13.8017C12.3619 13.8601 12.2109 13.9 11.9837 13.9H5.19409V6.79713C5.78363 6.77947 6.17476 6.67971 6.48609 6.47752C6.78503 6.35021 7.05692 6.06217 7.32906 5.67814C7.61147 5.27961 7.91607 4.74491 8.26566 4.09471L8.27103 4.08472L8.27524 4.07418C8.47538 3.57309 8.62406 3.20088 8.7352 2.94119C8.79082 2.81121 8.83556 2.71292 8.87151 2.64235C8.88947 2.6071 8.90405 2.5811 8.9156 2.56269C8.9258 2.54642 8.93088 2.54053 8.93083 2.54049L8.96334 2.50793L8.97824 2.46316C9.02003 2.33759 9.07663 2.25128 9.14852 2.19528C9.21867 2.14065 9.32239 2.1 9.48744 2.1H9.58729C9.75898 2.1 9.87693 2.18181 9.95948 2.30123C10.0438 2.42319 10.0836 2.57637 10.0861 2.68859C9.98743 3.47542 9.86461 4.11446 9.76649 4.55668C9.71715 4.77901 9.67408 4.9515 9.64349 5.06792C9.62819 5.12613 9.61602 5.17031 9.60775 5.19969L9.59836 5.23255L9.59604 5.24047ZM3.79557 13.9H2.19852V6.8H3.79557V13.9Z"
      fill="black"
      stroke="black"
      strokeWidth="0.4"
    />
  </svg>
);

export const MinusSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M0.8 7.99602C0.8 11.971 4.03073 15.2 8 15.2C11.9691 15.2 15.2 11.9711 15.2 8.00398C15.2 4.02903 11.9693 0.8 8 0.8C4.03088 0.8 0.8 4.02888 0.8 7.99602ZM2.73872 7.99602C2.73872 5.09463 5.09668 2.73785 8 2.73785C10.8953 2.73785 13.2613 5.1026 13.2613 7.99602C13.2613 10.8974 10.9033 13.2542 8 13.2542C5.09668 13.2542 2.73872 10.8974 2.73872 7.99602ZM10.2005 8.97689C10.7906 8.97689 11.3173 8.56501 11.3173 7.99602C11.3173 7.42806 10.7918 7.01514 10.2084 7.01514H5.79157C5.20142 7.01514 4.67472 7.42703 4.67472 7.99602C4.67472 8.56603 5.21059 8.97689 5.79157 8.97689H10.2005Z"
      fill="black"
      stroke="black"
      strokeWidth="0.4"
    />
  </svg>
);

export const StarSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M7.69873 1.19425L7.69876 1.19431L9.46996 4.28742L13.0047 4.99019C13.0047 4.99019 13.0047 4.9902 13.0048 4.99021L7.69873 1.19425ZM7.69873 1.19425C7.64618 1.10262 7.5687 1.02642 7.47604 0.974898L7.42744 1.0623L7.47617 0.974972C7.40909 0.937539 7.33524 0.913542 7.25885 0.9043C7.18246 0.895059 7.10496 0.900745 7.03077 0.921047C6.95658 0.94135 6.8871 0.975888 6.82633 1.02275C6.76556 1.06962 6.71467 1.12791 6.67667 1.19434L6.67666 1.19435L4.90727 4.28742L1.37168 4.99019C1.37166 4.99019 1.37163 4.9902 1.37161 4.9902C1.2698 5.0103 1.1751 5.05675 1.09727 5.12493C1.01942 5.19314 0.961234 5.28067 0.928888 5.37864C0.896539 5.47661 0.891245 5.58135 0.913578 5.68204C0.935909 5.78273 0.985017 5.87558 1.05568 5.95112L3.49626 8.56524L3.08194 12.0909C3.08194 12.0909 3.08193 12.0909 3.08193 12.0909C3.06985 12.1933 3.08567 12.297 3.12768 12.3912C3.1697 12.4855 3.23635 12.5669 3.32061 12.6271C3.40485 12.6873 3.50372 12.7243 3.60704 12.7344C3.71036 12.7444 3.81459 12.7272 3.90905 12.6845L7.18952 11.2067L10.4682 12.6845L7.69873 1.19425ZM6.74586 12.564L6.74594 12.5639L6.74234 12.5607C6.61898 12.4505 6.4576 12.3915 6.29167 12.3955C6.12574 12.3994 5.96742 12.4661 5.84961 12.5821C5.73175 12.6982 5.66347 12.8547 5.6594 13.0194C5.65533 13.1842 5.7158 13.3439 5.82784 13.4655L5.82775 13.4655L5.8312 13.4689L7.29447 14.9106C7.29452 14.9107 7.29458 14.9107 7.29464 14.9108C7.35474 14.9706 7.42616 15.018 7.50476 15.0505L7.54291 14.958L7.50476 15.0505C7.58343 15.0829 7.66779 15.0998 7.75301 15.1C7.83822 15.1002 7.92267 15.0839 8.00153 15.0518C8.08038 15.0198 8.15213 14.9727 8.2126 14.9132L8.14244 14.8419L8.2126 14.9132C8.27308 14.8536 8.32109 14.7828 8.3538 14.7047C8.38651 14.6267 8.40324 14.543 8.403 14.4584C8.40276 14.3739 8.38555 14.2903 8.3524 14.2124C8.3193 14.1347 8.27098 14.0642 8.21029 14.005C8.2102 14.0049 8.21011 14.0049 8.21003 14.0048L6.74586 12.564ZM12.3236 12.5617L12.2855 12.6542L12.3236 12.5617C12.2449 12.5292 12.1606 12.5124 12.0754 12.5122C11.9902 12.512 11.9057 12.5283 11.8268 12.5603C11.748 12.5924 11.6762 12.6395 11.6158 12.699C11.5553 12.7586 11.5073 12.8294 11.4746 12.9075C11.4419 12.9855 11.4251 13.0692 11.4254 13.1538C11.4256 13.2383 11.4428 13.3219 11.476 13.3998C11.5091 13.4776 11.5574 13.548 11.6181 13.6072C11.6182 13.6073 11.6183 13.6073 11.6183 13.6074L12.78 14.7512C12.78 14.7513 12.7801 14.7513 12.7802 14.7514C12.8403 14.8112 12.9117 14.8586 12.9903 14.891C13.0689 14.9235 13.1533 14.9403 13.2385 14.9406C13.3237 14.9408 13.4082 14.9244 13.487 14.8924C13.5659 14.8604 13.6376 14.8133 13.6981 14.7537C13.7586 14.6942 13.8066 14.6234 13.8393 14.5453C13.872 14.4672 13.8887 14.3835 13.8885 14.299C13.8883 14.2145 13.871 14.1309 13.8379 14.053C13.8048 13.9752 13.7565 13.9047 13.6958 13.8456C13.6957 13.8455 13.6956 13.8454 13.6955 13.8453L12.5339 12.7016C12.5338 12.7015 12.5337 12.7014 12.5336 12.7013C12.4735 12.6415 12.4022 12.5941 12.3236 12.5617ZM10.8819 8.56435L13.3207 5.95112L10.5094 12.5934C10.5879 12.6289 10.6746 12.6432 10.7605 12.6348C10.8465 12.6264 10.9286 12.5957 10.9985 12.5457C11.0684 12.4958 11.1235 12.4284 11.1582 12.3505C11.1929 12.2727 11.206 12.1871 11.196 12.1026L11.2953 12.091L11.2953 12.0909C11.2953 12.0909 11.2953 12.0909 11.2953 12.0909L10.8819 8.56435ZM9.86213 11.0072L7.22881 9.82031L7.18773 9.80179L7.14664 9.82031L4.51241 11.0072L4.84462 8.17646L4.85002 8.13047L4.81844 8.0966L2.86213 5.9985L5.69831 5.43578L5.74301 5.42691L5.76564 5.38736L7.1873 2.90327L8.60982 5.38739L8.63245 5.4269L8.67711 5.43578L11.5141 5.99933L9.55614 8.09656L9.52452 8.13043L9.52992 8.17646L9.86213 11.0072ZM14.9249 9.22628L14.925 9.22624L14.9226 9.22385L13.4593 7.78308C13.4592 7.78299 13.4591 7.78291 13.4591 7.78282C13.399 7.72306 13.3276 7.67565 13.249 7.64323C13.1704 7.61076 13.086 7.59394 13.0008 7.59371C12.9156 7.59347 12.8311 7.60982 12.7523 7.64186C12.6734 7.67389 12.6017 7.721 12.5412 7.78054C12.4807 7.84009 12.4327 7.91091 12.4 7.98897C12.3673 8.06703 12.3505 8.15074 12.3508 8.23527C12.351 8.3198 12.3682 8.40342 12.4014 8.48129C12.4345 8.55907 12.4828 8.62955 12.5435 8.68872C12.5436 8.68879 12.5437 8.68886 12.5438 8.68893L14.0079 10.1297L14.0079 10.1298L14.0105 10.1321C14.1335 10.245 14.2959 10.3062 14.4634 10.3032C14.6309 10.3003 14.791 10.2334 14.9099 10.1162C15.0288 9.99903 15.0971 9.84061 15.0999 9.6743C15.1027 9.50799 15.0398 9.34738 14.9249 9.22628Z"
      fill="black"
      stroke="black"
      strokeWidth="0.2"
    />
  </svg>
);

export const WavyLineSvg = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      id="Vector"
      d="M4.17611 0C5.48814 0 6.62523 0.636364 7.76231 1.80303C8.46206 2.43939 9.24927 2.75758 10.2114 2.75758C11.1736 2.75758 12.1357 2.43939 12.9229 1.69697C13.1853 1.48485 13.6227 1.48485 13.8851 1.90909C14.06 2.22727 14.06 2.75758 13.7102 3.07576C12.6605 3.92424 11.5234 4.34848 10.2114 4.34848C8.8994 4.34848 7.76231 3.92424 6.88763 2.9697C6.01295 2.01515 5.13827 1.59091 4.17611 1.59091C3.21396 1.59091 2.25181 2.12121 1.11472 3.07576C0.85232 3.39394 0.414979 3.28788 0.152574 2.86364C-0.109831 2.43939 0.0651055 2.01515 0.32751 1.80303C1.63953 0.636364 2.95156 0 4.17611 0ZM4.17611 4.87879C5.48814 4.87879 6.62523 5.51515 7.76231 6.68182C8.46206 7.31818 9.24927 7.63636 10.2114 7.63636C11.1736 7.63636 12.1357 7.31818 12.9229 6.57576C13.1853 6.36364 13.6227 6.36364 13.8851 6.78788C14.06 7.10606 14.06 7.63636 13.7102 7.95455C12.6605 8.80303 11.5234 9.22727 10.2114 9.22727C8.8994 9.22727 7.76231 8.80303 6.88763 7.84848C6.01295 6.89394 5.13827 6.4697 4.17611 6.4697C3.21396 6.4697 2.16434 7 1.11472 7.95455C0.85232 8.16667 0.414979 8.16667 0.152574 7.74242C-0.109831 7.31818 0.0651055 6.89394 0.32751 6.68182C1.63953 5.40909 2.95156 4.87879 4.17611 4.87879ZM4.17611 9.65152C5.48814 9.65152 6.62523 10.2879 7.76231 11.4545C8.46206 12.0909 9.24927 12.4091 10.2114 12.4091C11.1736 12.4091 12.1357 12.0909 12.9229 11.3485C13.1853 11.1364 13.6227 11.1364 13.8851 11.5606C14.06 11.8788 14.06 12.4091 13.7102 12.7273C12.6605 13.5758 11.5234 14 10.2114 14C8.8994 14 7.76231 13.5758 6.88763 12.6212C6.01295 11.6667 5.13827 11.2424 4.17611 11.2424C3.21396 11.2424 2.16434 11.7727 1.11472 12.7273C0.85232 13.0455 0.414979 12.9394 0.152574 12.5152C-0.109831 12.197 -0.0223627 11.6667 0.32751 11.3485C1.63953 10.2879 2.95156 9.65152 4.17611 9.65152Z"
      fill="black"
    />
  </svg>
);

export const svgMap = (svg: string) => {
  switch (svg) {
    case "LikeSvg":
      return LikeSvg;
    case "DislikeSvg":
      return DislikeSvg;
    case "ChecKSvg":
      return ChecKSvg;
    case "MinusSvg":
      return MinusSvg;
    case "ContrastSvg":
      return ContrastSvg;
    case "CheckCircleSvg":
      return CheckCircleSvg;
    case "WavyLineSvg":
      return WavyLineSvg;
    case "ForkSvg":
      return ForkSvg;
    case "StarSvg":
      return StarSvg;
    default:
      return LikeSvg;
  }
};

export const icons = [
  "LikeSvg",
  "DislikeSvg",
  "ChecKSvg",
  "MinusSvg",
  "ContrastSvg",
  "CheckCircleSvg",
  "WavyLineSvg",
  "ForkSvg",
  "StarSvg",
];

interface ReviewConfigBtnProp {
  DefaultBtnConfig: ReviewConfigBtnType[];
}

const ReviewConfigBtn = forwardRef((prop: ReviewConfigBtnProp, ref) => {
  const { DefaultBtnConfig } = prop;
  useImperativeHandle(ref, () => ({
    getButtons: () => btnConfig,
  }));

  const [btnConfig, setBtnConfig] = useState<ReviewConfigBtnType[]>([]);
  const [editBtn, setEditBtn] = useState<ReviewConfigBtnType>();
  const [editIndex, setEditIndex] = useState<number>(-1);
  const [isHovered, setIsHovered] = useState("");

  // edit btn
  const [editBtnInput, setEditBtnInput] = useState("");
  const [editColorPicker, setEditColorPicker] = useState("");
  const [editIcon, setEditIcon] = useState("");
  // 创建指向Input组件的引用，类型为InputRef
  const inputRef = useRef<InputRef>(null);
  useEffect(() => {
    setBtnConfig(DefaultBtnConfig);
  }, [DefaultBtnConfig]);

  useEffect(() => {
    setBtnConfig((pre) => {
      return pre.map((item, index) => {
        if (index === editIndex) {
          item.color = editColorPicker;
          item.value = editBtnInput;
          item.icon = editIcon;
        }
        return item;
      });
    });
  }, [editBtnInput, editColorPicker, editIcon]);
  useEffect(() => {
    if (editBtn && inputRef.current) {
      const inputElement = inputRef.current.input;
      if (inputElement) {
        inputElement.focus();
      }
    }
  }, [editBtn]);
  const onAddBtnCheck = () => {
    if (btnConfig.length === 5) {
      message.error("打分按钮不能超过5个");
      return false;
    }
    return true;
  };

  const onDeleteBtnCheck = () => {
    if (btnConfig.length === 2) {
      message.error("打分按钮不能少于2个");
      return false;
    }
    return true;
  };

  // 新增：处理按下回车键保存按钮的函数
  const handleEnterKeySave = (e: any) => {
    if (e.keyCode === 13 && editBtn) {
      saveEditedButton();
    }
  };

  // 新增：保存编辑后的按钮的函数
  const saveEditedButton = () => {
    setBtnConfig((pre) => {
      return pre.map((item, index) => {
        if (index === editIndex) {
          item.value = editBtnInput;
          item.color = editColorPicker;
          item.icon = editIcon;
          item.edit = false;
        }
        return item;
      });
    });
    setEditBtn(undefined);
    setEditIndex(-1);
    setEditBtnInput("");
    setEditColorPicker("");
    setEditIcon("");
  };
  return (
    <>
      <Space size={16} className={classes["config-btn-container"]}>
        {btnConfig?.map((btn, index) => {
          return (
            <div className={classes["config-btn-item"]} key={index}>
              {editBtn?.value !== btn.value ? (
                <>
                  <Button
                    type="primary"
                    className="review-btn-default boldText"
                    style={{
                      width: "120px",
                      background: `${isHovered === btn.value ? btn.color : ""}`
                    }}
                    onMouseEnter={() => setIsHovered(btn.value)}
                    onMouseLeave={() => setIsHovered("")}
                  >
                    <Space>
                      <Icon component={svgMap(btn.icon)} />
                      {
                        btn.value.length > 3 ? 
                        <Tooltip title={btn.value}>
                      <div style={{
                        width: "55px",
                        overflow: "hidden",
                        textOverflow: "ellipsis"
                      }}>
                        {btn.value}
                      </div>
                      
                      </Tooltip>
                      : <div>{btn.value}</div>
                      }
                    </Space>
                  </Button>

                  <Button
                    type="link"
                    className={classes["config-edit-btn"]}
                    onClick={() => {
                      setEditBtn(btn);
                      setEditIndex(index);
                      setEditBtnInput(btn.value);
                      setEditColorPicker(btn.color);
                      setEditIcon(btn.icon);
                    }}
                  >
                    <EllipsisOutlined style={{ color: "#A5B1C5" }} />
                  </Button>
                  <a
                    className={classes["config-delte-btn"]}
                    onClick={() => {
                      if (!onDeleteBtnCheck()) {
                        return;
                      }
                      setBtnConfig((pre) =>
                        pre.filter((item, i) => i !== index)
                      );
                    }}
                  >
                    <CloseCircleFilled style={{ color: "#A5B1C5" }} />
                    {/* <CloseCircleOutlined style={{ color: "#A5B1C5" }} /> */}
                  </a>
                </>
              ) : (
                <>
                  <Space.Compact
                    size="large"
                    className={classes["config-edit-input"]}
                  >
                    {<Icon component={svgMap(editIcon)} />}
                    <Input
                      ref={inputRef} // 关联引用
                      bordered={false}
                      value={editBtnInput}
                      onChange={(e) => setEditBtnInput(e.target.value)}
                      suffix={
                        <ColorPicker
                          value={editColorPicker}
                          onChange={(color: Color) =>
                            setEditColorPicker("#" + color.toHex())
                          }
                          styles={{
                            popupOverlayInner: {
                              width: 290,
                            },
                          }}
                          presets={[
                            {
                              label: "按钮颜色",
                              defaultOpen: true,
                              colors: [
                                "#FF7570",
                                "#FF811A",
                                "#FFC60A",
                                "#91AD00",
                                "#35BD4B",
                                "#2DBEAB",
                                "#25B0E7",
                                "#7AA2FF",
                                "#EB78B8",
                                "#B791FA",
                                "#8F959E",
                                "#FEE3E2",
                                "#FEE7CD",
                                "#FAEDC2",
                                "#E3F0A3",
                                "#D0F5CE",
                                "#C4F2EC",
                                "#CAEFFC",
                                "#E0E9FF",
                                "#FEE2F2",
                                "#EFE6FE",
                                "#EFF0F1",
                                "#FDC6C4",
                                "#F8C68E",
                                "#F8E083",
                                "#C7DD65",
                                "#9CE49B",
                                "#80E6D7",
                                "#A1DBFB",
                                "#C5D3FE",
                                "#F5C5E1",
                                "#DCC9FC",
                                "#DEE0E3",
                                "#EC524B",
                                "#E47221",
                                "#D39B1F",
                                "#7B9217",
                                "#42A548",
                                "#37A793",
                                "#3A93C8",
                                "#6081F9",
                                "#D85DA5",
                                "#A06FEF",
                                "#656A73",
                                "#CF4237",
                                "#9E4C13",
                                "#825C10",
                                "#5C6D12",
                                "#3EA027",
                                "#216F62",
                                "#266993",
                                "#3753ED",
                                "#B13079",
                                "#7D34EE",
                                "#383C43",
                              ],
                            },
                          ]}
                          panelRender={(
                            _,
                            { components: { Picker, Presets } }
                          ) => (
                            <div
                              className="custom-panel"
                              style={{
                                display: "flex",
                                width: 271,
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <div>
                                <Space size={5}>
                                  {icons.map((svg) => {
                                    return (
                                      <Button
                                        className={classes["color-picker-icon"]}
                                        type="text"
                                        size="small"
                                        onClick={() => {
                                          setEditIcon(svg);
                                        }}
                                      >
                                        <Icon component={svgMap(svg)} />
                                      </Button>
                                    );
                                  })}
                                </Space>
                              </div>
                              <Divider
                                style={{
                                  height: "auto",
                                  margin: "8px 0",
                                }}
                              />
                              <div
                                style={{
                                  flex: 1,
                                }}
                              >
                                <Presets />
                              </div>
                            </div>
                          )}
                        >
                          <DownCircleOutlined />
                        </ColorPicker>
                      }
                      onKeyDown={handleEnterKeySave}
                    />
                    <Button
                      type="text"
                      shape="circle"
                      onClick={() => {
                        setBtnConfig(btnConfig);
                        setEditBtn(undefined);
                      }}
                    >
                      <PlusCircleFilled style={{ color: "#A5B1C5" }} />
                    </Button>
                  </Space.Compact>
                </>
              )
              }
            </div>
          );
        })}
        <Button
          type="text"
          shape="circle"
          onClick={() => {
            if (!onAddBtnCheck()) {
              return;
            }
            const newBtn = {
              icon: "LikeSvg",
              value: "",
              color: "#0FB698",
              edit: true,
            };

            const newBtnList = btnConfig.map((item) => {
              item.edit = false;
              return item;
            });
            newBtnList.push(newBtn);
            setBtnConfig(newBtnList);
            setEditBtn(newBtn);
            setEditIndex(newBtnList.length - 1);
            setEditBtnInput(newBtn.value);
            setEditColorPicker(newBtn.color);
            setEditIcon(newBtn.icon);
          }}
        >
          <PlusCircleFilled style={{ color: "#A5B1C5" }} />
        </Button>
      </Space >
    </>
  );
});

export default ReviewConfigBtn;
