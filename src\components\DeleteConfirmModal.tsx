import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, Alert } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { useState } from "react";
import warnIcon from "../assets/img/modal-warn-icon.svg";
const { confirm } = Modal;
interface DeleteConfirmModalProps {
  visible: boolean;
  onCancel: () => void;
  onDelete: (modelId?: string) => Promise<void>;
  title?: string;
  content?: string;
  okText?: string;
  cancelText?: string;
}
const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  visible,
  onCancel,
  onDelete,
  title = "提示",
  content = "此操作将永久删除该条目，是否继续？",
  okText = "确认删除",
  cancelText = "取消",
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleDelete = async () => {
    try {
      setLoading(true);
      setError("");
      await onDelete();
      onCancel(); // 成功后关闭弹窗
    } catch (e) {
      setError("删除失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onCancel}
      footer={[ <div style={{ width: "100%", display: "flex", justifyContent: "center" ,gap:"10px"}}>
        {" "}
        <button
          key="cancel"
          onClick={onCancel}
          className="custom-button"
          disabled={loading}
        >
          {cancelText}
        </button>
        <button
          key="delete"
          className="custom-button"
          onClick={handleDelete}
          disabled={loading}
        >
          {okText}
        </button>
      </div>
       ,
      ]}
      title={
        <div style={{ display: "flex", alignItems: "center" }}>
          <img src={warnIcon} alt="警告" className="warning" />
          <span style={{ marginLeft: 8 }}>{title}</span>
        </div>
      }
    >
      <Spin spinning={loading}>
        <div style={{ display: "flex", justifyContent: "center" }}>
          {content}
        </div>
        {error && <Alert message={error} type="error" showIcon />}
      </Spin>
    </Modal>
  );
};

export default DeleteConfirmModal;
