import React, { useRef } from 'react';
import { Button, Form, message, Space } from 'antd';
import { useNavigate } from 'react-router-dom';
import { Scrollbar } from 'react-scrollbars-custom';
import { createTask } from '../../../api/task';
import SelectDatasetModal from '../../../components/SelectDatasetModal';
import UploadErrorModal from '../../../components/UploadErrorModal';
import '@/css/uploadBaseModel.css';

// 导入类型定义
import { CreateTaskFormType, CreateTaskParams, TaskConfigMap } from './types';

// 导入常量
import { TASK_CONFIG_MAP, BUTTON_TEXTS } from './constants';

// 导入自定义Hooks
import { useTemplateData } from './hooks/useTemplateData';
import { useFileManagement } from './hooks/useFileManagement';
import { useLabelManagement } from './hooks/useLabelManagement';
import { useModelConfig } from './hooks/useModelConfig';

// 导入子组件
import FormHeader from './components/FormHeader';
import DatasetSelectionSection from './components/DatasetSelectionSection';
import ModelConfigSection from './components/ModelConfigSection';
import RequirementDescriptionSection from './components/RequirementDescriptionSection';
import AutoLabelSection from './components/AutoLabelSection';
import { LeftOutlined } from '@ant-design/icons';

const CreateTaskView: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const datasetModalRef = useRef<any>(null);

  // 使用自定义Hooks
  const templateData = useTemplateData();
  const fileManagement = useFileManagement();
  const labelManagement = useLabelManagement();
  const modelConfig = useModelConfig();

  // 任务名称状态
  const [taskName, setTaskName] = React.useState('');

  // 处理返回
  const handleBack = () => {
    navigate(-1);
  };

  // 处理任务名称变化
  const handleTaskNameChange = (name: string) => {
    setTaskName(name);
  };

  // 处理文件删除
  const handleDelFile = (index: number) => {
    fileManagement.handleDelFile(index, datasetModalRef);
  };

  // 处理AI生成标签
  const handleAIGenerate = () => {
    labelManagement.handleAIGenerate(fileManagement.fileList);
  };

  // 验证成功
  const onFinish = (values: CreateTaskFormType) => {
    // 验证标签格式
    if (!labelManagement.validateLabels()) {
      return;
    }

    if (labelManagement.labelText) {
      values.description = templateData.textValue;
    }
    values.domain = templateData.textValueTemplate.templateName;

    const taskConfigMap: TaskConfigMap = TASK_CONFIG_MAP.AUTO;
    if (modelConfig.modelConfig === '手动配置') {
      taskConfigMap.splitLevel = modelConfig.splitLevel ? modelConfig.splitLevel / 25 + 1 : 3;
      taskConfigMap.questionDensity = modelConfig.questionDensityValue
        ? modelConfig.questionDensityValue / 25 + 1
        : 3;
    }

    const params: CreateTaskParams = {
      taskName: values.taskName,
      datasetList: fileManagement.fileList.map((file) => file.dataSetId!),
      taskConfigMap,
      description: values.description,
      domain: values.domain,
      tags: labelManagement.autoLabel
        ? labelManagement.labelText.split('，').filter((item) => item.trim())
        : [],
    };
    createTask(params).then((res) => {
      if (res?.data?.code === 200) {
        navigate('/main/task');
      } else {
        message.error(res?.data?.message);
      }
    });
  };

  // 验证失败
  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <Scrollbar>
      <div className="createTaskContent">
        <Space size={20} style={{ display: 'inline-flex', alignItems: 'center' }}>
          <Button
            style={{ fontSize: '12px', width: '36px', height: '36px' }}
            shape="circle"
            icon={<LeftOutlined style={{ fontSize: '16px' }} />}
            onClick={handleBack}
          />
          <div
            className="mediumText"
            style={{ fontSize: '28px', lineHeight: '36px', fontWeight: '500' }}
          >
            生成训练数据
          </div>
        </Space>
        <div className="createTaskArea" style={{ marginBottom: '47px' }}>
          <Form
            form={form}
            className="reqularText"
            name="createTaskForm"
            layout="vertical"
            initialValues={{}}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <FormHeader taskName={taskName} onTaskNameChange={handleTaskNameChange} />

            <DatasetSelectionSection
              fileList={fileManagement.fileList}
              onShowDataModal={fileManagement.handleShowDataModal}
              onAddTag={fileManagement.handleAddTag}
              onDelFile={handleDelFile}
              onReUpload={fileManagement.handleReUpload}
              datasetModalRef={datasetModalRef}
            />

            <ModelConfigSection
              modelConfig={modelConfig.modelConfig}
              splitLevel={modelConfig.splitLevel}
              questionDensityValue={modelConfig.questionDensityValue}
              onModelConfigChange={modelConfig.handleModelConfigChange}
              onSplitLevelChange={modelConfig.handleSplitLevelChange}
              onQuestionDensityChange={modelConfig.handleQuestionDensityChange}
            />

            <RequirementDescriptionSection
              exampleText={templateData.exampleText}
              selectedIndex={templateData.selectedIndex}
              textValue={templateData.textValue}
              onItemClick={templateData.handleItemClick}
              onApplyTemplate={templateData.handleApplyTemplate}
              onTextChange={templateData.handleTextChange}
            />

            <AutoLabelSection
              autoLabel={labelManagement.autoLabel}
              labelText={labelManagement.labelText}
              isGeneratingLabels={labelManagement.isGeneratingLabels}
              fileList={fileManagement.fileList}
              onAutoLabelChange={labelManagement.handleAutoLabelChange}
              onLabelTextChange={labelManagement.handleLabelTextChange}
              onAIGenerate={handleAIGenerate}
            />

            <div style={{ textAlign: 'center', marginTop: '90px' }}>
              <Button
                shape="round"
                size="large"
                htmlType="submit"
                className="createBtn"
                disabled={
                  !taskName ||
                  taskName?.length === 0 ||
                  fileManagement.fileList.length === 0 ||
                  fileManagement.fileList?.some((file) => (file.status as any) !== 'success')
                }
              >
                {BUTTON_TEXTS.START_TASK}
              </Button>
            </div>
          </Form>

          <SelectDatasetModal
            ref={datasetModalRef}
            visible={fileManagement.showDataModal}
            OnClose={fileManagement.handleDataModalOk}
          />

          <UploadErrorModal
            visible={fileManagement.uploadErrorModal}
            OnClose={fileManagement.handleCloseUploadErrorModal}
            rowData={undefined}
          />
        </div>
      </div>
    </Scrollbar>
  );
};

export default CreateTaskView;
