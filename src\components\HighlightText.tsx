import React from 'react';

interface HighlightTextProps {
  text: string;
  searchKeyword: string;
}

const HighlightText: React.FC<HighlightTextProps> = ({ text, searchKeyword }) => {
  if (!searchKeyword.trim()) {
    return <span>{text}</span>;
  }

  // 使用正则表达式进行全局匹配，不区分大小写
  const regex = new RegExp(`(${searchKeyword})`, 'gi');
  const parts = text.split(regex);

  return (
    <span>
      {parts.map((part, index) =>
        regex.test(part) ? (
          <mark key={index} style={{ backgroundColor: 'rgba(15, 182, 152, 0.3)' }}>{part}</mark>
        ) : (
          <span key={index}>{part}</span>
        )
      )}
    </span>
  );
};

export default HighlightText;
