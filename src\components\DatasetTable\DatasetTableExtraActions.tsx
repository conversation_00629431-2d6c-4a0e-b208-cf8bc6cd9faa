import { Button, Modal, Space, Table, message } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import bxcodeblock from '../../assets/img/bxcodeblock.svg';
import bxtrash from '../../assets/img/bxtrash.svg';
import { deleteDataset } from '../../api/dataset';
import { deleteTask } from '../../api/task';
import { DataSetType, TaskDeleteReviewType } from '../../types';

interface DatasetTableExtraActionsProps {
  selectedRows: DataSetType[];
  setUploadDatasetModal: (val: boolean) => void;
  tableRef: React.RefObject<any>;
  setSelectedRows: (rows: DataSetType[]) => void;
}

const DatasetTableExtraActions: React.FC<DatasetTableExtraActionsProps> = ({
  selectedRows,
  setUploadDatasetModal,
  tableRef,
  setSelectedRows,
}) => {
  const navigate = useNavigate();
  const { confirm } = Modal;

  const handleDelete = () => {
    const hasRelatedTask = selectedRows.some((row) => row.relatedQATaskList.length > 0);

    let relatedTaskList: any[] = [];
    if (hasRelatedTask) {
      relatedTaskList = selectedRows
        .filter((row) => row.relatedQATaskList.length > 0)
        .map((item) =>
          item.relatedQATaskList.map((task) => ({
            ...task,
            datasetName: item.name,
            datasetId: item.id,
          }))
        )
        .flat();
    }

    const columns: ColumnsType<TaskDeleteReviewType> = [
      {
        title: '数据集名称',
        dataIndex: 'datasetName',
        key: 'datasetName',
        render: (_, taskInfo) => <a className="dataset-name">{taskInfo.datasetName}</a>,
      },
      {
        title: '任务名称',
        dataIndex: 'taskName',
        key: 'taskName',
        render: (_, taskInfo) => (
          <a
            className="dataset-name"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/main/task/detail/${taskInfo.taskId}`);
              modal.destroy();
            }}
          >
            {taskInfo.taskName}
          </a>
        ),
      },
      {
        title: 'Action',
        key: 'action',
        render: (_, { taskId }) => (
          <Space>
            <Button
              type="link"
              style={{ color: '#0fb698', fontFamily: 'HarmonyOS Sans SC Reqular' }}
              onClick={() => {
                navigate(`/main/task/detail/${taskId}`);
                modal.destroy();
              }}
            >
              详情
            </Button>
            <Button
              type="link"
              style={{ color: '#0fb698', fontFamily: 'HarmonyOS Sans SC Reqular' }}
              onClick={() => {
                const taskModal = confirm({
                  centered: true,
                  title: '删除提示',
                  icon: <ExclamationCircleFilled />,
                  width: 540,
                  content: <div className="default-info">确定要删除所选任务吗？</div>,
                  onOk() {
                    return deleteTask([taskId]).then((res) => {
                      if (res.data.code === 200) {
                        taskModal.destroy();
                        modal.destroy();
                      } else {
                        message.error(res.data.message);
                      }
                    });
                  },
                });
              }}
            >
              删除
            </Button>
          </Space>
        ),
      },
    ];

    const modal = confirm({
      centered: true,
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      width: 540,
      content: (
        <>
          <div className="default-info" style={{ color: 'black' }}>
            确定要删除所选数据集吗？
          </div>
          {hasRelatedTask && (
            <>
              <div className="upload-error-label" style={{ marginTop: '8px' }}>
                所选源数据集有关联的推理任务，无法进行删除。如需删除，请先删除关联任务。
              </div>
              <Table
                scroll={{ y: 400 }}
                size="small"
                pagination={false}
                columns={columns}
                dataSource={relatedTaskList}
                rowKey="taskId"
                className="dataset-table"
                tableLayout="fixed"
              />
            </>
          )}
        </>
      ),
      footer: [
        <div
          key="footer"
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '8px',
            paddingTop: '2rem',
          }}
        >
          <Button type="text" onClick={() => modal.destroy()} shape="round">
            取消
          </Button>
          <Button
            type="primary"
            disabled={hasRelatedTask}
            onClick={() => {
              const rowIds = selectedRows.map((item) => item.id);
              tableRef.current?.handleDeleteRows(rowIds);
              deleteDataset(rowIds).then((res) => {
                modal.destroy();
                setSelectedRows([]);
                if (res.data.code === 0 || res.data.code === 200) {
                  tableRef.current?.onRefresh();
                }
              });
            }}
            shape="round"
            className="primary-btn"
            style={{ width: '120px' }}
          >
            确认删除
          </Button>
        </div>,
      ],
    });
  };

  return (
    <div style={{ display: 'inline-flex' }}>
      <Button
        size="large"
        onClick={() => setUploadDatasetModal(true)}
        className="upload-dataset-btn"
        style={{ marginRight: '8px' }}
      >
        <img className="btn-icon" src={bxcodeblock} />
        导入数据集
      </Button>
      <Button
        disabled={selectedRows.length === 0}
        size="large"
        className="upload-dataset-btn"
        style={{ marginRight: '16px' }}
        onClick={handleDelete}
      >
        <img className="btn-icon" src={bxtrash} />
        删除数据集
      </Button>
    </div>
  );
};

export default DatasetTableExtraActions;
