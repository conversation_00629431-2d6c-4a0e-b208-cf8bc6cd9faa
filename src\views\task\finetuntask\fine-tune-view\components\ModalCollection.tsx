import React from 'react';
import { Button, DescriptionsProps, Modal, Tooltip, message } from 'antd';
import warningIcon from '@/assets/img/warningIcon.svg';
import infoIcon from '@/assets/img/info-icon.svg';
import TimeModal from '@/components/TimeModal';
import OpenDraw from '@/components/OpenDraw';
import DescriptionsWrapper from '../DescriptionWrapper';
import { deleteBaseModel, deleteModel, onlineModel, startTrain, cannelTrain } from '@/api/modle';

interface ModalCollectionProps {
  // Delete Modal
  deleteModelDialog: boolean;
  setDeleteModelDialog: (show: boolean) => void;
  modelId: string;
  handleBaseDelete: (id: string) => void;

  // Time Modal
  online: boolean;
  setOnline: (show: boolean) => void;
  prolong: boolean;
  setProlong: (show: boolean) => void;
  timeValue: any;
  setTimeValue: (value: any) => void;
  handleButtonClick: (value: number) => void;

  // Light Weight Modal
  lightWeight: boolean;
  setLightWeight: (show: boolean) => void;
  currentItemId: number;
  lightWeightState: any;
  confirmLightWeight: (id: any) => void;
  status: any;

  // Off Model Modal
  offmodel: boolean;
  setOffmodel: (show: boolean) => void;

  // Cancel Train Modal
  cancelTrain: boolean;
  setCancelTrain: (show: boolean) => void;
  modalTitle: string;

  // Draw Modals
  openDrawView: boolean;
  setOpenDrawView: (show: boolean) => void;
  myopenDrawView: boolean;
  setMyOpenDrawView: (show: boolean) => void;
  interruptDrawView: boolean;
  setInterruptDrawView: (show: boolean) => void;

  titltext: string;
  mytitltext: string;
  moduletext: string;
  buttonText: string;
  descItems: DescriptionsProps['items'];
  trainItems: DescriptionsProps['items'];
  interruptItems: DescriptionsProps['items'];
  tooltip: any;

  onNavigateToCreate: () => void;
  onDeleteClick: () => void;
  getModels: () => void;
}

const ModalCollection: React.FC<ModalCollectionProps> = ({
  deleteModelDialog,
  setDeleteModelDialog,
  modelId,
  handleBaseDelete,
  online,
  setOnline,
  prolong,
  setProlong,
  timeValue,
  setTimeValue,
  handleButtonClick,
  lightWeight,
  setLightWeight,
  currentItemId,
  lightWeightState,
  confirmLightWeight,
  status,
  offmodel,
  setOffmodel,
  cancelTrain,
  setCancelTrain,
  modalTitle,
  openDrawView,
  setOpenDrawView,
  myopenDrawView,
  setMyOpenDrawView,
  interruptDrawView,
  setInterruptDrawView,
  titltext,
  mytitltext,
  moduletext,
  buttonText,
  descItems,
  trainItems,
  interruptItems,
  tooltip,
  onNavigateToCreate,
  onDeleteClick,
  getModels,
}) => {
  return (
    <>
      {/* Delete Model Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src={warningIcon} alt="警告" className="warning" />
            <span style={{ marginLeft: 8 }}>提示</span>
          </div>
        }
        centered
        open={deleteModelDialog}
        closable={false}
        width={520}
        className="delete-model"
        footer={[
          <Button className="modal-btn" onClick={() => setDeleteModelDialog(false)}>
            取消
          </Button>,
          <Button className="modal-btn" onClick={() => handleBaseDelete(modelId)}>
            确定
          </Button>,
        ]}
        wrapClassName="custom-modal"
      >
        <p>确定要删除模型吗？</p>
      </Modal>

      {/* Time Modals */}
      <TimeModal
        visible={online}
        titltext={'选择上线时间'}
        buttontext={'确认上线'}
        timetext="持续"
        onButtonClick={() => handleButtonClick(timeValue)}
        value={timeValue}
        OnClose={() => setOnline(false)}
        onValueChange={setTimeValue}
      >
        <></>
      </TimeModal>

      <TimeModal
        visible={prolong}
        titltext={'延长上线时间'}
        buttontext={'确认延长'}
        timetext="延长"
        onButtonClick={() => setProlong(false)}
        OnClose={() => setProlong(false)}
      >
        <></>
      </TimeModal>

      {/* Light Weight Modal */}
      <Modal
        width={429}
        title={
          <div className="modalTitle">
            <img src={warningIcon} style={{ marginRight: '12px' }} alt="" />
            模型体积信息
          </div>
        }
        centered
        open={lightWeight}
        footer={null}
        maskClosable={false}
        onCancel={() => setLightWeight(false)}
      >
        <div className="lightWeightModal">
          <span className={'lightWeightContent origion'}>原模型体积：10GB</span>
          <span className={'lightWeightContent'}>
            轻量化模型体积：
            {status === 7
              ? '8GB'
              : lightWeightState[currentItemId as keyof typeof lightWeightState]
                ? '8GB'
                : '待确认'}
          </span>
        </div>
        <div className="offmodelButton">
          <Button
            className="offmodeltrue"
            disabled={
              status === 7 ? true : lightWeightState[currentItemId as keyof typeof lightWeightState]
            }
            onClick={() => confirmLightWeight(currentItemId)}
          >
            轻量化
          </Button>
        </div>
      </Modal>

      {/* Off Model Modal */}
      <Modal
        width={429}
        title="模型下线"
        centered
        open={offmodel}
        footer={null}
        maskClosable={false}
        onCancel={() => setOffmodel(false)}
      >
        <div className="offmodel">确认模型下线</div>
        <div className="offmodelButton">
          <Button className="offmodelCancel" onClick={() => setOffmodel(false)}>
            取消
          </Button>
          <Button
            className="offmodeltrue"
            onClick={() => {
              setOffmodel(false);
              onlineModel(modelId).then((res: any) => {
                if (res.data?.code === 200) {
                  message.success(res.data?.data?.message || '下线成功');
                }
              });
            }}
          >
            确认下线
          </Button>
        </div>
      </Modal>

      {/* Cancel Train Modal */}
      <Modal
        width={429}
        title={modalTitle}
        centered
        open={cancelTrain}
        footer={null}
        maskClosable={false}
        onCancel={() => setCancelTrain(false)}
      >
        <div className="offmodel">{'确认' + modalTitle}</div>
        <div className="offmodelButton">
          <Button className="offmodelCancel" onClick={() => setCancelTrain(false)}>
            取消
          </Button>
          <Button
            className="offmodeltrue"
            onClick={() => {
              if (modalTitle === '模型训练') {
                startTrain(modelId).then((res: any) => {
                  if (res.data?.code === 200) {
                    message.success('开始训练');
                  }
                });
              } else if (modalTitle === '模型暂停') {
                cannelTrain(Number(modelId)).then((res: any) => {
                  if (res.data?.code === 200) {
                    message.success('暂停成功');
                  }
                });
              }
              setCancelTrain(false);
            }}
          >
            确认
          </Button>
        </div>
      </Modal>

      {/* Draw Modals */}
      <OpenDraw
        visible={openDrawView}
        titltext={titltext}
        moduletext={moduletext}
        buttontext={buttonText}
        logo={true}
        text={true}
        isDelete={true}
        tooltip={tooltip}
        onButtonClick={onNavigateToCreate}
        OnClose={() => setOpenDrawView(false)}
        deleteClick={onDeleteClick}
      >
        <DescriptionsWrapper>{descItems}</DescriptionsWrapper>
      </OpenDraw>

      <OpenDraw
        visible={myopenDrawView}
        titltext={titltext}
        buttontext={'确定'}
        logo={false}
        text={false}
        isbutton={false}
        onButtonClick={() => setMyOpenDrawView(false)}
        OnClose={() => setMyOpenDrawView(false)}
      >
        <DescriptionsWrapper>{trainItems}</DescriptionsWrapper>
      </OpenDraw>

      <OpenDraw
        visible={interruptDrawView}
        titltext={titltext}
        buttontext={'确定'}
        logo={false}
        text={false}
        isbutton={false}
        onButtonClick={() => setInterruptDrawView(false)}
        OnClose={() => setMyOpenDrawView(false)}
      >
        <DescriptionsWrapper>{trainItems}</DescriptionsWrapper>
      </OpenDraw>

      <OpenDraw
        visible={interruptDrawView}
        titltext={titltext}
        buttontext="重新训练"
        interruptText="请结合中断原因及训练进度选择适合您的训练方式"
        isInterruptText={true}
        logo={false}
        text={false}
        isbutton={false}
        onButtonClick={() => {
          startTrain(modelId).then((res: any) => {
            if (res.data?.code === 200) {
              message.success('开始训练');
              setInterruptDrawView(false);
            } else {
              setInterruptDrawView(false);
            }
          });
        }}
        OnClose={() => setInterruptDrawView(false)}
      >
        <DescriptionsWrapper>{interruptItems}</DescriptionsWrapper>
        <div style={{ position: 'absolute', bottom: '103px', left: '18%' }}>
          <Tooltip title="请结合中断原因及训练进度选择适合您的训练方式">
            <img src={infoIcon} style={{ width: '16px', height: '16px' }} />
          </Tooltip>
        </div>
      </OpenDraw>
    </>
  );
};

export default ModalCollection;
