import { Col, Divider, Radio, Row, Tabs, TabsProps } from "antd";
import LoginDefaultBox from "./components/Login";
import LoginMessageBox from "../../components/LoginMessageBox";
import { Link } from "react-router-dom";
import { useState } from "react";
import UserAgreement from "../../components/UserAgreement";
import WelcomePage from "../../components/WelcomePage";
import "./LoginView.css";
import { Scrollbar } from "react-scrollbars-custom";

const LoginView: React.FC = () => {
  const [isAgree, setIsAgree] = useState(false);

  const loginTabItem: TabsProps["items"] = [
    {
      label: `账号登录`,
      key: "1",
      children: <LoginDefaultBox isAgree={isAgree}></LoginDefaultBox>,
    },
    // {
    //   label: `短信登录`,
    //   key: "2",
    //   children: <LoginMessageBox isAgree={isAgree}></LoginMessageBox>,
    // },
  ];

  const handleIsAgreeChange = (newValue: boolean) => {
    setIsAgree(newValue);
  };

  return (
    <Scrollbar style={{ position: "inherit" }}>
      <div className="bg">
        <Row justify="start" style={{ minWidth: "1440px" }}>
          <Col span={12} className="loginLf">
            <WelcomePage></WelcomePage>
          </Col>
          <Col span={12} className="loginRt">
            <div className="loginBox">
              <b className="loginTitle boldText">行至账号</b>
              <Tabs
                destroyInactiveTabPane={false}
                animated={true}
                size="large"
                defaultActiveKey="1"
                items={loginTabItem}
                className="loginTabs"
              />
              <UserAgreement
                isAgree={isAgree}
                onIsAgreeChange={handleIsAgreeChange}
              ></UserAgreement>
            </div>
          </Col>
        </Row>
      </div>
    </Scrollbar>
  );
};

export default LoginView;
