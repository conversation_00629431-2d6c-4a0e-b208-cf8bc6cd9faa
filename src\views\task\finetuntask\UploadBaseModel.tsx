import React, { useState, useEffect } from "react";
import { Button, Input, Space, Form, Select, message } from "antd";
import "../../../css/uploadBaseModel.css";
import { LeftOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { queryServer } from "../../../api/server";
import { uploadModel, queryBaseModel } from "../../../api/modle";

interface OptionsType {
  label: string;
  value: string;
}

export interface UploadBaseModelFormType {
  customModelName: string; // 模型名称（直接使用字符串）
  introduction: string; // 模型描述
  manufacturer: string; // 模型厂家
  modelVersion: string; // 模型版本
  parameter: string; // 模型参数量
  serverId: string | number; // 服务器ID（实际提交的值）
}
export interface AddBaseModelFormType {
  modelName: string; // 模型名称
  introduction: string; // 模型描述
  manufacturer: string; // 模型厂家
  modelVersion: string; // 模型版本
  parameter: string; // 模型参数量
  modelPath: string; // 模型地址
}

const UploadBaseModel: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<UploadBaseModelFormType>();
  const [serverOptions, setServerOptions] = useState<OptionsType[]>([]);
  const [modelOptions, setModelOptions] = useState<OptionsType[]>([]);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        const [servers, models] = await Promise.all([
          queryServer(),
          queryBaseModel(),
        ]);

        // 转换服务器数据：显示名称 => 值用ID
        setServerOptions(
          servers.map((server) => ({
            label: server.serverName,
            value: server.id.toString(),
          }))
        );

        // 转换模型数据：显示名称 => 值用名称
        setModelOptions(
          models.map((model) => ({
            label: model.modelName,
            value: model.modelName,
          }))
        );
      } catch (error) {
        console.error("初始化数据失败:", error);
      }
    };

    loadOptions();
  }, []);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log(values);
      const finallyValues = {
        ...values,
        serverId: Number(values.serverId),
      };
      const res = await uploadModel(finallyValues);
      if (res.code === 200) {
        message.success("上传成功");
        navigate(-1);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error("提交失败，请检查表单");
    }
  };

  return (
    <div className="uploadBaseModel">
      <Space size={20} style={{ display: "inline-flex", alignItems: "center" }}>
        <Button
          shape="circle"
          icon={<LeftOutlined />}
          onClick={() => navigate(-1)}
          style={{ fontSize: "12px", width: "36px", height: "36px" }}
        />
        <div
          className="mediumText"
          style={{ fontSize: "20px", lineHeight: "36px", fontWeight: "500" }}
        >
          载入基座模型
        </div>
      </Space>

      <div className="uploadModelArea">
        <Form
          form={form}
          onFinish={handleSubmit}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18, offset: 2 }}
        >
          {/* 模型名称选择（值直接使用字符串） */}
          <Form.Item<UploadBaseModelFormType>
            name="customModelName"
            label="模型名称"
            rules={[{ required: true, message: "请输入模型名称" }]}
          >
            <Input
              placeholder="请输入模型名称"
              // options={modelOptions}
              size="large"
              style={{ width: 511 }}
            />
          </Form.Item>
          {/* 服务器选择（显示名称，提交ID） */}
          <Form.Item<UploadBaseModelFormType>
            name="serverId"
            label="服务器名称"
            rules={[{ required: true, message: "请选择服务器名称" }]}
          >
            <Select
              placeholder="请选择服务器"
              options={serverOptions}
              size="large"
              style={{ width: 511 }}
            />
          </Form.Item>

          {/* 模型版本 */}
          <Form.Item<UploadBaseModelFormType>
            name="modelVersion"
            label="模型版本"
          >
            <Input
              placeholder="请输入模型版本"
              size="large"
              style={{ width: 511 }}
            />
          </Form.Item>

          {/* 模型参数量 */}
          <Form.Item<UploadBaseModelFormType>
            name="parameter"
            label="模型参数量"
          >
            <Input
              placeholder="请输入模型参数量"
              size="large"
              style={{ width: 511 }}
            />
          </Form.Item>

          {/* 模型厂家 */}
          <Form.Item<UploadBaseModelFormType>
            name="manufacturer"
            label="模型厂家"
          >
            <Input
              placeholder="请输入模型厂家"
              size="large"
              style={{ width: 511 }}
            />
          </Form.Item>

          {/* 模型描述 */}
          <Form.Item<UploadBaseModelFormType>
            name="introduction"
            label="模型描述"
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入模型描述"
              size="large"
              style={{ width: 511 }}
            />
          </Form.Item>

          {/* 操作按钮 */}
          <Form.Item wrapperCol={{ offset: 8 }} style={{ marginTop: 40 }}>
            <Button
              shape="round"
              className="cancalBut"
              size="large"
              onClick={() => navigate(-1)}
              style={{ marginRight: 60 }}
            >
              取消
            </Button>
            <Button
              shape="round"
              type="primary"
              htmlType="submit"
              className="submBut"
              size="large"
              style={{ marginLeft: 60 }}
            >
              确认上传
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default UploadBaseModel;
