import React from 'react';
import { <PERSON><PERSON>, Space } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { QADocument, TaskDetailType } from '../../../types';
import { QaDeleteInfo, QaUpdateParams, deleteQA, updateQA } from '../../../api/qa';

interface QAEditSectionProps {
  question: QADocument;
  taskDetail: TaskDetailType;
  currentQues: string;
  currentAnsw: string;
  editQuestion: boolean;
  editAnswer: boolean;
  isConfirm: boolean;
  qaFileMap: Map<string, string>;
  onCurrentQuesChange: (value: string) => void;
  onCurrentAnswChange: (value: string) => void;
  onEditQuestionChange: (value: boolean) => void;
  onEditAnswerChange: (value: boolean) => void;
  onIsConfirmChange: (value: boolean) => void;
  onRefresh: () => void;
  onDeleteConfirm: () => void;
}

const QAEditSection: React.FC<QAEditSectionProps> = ({
  question,
  taskDetail,
  currentQues,
  currentAnsw,
  editQuestion,
  editAnswer,
  isConfirm,
  qaFileMap,
  onCurrentQuesChange,
  onCurrentAnswChange,
  onEditQuestionChange,
  onEditAnswerChange,
  onIsConfirmChange,
  onRefresh,
  onDeleteConfirm,
}) => {
  const handleQuestionSave = () => {
    const params: QaUpdateParams = {
      answer: currentAnsw,
      id: question.id,
      question: currentQues,
      taskId: taskDetail.id,
    };
    updateQA(params).then((res) => {
      if (res.data?.code === 200 && taskDetail) {
        onRefresh();
      }
    });
    onEditQuestionChange(false);
  };

  const handleAnswerSave = () => {
    const params: QaUpdateParams = {
      answer: currentAnsw,
      id: question.id,
      question: currentQues,
      taskId: taskDetail.id,
    };
    updateQA(params).then((res) => {
      if (res.data?.code === 200 && taskDetail) {
        onRefresh();
      }
    });
    onEditAnswerChange(false);
  };

  const handleQuestionCancel = () => {
    onCurrentQuesChange(question.question);
    onEditQuestionChange(false);
  };

  const handleAnswerCancel = () => {
    onCurrentAnswChange(question.answer);
    onEditAnswerChange(false);
  };

  return (
    <>
      <Space direction="vertical" style={{ width: '100%', flex: '1', padding: '0 1rem' }} size={18}>
        <div style={{ color: '#6D7279' }}>
          <span className="boldText" style={{ fontSize: 16, marginRight: 32 }}>
            Step 1
          </span>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
          }}
          className="boldText"
        >
          问题：
        </div>
        <div style={{ color: '#6D7279' }}>
          请审核生成的问题和答案内容是否正确，如不正确请对其进行编辑和修改，也可以将其删除
        </div>
        <div style={{ position: 'relative' }}>
          <TextArea
            className="review-textarea"
            value={currentQues}
            style={{ resize: 'none', height: 60 }}
            onChange={(e) => {
              if (!editQuestion) {
                onEditQuestionChange(true);
              }
              onIsConfirmChange(false);
              onCurrentQuesChange(e.target.value);
            }}
          />
          <div
            style={{
              position: 'absolute',
              right: '0px',
              visibility: editQuestion ? 'inherit' : 'hidden',
            }}
          >
            <Space>
              <Button type="link" style={{ padding: '4px 0' }} onClick={handleQuestionSave}>
                保存
              </Button>
              <Button type="link" style={{ padding: '4px 0' }} onClick={handleQuestionCancel}>
                撤销
              </Button>
            </Space>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
          }}
          className="boldText"
        >
          <label>回答：</label>
        </div>
        <div style={{ color: '#6D7279' }}>
          请审核生成的问题和答案内容是否正确，如不正确请对其进行编辑和修改，也可以将其删除
        </div>
        <div style={{ position: 'relative' }}>
          <TextArea
            className="review-textarea"
            value={currentAnsw}
            style={{ resize: 'none', height: 148 }}
            onChange={(e) => {
              if (!editAnswer) {
                onEditAnswerChange(true);
              }
              onIsConfirmChange(false);
              onCurrentAnswChange(e.target.value);
            }}
          />
          <div
            style={{
              position: 'absolute',
              right: '0px',
              visibility: editAnswer ? 'inherit' : 'hidden',
            }}
          >
            <Space>
              <Button type="link" style={{ padding: '4px 0' }} onClick={handleAnswerSave}>
                保存
              </Button>
              <Button type="link" style={{ padding: '4px 0' }} onClick={handleAnswerCancel}>
                撤销
              </Button>
            </Space>
          </div>
        </div>
        <Space size={19}>
          <Button
            type="primary"
            className={isConfirm ? ' review-btn-confirm' : ' review-btn-default'}
            style={{ width: 120 }}
            onClick={() => {
              onIsConfirmChange(true);
            }}
          >
            确认无误
          </Button>
          <Button
            type="primary"
            className={'review-btn-delete'}
            style={{ width: 120 }}
            onClick={onDeleteConfirm}
          >
            删除本条
          </Button>
        </Space>
      </Space>
    </>
  );
};

export default QAEditSection;
