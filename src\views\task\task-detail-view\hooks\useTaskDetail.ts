import { useState, useEffect, useRef } from 'react';
import { message } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { TaskDetailType } from '../../../../types';
import { UserType } from '../../../../api/user';
import { ReviewConfigBtnType } from '../../../../components/ReviewConfigModal/components/ReviewConfigBtn/type';
import {
  flattenFileTree,
  getQATaskFileTree,
  getReviewConfigInfo,
  getTaskDetail,
  isSetReviewConfigInfo,
} from '../../../../api/task';
import { getTaglList } from '../../../../api/qa';
import { getAllUserByTaskId } from '../../../../api/review';

export const useTaskDetail = (taskId: string) => {
  // 基础状态
  const [taskDetail, setTaskDetail] = useState<TaskDetailType>();
  const [fileTree, setFileTree] = useState<any[]>([]);
  const [flattenTree, setFlattenTree] = useState<any[]>([]);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [fileName, setFileName] = useState<string>();
  const [apiFlg, setApiFlg] = useState(false);
  const [tagList, setTagList] = useState<[]>([]);
  const [scoreButtonInfo, setScoreButtonInfo] = useState<ReviewConfigBtnType[]>([]);
  const [isSetReview, setIsSetReview] = useState<boolean>(false);

  // 审核相关状态
  const [verifyUsers, setVerifyUser] = useState<{ label: string; key: string; value: string }[]>(
    []
  );
  const [reviewOptions, setReviewOptions] = useState<
    { label: string; key: string; value: string }[]
  >([]);

  const intervalRef = useRef<NodeJS.Timer>();

  // 标签列表更新函数
  const handleTagListUpdate = () => {
    if (taskId) {
      getTaglList(taskId).then((res) => {
        if (res.data?.code === 200) {
          setTagList(res.data.data.tagS);
        } else {
          message.error(res.data?.message);
        }
      });
    }
  };

  // 获取任务详情和相关数据
  const onGetTaskDetail = (taskId: string) => {
    if (!apiFlg) {
      setApiFlg(true);
      getTaskDetail(taskId).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          const data = res.data.data;
          setTaskDetail(data);
        }
      });

      getQATaskFileTree(taskId).then((res) => {
        if (res.data?.code === 200) {
          const data = res.data.data;
          if (data) {
            const fileTrees = data.map((item: any, index: number) => ({
              ...item.fileTreeNodeForTask,
              fileId: item.fileTreeNodeForTask.fileId,
            }));

            const flattenTree = flattenFileTree(fileTrees);
            setFileTree(
              data.map((item: any, index: number) => {
                // item.fileTreeNodeForTask.fileId = index.toString();
                return item;
              })
            );
            setFlattenTree(flattenTree);
            setTreeData(fileTrees as any[]);
          }
        }
      });

      handleTagListUpdate();
    }
  };

  // 获取详细信息和初始化数据
  const getDetail = () => {
    if (taskId) {
      onGetTaskDetail(taskId);
      getAllUserByTaskId(taskId).then((res: any) => {
        if (res?.data?.code === 200) {
          const userList: UserType[] = res.data.data;
          setVerifyUser(
            userList.map((item) => {
              return {
                label: item.userName,
                value: item.id,
                key: `verifyUser_${item.id}`,
              };
            })
          );
        }
      });
      getReviewConfigInfo(taskId).then((res) => {
        if (res?.data?.code === 200 && res.data.data) {
          setReviewOptions(
            res.data?.data?.scoreButtonInfo.map((item: any) => {
              return {
                label: item.value,
                value: item.value,
                key: `reviewOption_${item.value}`,
              };
            })
          );
          setScoreButtonInfo(res.data.data.scoreButtonInfo);
        }
      });
      isSetReviewConfigInfo(taskId).then((res) => {
        if (res?.data?.code === 200) {
          setIsSetReview(!res.data.data);
        }
      });
    }
  };

  useEffect(() => {
    getDetail();
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout);
    };
  }, [taskId]);

  return {
    taskDetail,
    fileTree,
    flattenTree,
    treeData,
    fileName,
    apiFlg,
    tagList,
    scoreButtonInfo,
    isSetReview,
    verifyUsers,
    reviewOptions,
    intervalRef,
    handleTagListUpdate,
    onGetTaskDetail,
    getDetail,
  };
};
