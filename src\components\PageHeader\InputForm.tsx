import { updateLogoInfo } from '@/api/auth';
import { UploadOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Upload, UploadFile, UploadProps } from 'antd';
import { useEffect, useState } from 'react';

const InputForm = (props: any) => {
  const { inputContent, currentLogo } = props;
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };

  useEffect(() => {
    form.setFieldsValue({
      inputData: inputContent,
      logoUrl: [],
    });
    setFileList([]); // 初始化清空
  }, [inputContent]);

  const handleUploadChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    form.setFieldsValue({
      logoUrl: newFileList,
    });
  };

  const handleSubmit = (values: any) => {
    const params: any = {
      logoName: values.inputData,
    };

    if (values.logoUrl?.length) {
      params.file = values.logoUrl[0].originFileObj;
    }

    updateLogoInfo(params)
      .then((res: any) => {
        if (res?.status === 200) {
          message.success('修改成功');
          props.fetchAgain();
        }
      })
      .catch(() => {
        message.error('修改失败');
      });
    handleCancel();
  };

  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    props.onClose();
  };

  return (
    <div className="rename-input">
      <Form
        form={form}
        onFinish={handleSubmit}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18, offset: 2 }}
      >
        <Form.Item label="重命名" name="inputData">
          <Input placeholder="请输入 logo 名称" maxLength={25} />
        </Form.Item>

        <Form.Item
          label="上传图片"
          name="logoUrl"
          valuePropName="fileList"
          getValueFromEvent={normFile}
        >
          <Upload
            name="logo"
            listType="picture-card"
            maxCount={1}
            accept=".png,.jpg,.jpeg"
            beforeUpload={() => false} // 阻止自动上传
            showUploadList={false} // 自定义渲染，不显示默认列表
            fileList={fileList}
            onChange={handleUploadChange}
          >
            {fileList.length > 0 ? (
              <img
                src={
                  fileList[0].thumbUrl ||
                  (fileList[0].originFileObj && URL.createObjectURL(fileList[0].originFileObj))
                }
                alt="logo"
                style={{ width: '100%', objectFit: 'cover' }}
              />
            ) : currentLogo ? (
              <img
                src={
                  typeof currentLogo === 'string' ? currentLogo : URL.createObjectURL(currentLogo)
                }
                alt="logo"
                style={{ width: '100%', objectFit: 'cover' }}
              />
            ) : (
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            )}
          </Upload>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 15 }}>
          <Button type="primary" htmlType="submit" style={{ marginRight: 14 }}>
            提交
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default InputForm;
