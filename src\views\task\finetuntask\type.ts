export interface ChatMessageType {
    content: string;
    role: string;
    create_time?: Date;
    dialogue_id?: string;
    id?: string;
    update_time?: Date;
    typeWriter?: boolean;
  }

  export interface PlanFuncationsType {
    function_name: string;
    function_args: any;
  }
  export interface PlanType {
    index: number;
    content: string;
    functions: PlanFuncationsType;
  }

  export interface ChatType {
    message?: ChatMessageType[];
    dialogue_name: string;
    id: string;
    plan?: PlanType[];
    create_time: Date;
    user_id: string;
    update_time: Date;
  }
  interface FrameworkType {
    label: string;
    value: string;
  }
  export interface ConfigType {
    id: string;
    name: string;
    dep_framework: FrameworkType[];
    per_configuration: number;
    server_selection: FrameworkType[];
    ari_configuration:FrameworkType[]
  }
  export interface ServerDataType {
    label: string;
    value: string;
    id: number;
  };
  