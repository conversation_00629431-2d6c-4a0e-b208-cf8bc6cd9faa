import { Dispatch } from "react";
import { useDispatch } from "react-redux";
import { Button, Col, Form, Input, Row, Space } from "antd";
import { useNavigate, useSearchParams } from "react-router-dom";
import { defaultLogin } from "../../../store/auth/action";
import { queryUserInfo } from "../service";
import { getUserList } from "../../../store/user/action";
interface LoginDefaultBoxProps {
  isAgree: boolean;
}
type LoginFieldType = {
  account?: string;
  password?: string;
};

const LoginDefaultBox: React.FC<LoginDefaultBoxProps> = ({ isAgree }) => {
  // 定义dispatch 工具（发送action动作执行reducer）
  const dispath: Dispatch<any> = useDispatch();
  // 获取导航navigate
  const navigate = useNavigate();
  // 获取查询参数
  const [search] = useSearchParams();
  //获取到查询参数redirect 也就是 http://localhost:3000/#?redirect=/admin/abc
  // 上面地址的 /admin/abc
  const path = search.getAll("redirect")[0];
  const redirect = !path || path === '/login' || path === '/register' || path === '/' ? "/overview" : path;
  // 定义回调方法
  const callback = async () => {
    navigate(redirect);
    const res = await queryUserInfo();
    if(res && res.data) {
      sessionStorage.setItem("name", res.data.userName);
      sessionStorage.setItem('account',res.data.userAccount);
      sessionStorage.setItem('id',res.data.id)
    }

    dispath(getUserList());

  };

  const onRegister = () => {
    navigate("/register");
  };
  // 验证成功
  const onFinish = async (values: any) => {
    // 执行dispath action传入用户名和密码
    const { account, password } = values;
    dispath(defaultLogin({ account, password }, callback));
 

  };
  // 验证失败
  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <div>
      <Form
        name="loginDefault"
        // style={{ maxWidth: 600 }}
        initialValues={{}}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item<LoginFieldType>
          name="account"
          rules={[
            { required: true, message: "请输入账号" },
            // {
            //   pattern: /^1[3456789]\d{9}$/,
            //   message: "手机号格式不正确",
            // },
          ]}
        >
          <Input placeholder="请输入账号" className="loginInput" />
        </Form.Item>
        <Form.Item<LoginFieldType>
          validateTrigger="onSubmit"
          name="password"
          rules={[
            { required: true, message: "请输入密码" },
            {
              validator: (_, value) =>
                isAgree
                  ? Promise.resolve()
                  : Promise.reject(new Error("请阅读并同意协议")),
            },
          ]}
        >
          <Input.Password placeholder="请输入密码" className="loginInput" />
        </Form.Item>
        <Button type="link" className="fogotPassword info-label">
          忘记密码
        </Button>
        <Form.Item style={{ paddingTop: "1.5rem" }}>
          <Row justify="start">
            <Col span={16}>
              <Button type="primary" shape="round" block htmlType="submit" className="loginBtn boldText">
                登录
              </Button>
            </Col>
            <Col span={7} offset={1}>
              <Button
                shape="round"
                block
                className="registerBtn boldText"
                onClick={onRegister}
              >
                注册
              </Button>
            </Col>
          </Row>
        </Form.Item>
      </Form>
    </div>
  );
};

export default LoginDefaultBox;
