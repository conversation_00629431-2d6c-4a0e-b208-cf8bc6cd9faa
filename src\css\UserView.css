.logo-child32 {
  position: absolute;
  top: 0.13rem;
  left: 18.33rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-white);
  width: 3.82rem;
  height: 25.7rem;
  transform: rotate(45.67deg);
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child33 {
  top: 0.19rem;
  left: 11.5rem;
  height: 11.47rem;
  transform: rotate(45.67deg);
}
.logo-child33,
.logo-child34,
.logo-child35,
.logo-child36 {
  position: absolute;
  border-radius: var(--br-26xl);
  background-color: var(--color-white);
  width: 3.82rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child34 {
  top: 0;
  left: 4.91rem;
  height: 7.03rem;
  transform: rotate(45.67deg);
}
.logo-child35,
.logo-child36 {
  top: 21.18rem;
  left: 9.88rem;
  height: 11.47rem;
  transform: rotate(-134.33deg);
}
.logo-child36 {
  top: 21.38rem;
  left: 16.47rem;
  height: 7.03rem;
}
.logo7,
.logo8,
.logo9 {
  position: absolute;
  top: -6.25rem;
  left: 35.69rem;
  width: 21.38rem;
  height: 21.38rem;
  opacity: 0.7;
}
.logo8,
.logo9 {
  left: 13.31rem;
}
.logo9 {
  left: -9.06rem;
}
.div18,
.div19 {
  /* position: absolute;
  top: 18.19rem;
  left: 23.94rem; */
  color: var(--color-mediumaquamarine-300);
}
.div19 {
  /* top: 15.69rem;
  left: 22.19rem; */
}

.info-item-div {
  /* position: absolute;
  top: 18rem;
  left: 4.88rem; */
  display: inline-flex;
  justify-content: space-between;
  width: 21.81rem;
  height: 2.5rem;
  align-items: center;
}
.b6 {
  position: absolute;
  top: 2rem;
  left: 2.5rem;
  font-size: var(--font-size-base);
}
.frame-child10 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  background: linear-gradient(-19.54deg, #0fb698 16.48%, #113932 82.81%, #111);
  width: 5.5rem;
  height: 5.5rem;
}
.e3 {
  position: absolute;
  top: 1.66rem;
  left: 2.18rem;
  font-weight: 900;
  display: inline-block;
  width: 1.15rem;
  height: 2.18rem;
}
.ellipse-parent6 {
  position: absolute;
  top: 4.69rem;
  left: 5.63rem;
  width: 5.5rem;
  height: 5.5rem;
  font-size: var(--font-size-13xl);
  color: var(--color-white);
}
.logo-child47 {
  position: absolute;
  top: 0.13rem;
  left: 18.33rem;
  border-radius: var(--br-26xl);
  background: linear-gradient(180deg, #0fb698, rgba(15, 182, 152, 0.09));
  width: 3.82rem;
  height: 25.7rem;
  transform: rotate(45.67deg);
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child48 {
  top: 0.19rem;
  left: 11.5rem;
  height: 11.47rem;
  transform: rotate(45.67deg);
}
.logo-child48,
.logo-child49,
.logo-child50,
.logo-child51 {
  position: absolute;
  border-radius: var(--br-26xl);
  background: linear-gradient(180deg, #0fb698, rgba(15, 182, 152, 0.09));
  width: 3.82rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child49 {
  top: 0;
  left: 4.91rem;
  height: 7.03rem;
  transform: rotate(45.67deg);
}
.logo-child50,
.logo-child51 {
  top: 21.19rem;
  left: 9.67rem;
  height: 11.47rem;
  transform: rotate(-134.33deg);
}
.logo-child51 {
  top: 21.38rem;
  left: 16.47rem;
  height: 7.03rem;
}
.logo10 {
  position: absolute;
  top: -6.25rem;
  left: 58.06rem;
  width: 21.38rem;
  height: 21.38rem;
}
.vector-icon2 {
  position: relative;
  width: 0.53rem;
  height: 0.53rem;
}
.bxs-pencil-1-item {
  position: relative;
  border-radius: var(--br-lgi);
  background-color: var(--color-lightslategray-200);
  width: 0.6rem;
  height: 0.08rem;
}
.bxs-pencil-11 {
  width: 0.75rem;
  height: 0.75rem;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  padding: 0.04rem 0.07rem 0.07rem 0.08rem;
  box-sizing: border-box;
  align-items: flex-start;
  justify-content: flex-end;
}
.bxs-pencil-1-container,
.div21 {
  position: absolute;
  background-color: var(--color-white);
}
.bxs-pencil-1-container {
  height: 6.76%;
  width: 1.64%;
  top: -25.07%;
  right: 103.42%;
  bottom: 118.31%;
  left: -5.05%;
  border-radius: var(--br-5xl);
  box-shadow: 0 4px 4px rgba(119, 146, 185, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.div21 {
  top: 11.63rem;
  left: 4.88rem;
  border-radius: var(--br-5xs);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  width: 21.81rem;
  height: 2.5rem;
}
.eliohan1,
.group-div {
  position: absolute;
  top: 12.38rem;
  left: 5.63rem;
}
.group-div {
  top: 6.25rem;
  left: 14.25rem;
  width: 91.5rem;
  height: 22.19rem;
}
.group-child4,
.group-child5 {
  position: absolute;
  border: 1px solid var(--color-white);
  box-sizing: border-box;
}
.group-child4 {
  top: 4.13rem;
  left: 9rem;
  border-radius: var(--br-5xs);
  background: linear-gradient(180deg, #fff, #fefdf5);
  width: 18.19rem;
  height: 4.25rem;
}
.group-child5 {
  top: 5.06rem;
  left: 0;
  border-radius: var(--br-5xl);
  background: linear-gradient(163.44deg, #f9fdff, #deedf5);
  box-shadow: 0-4px 16px rgba(143, 171, 191, 0.2);
  width: 29.69rem;
  height: 16.13rem;
}
.div22,
.group-child6 {
  position: absolute;
  top: 7.06rem;
  left: 2.5rem;
}
.group-child6 {
  top: 7.64rem;
  left: 11.63rem;
  border-radius: var(--br-12xs);
  width: 0.59rem;
  height: 0.97rem;
}
.b7 {
  position: absolute;
  top: 2rem;
  left: 2.5rem;
  font-size: var(--font-size-base);
  color: var(--color-black);
}
.div23,
.rectangle-parent1 {
  position: absolute;
  color: var(--color-gray-400);
}
.rectangle-parent1 {
  top: 29.69rem;
  left: 76.06rem;
  width: 29.69rem;
  height: 21.19rem;
  font-size: var(--font-size-13xl);
  cursor: pointer;
}
.div23 {
  top: 2.5rem;
  left: 14.5rem;
  font-size: var(--font-size-9xl);
  line-height: 2.25rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 24px;
}
.bxs-right-arrow-alt-1-icon1 {
  position: absolute;
  top: 43.38rem;
  left: 98.75rem;
  width: 4.5rem;
  height: 4.5rem;
  overflow: hidden;
  opacity: 0.4;
}
.div24,
.frame-icon {
  /* position: absolute;
  top: 14.94rem;
  left: 4.88rem; */
  display: inline-flex;
  justify-content: space-between;
  width: 21.81rem;
  height: 2.5rem;
  align-items: center;
}
.frame-icon {
  top: 18.5rem;
  left: 38.63rem;
  width: 1.31rem;
  height: 1.25rem;
}
.frame-child11 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  background-color: var(--color-gainsboro-100);
  width: 1rem;
  height: 1rem;
}
.frame-child12,
.frame-child13 {
  position: absolute;
  top: 0.28rem;
  left: 0.63rem;
  background-color: var(--color-white);
  width: 0.13rem;
  height: 0.5rem;
  transform: rotate(45deg);
  transform-origin: 0 0;
}
.frame-child13 {
  top: 0.37rem;
  left: 0.28rem;
  transform: rotate(-45deg);
}
.ellipse-parent7 {
  position: absolute;
  top: 18.63rem;
  left: 36.88rem;
  width: 1rem;
  height: 1rem;
}
.div17 {
  position: relative;
  background-color: var(--color-white);
  width: 100%;
  height: 67.5rem;
  overflow: hidden;
  text-align: left;
  font-size: var(--font-size-sm);
  color: var(--color-black);
}

.info-div {
  top: 11.63rem;
  left: 4.88rem;
  position: absolute;
  display: flex;
  flex-direction: column;
}

.info-username {
  font-size: 22px;
  font-style: normal;
  font-weight: 700;
}