import { But<PERSON>, Col, Form, Input, Row } from "antd";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Dispatch, useState } from "react";
import UserAgreement from "../components/UserAgreement";
import { useDispatch } from "react-redux";
import SendMessageBtn from "../components/SendMessageBtn";
import WelcomePage from "../components/WelcomePage";
import { RegisterParams, VerifyCodeTypeEnum } from "../types";
import { registerUser } from "../store/auth/action";
import Scrollbar from "react-scrollbars-custom";

interface RegisterFieldType extends RegisterParams {
  confimPassword: string;
}
const RegisterView: React.FC = () => {
  const dispath: Dispatch<any> = useDispatch();
  const [account, setAccount] = useState("");
  const [userName, setUserName] = useState("");
  const [password, setPassword] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [isAgree, setIsAgree] = useState(false);

  // 获取查询参数
  const navigate = useNavigate();
  // 获取查询参数
  const [search] = useSearchParams();
  //获取到查询参数redirect 也就是 http://localhost:3000/#?redirect=/admin/abc
  // 上面地址的 /admin/abc
  const path = search.getAll("redirect")[0];
  const redirect = !path || path === '/login' || path === '/register' || path === '/' ? "/overview" : path;
  // 定义回调方法
  const callback = () => {
    navigate(redirect);
  };
  // 上面地址的 /admin/abc
  const handleIsAgreeChange = (newValue: boolean) => {
    setIsAgree(newValue);
  };

  const onFinish = (values: any) => {
    // 执行dispath action传入用户名和密码
    dispath(
      registerUser(
        {
          userName,
          account,
          password,
          verificationCode,
          registerType: "PHONE_NUMBER",
        },
        callback
      )
    );
  };

  // 验证失败
  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };
  return (
    <Scrollbar style={{ position: "inherit" }}>
      <div className="bg">
        <Row justify="start">
          <Col span={12} className="loginLf">
            <WelcomePage></WelcomePage>
          </Col>
          <Col span={12} className="loginRt">
            <div className="loginBox">
              <h2 className="loginTitle">
                <span className="primary boldText">行至账号-注冊</span>
              </h2>
              <Form
                className="registerForm"
                requiredMark={false}
                name="loginDefault"
                style={{ maxWidth: 600 }}
                initialValues={{
                  remember: true,
                  username: "",
                  phoneNum: "",
                  password: "",
                }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
              >
                <Form.Item<RegisterFieldType>
                  label="用户名"
                  name="userName"
                  rules={[{ required: true, message: "请输入用户名" }]}
                >
                  <Input
                    placeholder="请输入用户名"
                    className="loginInput"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                  />
                </Form.Item>
                <Form.Item<RegisterFieldType>
                  label="手机号"
                  name="account"
                  rules={[
                    { required: true, message: "请输入手机号" },
                    {
                      pattern: /^1[3456789]\d{9}$/,
                      message: "手机号格式不正确",
                    },
                  ]}
                >
                  <Input
                    className="loginInput"
                    placeholder="请输入手机号"
                    value={account}
                    onChange={(e) => setAccount(e.target.value)}
                  />
                </Form.Item>
                <Row justify="start" gutter={8}>
                  <Col span={17}>
                    <Form.Item<RegisterFieldType>
                      label="验证码"
                      name="verificationCode"
                      rules={[{ required: true, message: "请输入短信验证码" }]}
                    >
                      <Input
                        className="loginVerifyCodeInput"
                        placeholder="请输入短信验证码"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7}>
                    <SendMessageBtn
                      disabled={false}
                      params={{ account, registerType: "PHONE_NUMBER" }}
                      type={VerifyCodeTypeEnum.Register}
                    ></SendMessageBtn>
                  </Col>
                </Row>
                <Form.Item<RegisterFieldType>
                  label="密码"
                  name="password"
                  rules={[{ required: true, message: "请输入密码" }]}
                >
                  <Input.Password
                    className="loginInput"
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </Form.Item>
                <Form.Item<RegisterFieldType>
                  label="确认密码"
                  name="confimPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: "请输入密码" },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue("password") === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error("输入的密码不匹配!"));
                      },
                    }),
                  ]}
                >
                  <Input.Password placeholder="请输入密码" className="loginInput" />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" shape="round" block htmlType="submit" className="verifyloginBtn loginBtn">
                    注册并登录
                  </Button>
                </Form.Item>
              </Form>
              <UserAgreement
                isAgree={isAgree}
                onIsAgreeChange={handleIsAgreeChange}
              ></UserAgreement>
            </div>
          </Col>
        </Row>
      </div>
    </Scrollbar>
  );
};

export default RegisterView;
