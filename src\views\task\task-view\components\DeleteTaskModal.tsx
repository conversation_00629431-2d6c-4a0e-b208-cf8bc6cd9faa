
import React from "react";
import { <PERSON><PERSON>, But<PERSON>, message } from "antd";
import { ExclamationCircleFilled } from "@ant-design/icons";

export interface DeleteTaskModalProps {
    visible: boolean;
    onCancel:React.Dispatch<React.SetStateAction<boolean>>;
    onConfirm:React.Dispatch<React.SetStateAction<boolean>>;
    selectedRows: TaskType[];
}
const DeleteTaskModal: React.FC<DeleteTaskModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  selectedRows,
}) => {
  return (
    <Modal
      centered
      title="删除提示"
    //   icon={<ExclamationCircleFilled />}
      width={540}
      open={visible}
    //   onCancel={}
    //   footer={[
    //     <Button key="cancel" type="text" onClick={onCancel} shape="round">
    //       取消
    //     </Button>,
    //     <Button
    //       key="confirm"
    //       type="primary"
    //       onClick={onConfirm}
    //       shape="round"
    //       className="primary-btn"
    //       style={{ width: "120px" }}
    //     >
    //       确认删除
    //     </Button>,
    //   ]}
    >
      <div className="default-info" style={{ color: "black" }}>
        确定要删除所选任务吗？
      </div>
    </Modal>
  );
};

export default DeleteTaskModal;