.group-child32 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-5xl);
  /* background: linear-gradient(180deg, #111, #5f6d82); */
  width: 29.69rem;
  height: 21.19rem;
  background-image: url('../assets/img/userinfo-account.png');
  background-size: cover;
}
.vector-icon5 {
  position: absolute;
  height: 117.4%;
  width: 58.74%;
  top: 0;
  right: 3.58%;
  bottom: -17.4%;
  left: 37.68%;
  border-radius: var(--br-11xs);
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0.4;
}
.b79,
.div694 {
  position: absolute;
  top: 12.69rem;
  left: 13.94rem;
}
.b79 {
  top: 7.25rem;
  /* left: 11.19rem; */
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--font-size-45xl);
}
.div695 {
  top: 18.31rem;
  left: 9.69rem;
  text-decoration: underline;
  color: var(--color-lightsteelblue-100);
  cursor: pointer;
}
.b80,
.div695,
.div696 {
  position: absolute;
}
.div696 {
  top: 18.31rem;
  left: 14.69rem;
  text-decoration: underline;
  color: var(--color-lightsteelblue-100);
  cursor: pointer;
}
.b80 {
  top: 2rem;
  left: 2.5rem;
  font-size: var(--font-size-base);
}
.rectangle-parent67 {
  position: absolute;
  top: 29.69rem;
  left: 14.25rem;
  width: 29.69rem;
  height: 21.19rem;
  text-align: left;
  font-size: var(--font-size-sm);
  color: var(--color-white);
}
