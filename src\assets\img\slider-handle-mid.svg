<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_343_576)">
<circle cx="15" cy="13" r="9" fill="#EBA900"/>
<circle cx="15" cy="13" r="8.5" stroke="#F6C548"/>
</g>
<rect x="11" y="10.5" width="8" height="1" fill="#F6C548"/>
<rect x="11" y="12.5" width="8" height="1" fill="#F6C548"/>
<rect x="11" y="14.5" width="8" height="1" fill="#F6C548"/>
<defs>
<filter id="filter0_d_343_576" x="0" y="0" width="30" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.964706 0 0 0 0 0.772549 0 0 0 0 0.282353 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_343_576"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_343_576" result="shape"/>
</filter>
</defs>
</svg>
