// src/utils/modelDescriptions.ts
import React from 'react';
import { DescriptionsProps } from 'antd';
import { ModelDetailType } from '@/types';
import { DescriptionsItemType } from 'antd/es/descriptions';
import { Progress } from 'antd'; // 确保导入了Progress组件

// 模型状态映射（复用组件中的逻辑）
export const modelStatusChange = (status: number): string => {
  switch (status) {
    case 7:
      return '在线';
    case 6:
      return '上线中';
    case 5:
      return '离线';
    case 4:
      return '停止训练';
    case 2:
      return '训练中';
    case 3:
      return '训练失败';
    case 8:
      return '上线失败';
    default:
      return '';
  }
};

// 学习率格式化（复用组件中的逻辑）
export const formatLearnRatio = (num: number): string => {
  const ratioMap: Record<number, string> = {
    0.00001: '1e-5',
    0.00005: '5e-5',
    0.0001: '1e-4',
    0.0005: '5e-4',
    0.001: '1e-3',
  };
  return ratioMap[num] || '-';
};

// 基础模型描述项
export const getDescItems = (
  modelDetail: ModelDetailType,
  modelServerDetail: any
): DescriptionsProps['items'] => [
  { key: '1', label: '模型ID', children: modelDetail.id },
  {
    key: '2',
    label: '模型类型',
    children: modelDetail.category === 0 ? '基座模型' : '微调模型',
  },
  { key: '3', label: '参数量', children: modelDetail.parameter },
  { key: '4', label: '模型介绍', children: modelDetail.introduction },
  { key: '5', label: '适用场景推荐', children: modelDetail.scene },
  { key: '6', label: '服务器名称', children: modelServerDetail.serverName },
];

// 训练详情描述项
export const getTrainItems = (
  modelDetail: ModelDetailType,
  tarinDetial: any,
  getModelName: (id: string) => string
): DescriptionsItemType[] => [
  { key: '1', label: '模型ID', children: modelDetail.id },
  { key: '2', label: '模型介绍', children: modelDetail.introduction },
  { key: '3', label: '数据集', children: tarinDetial?.datasets || '-' },
  {
    key: '4',
    label: '数据集比例',
    children: tarinDetial?.datasetRadio ? `${tarinDetial.datasetRadio * 100}%` : '-',
  },
  {
    key: '5',
    label: '基座模型',
    children: getModelName(tarinDetial?.modelBaseId) || '-',
  },
  { key: '6', label: '训练策略', children: tarinDetial?.trainStrategy || '-' },
  { key: '7', label: '迭代次数', children: tarinDetial?.interationNumber || '-' },
  { key: '8', label: '批次大小', children: tarinDetial?.batchSize || '-' },
  {
    key: '9',
    label: '学习率',
    children: formatLearnRatio(tarinDetial?.learnRate) || '-',
  },
  {
    key: '10',
    label: React.createElement(
      'span',
      { style: { color: '#000000', fontWeight: 'bold' } },
      '模型状态'
    ),
    children: modelStatusChange(tarinDetial?.status) || '-',
  },
  {
    key: '11',
    label: React.createElement(
      'span',
      { style: { color: '#000000', fontWeight: 'bold' } },
      '训练进度'
    ),
    children: tarinDetial?.trainProgress
      ? React.createElement(Progress, { percent: Number(tarinDetial.trainProgress), type: 'line' })
      : '-',
  },
];

// 中断详情描述项
export const getInterruptItems = (
  modelDetail: ModelDetailType,
  tarinDetial: any,
  getModelName: (id: string) => string
): DescriptionsProps['items'] => [
  { key: '1', label: '模型ID', children: modelDetail.id },
  { key: '2', label: '模型介绍', children: modelDetail.introduction || '-' },
  { key: '3', label: '数据集', children: tarinDetial?.datasets || '-' },
  {
    key: '4',
    label: '数据集比例',
    children: tarinDetial?.datasetRadio ? `${tarinDetial.datasetRadio * 100}%` : '-',
  },
  {
    key: '5',
    label: '基座模型',
    children: getModelName(tarinDetial?.modelBaseId) || '-',
  },
  { key: '6', label: '训练策略', children: tarinDetial?.trainStrategy || '-' },
  { key: '7', label: '迭代次数', children: tarinDetial?.interationNumber || '-' },
  { key: '8', label: '批次大小', children: tarinDetial?.batchSize || '-' },
  {
    key: '9',
    label: '学习率',
    children: formatLearnRatio(tarinDetial?.learnRate) || '-',
  },
  {
    key: '10',
    label: React.createElement(
      'span',
      { style: { color: '#000000', fontWeight: 'bold' } },
      '模型状态'
    ),
    children: modelStatusChange(tarinDetial?.status) || '-',
  },
  {
    key: '11',
    label: React.createElement(
      'span',
      { style: { color: '#000000', fontWeight: 'bold' } },
      '中断原因'
    ),
    children: tarinDetial?.stopReason || '-',
  },
  {
    key: '12',
    label: React.createElement(
      'span',
      { style: { color: '#000000', fontWeight: 'bold' } },
      '训练进度'
    ),
    children: tarinDetial?.trainProgress
      ? React.createElement(Progress, { percent: Number(tarinDetial.trainProgress), type: 'line' })
      : '-',
  },
];
