import React, { useState, useRef, useEffect, useCallback } from 'react';
import { getDataSets, changeDataSetsTags, renameDataSets } from '@/api/dataset';
import { DataSetStatus, DataSetType } from '@/types';
import { debounce } from '@/utils/utils';
import { DatasetTableHeaderProps } from '../DatasetTableHeader';
import { DatasetTableContentProps } from '../DatasetTableContent';
import { DatasetTableProps } from '../DatasetTable';
import { ColumnsType } from 'antd/es/table';
import { Button, Space, message } from 'antd';
import TagList from '../../TagList';
import RelatedTasksDropdown from '../../RelatedTasksDropdown';
import DatasetRenameInput from '../DatasetRenameInput';

export type UseDatasetTableReturn = {
  headerProps: any;
  tableProps: any;
  paginationProps: any;
  uploadErrorModalProps: any;
  previewFolderModalProps: any;
  imperativeMethods: {
    onRefresh: () => void;
    handleDeleteRows: (idList: string[]) => void;
    removeSelection: (datasetId: string) => void;
  };
};

export default function useDatasetTable(props: DatasetTableProps): UseDatasetTableReturn {
  // 1. 状态定义
  const [filterAttribute, setFilterAttribute] = useState<string>('datasetName');
  const [sortDirection, setSortDirection] = useState<string>('desc');
  const [sortAttribute, setSortAttribute] = useState<string>('createTime');
  const [filterInput, setFilterInput] = useState<string>('');
  const [debouncedInput, setDebouncedInput] = useState('');
  const [pagination, setPagination] = useState({ page: 1, size: 10, total: 0 });
  const [datasets, setDatasets] = useState<DataSetType[]>([]);
  const [apiFlg, setApiFlg] = useState(false);
  const [renameRows, setRenameRows] = useState<Set<string>>(new Set<string>());
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  const [previewModal, setPreviewModal] = useState<boolean>(false);
  const [selectDataSet, setSelectDataSet] = useState<DataSetType | null>(null);
  // 选中状态
  const [globalSelectedRows, setGlobalSelectedRows] = useState<Map<string, DataSetType>>(new Map());
  const [globalSelectedRowKeys, setGlobalSelectedRowKeys] = useState<Set<string>>(new Set());
  const [selectedRows, setSelectedRows] = useState<DataSetType[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [tableIndex, setTableIndex] = useState(0);

  // 2. 搜索防抖
  const debouncedSetFilterInput = useRef(
    debounce((value: string) => {
      setDebouncedInput(value);
    }, 300)
  ).current;
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFilterInput(value);
    debouncedSetFilterInput(value);
  };

  // 3. 数据拉取
  const fetchData = useCallback(async () => {
    setApiFlg(true);
    const params = {
      page: pagination.page,
      size: pagination.size,
      filterAttribute,
      sortAttribute,
      sortDirection,
      datasetName: filterAttribute === 'datasetName' ? filterInput : '',
      tag: filterAttribute !== 'datasetName' ? filterInput : '',
      startTime: '',
      endTime: '',
    };
    const res = await getDataSets(params);
    setApiFlg(false);
    if (res.data?.code === 200) {
      setDatasets(res.data?.data);
      setPagination((p) => ({ ...p, total: res.data?.totalCount }));
      setTableIndex((pagination.page - 1) * pagination.size);
    }
  }, [
    pagination.page,
    pagination.size,
    filterAttribute,
    sortAttribute,
    sortDirection,
    filterInput,
  ]);

  useEffect(() => {
    fetchData();
  }, [
    pagination.page,
    pagination.size,
    sortDirection,
    filterAttribute,
    sortAttribute,
    debouncedInput,
  ]);

  // 4. 选中状态同步
  useEffect(() => {
    if (datasets.length > 0) {
      const currentPageSelectedRows: DataSetType[] = [];
      const currentPageSelectedKeys: string[] = [];
      datasets.forEach((dataset) => {
        if (globalSelectedRowKeys.has(dataset.id)) {
          currentPageSelectedRows.push(dataset);
          currentPageSelectedKeys.push(dataset.id);
        }
      });
      setSelectedRows(currentPageSelectedRows);
      setSelectedRowKeys(currentPageSelectedKeys);
    }
  }, [datasets, globalSelectedRowKeys]);

  const columns: ColumnsType<DataSetType> = [
    {
      key: 'index',
      title: '序号',
      dataIndex: 'index',
      render: (_, __, index) => tableIndex + index + 1,
      width: '4.6%',
    },
    {
      key: 'name',
      title: '数据集名称',
      dataIndex: 'datasetName',
      ellipsis: true,
      shouldCellUpdate: () => true,
      render: (_, dataSet, index) => {
        const { id: dataSetId, datasetName } = dataSet;
        if (!renameRows.has(dataSetId)) {
          return (
            <a
              className="dataset-name"
              onClick={(e) => {
                e.preventDefault();
                setSelectDataSet({ ...dataSet });
                if (!previewModal) setPreviewModal(true);
              }}
            >
              {datasetName}
            </a>
          );
        } else {
          return (
            <Space.Compact>
              <DatasetRenameInput
                dataSet={dataSet}
                datasets={datasets}
                index={index}
                renameRows={renameRows}
                setRenameRows={setRenameRows}
                setDatasets={setDatasets}
                handleRefresh={fetchData}
              />
            </Space.Compact>
          );
        }
      },
      width: props.ShowActionColumn ? '14.2%' : '',
    },
    {
      key: 'createTime',
      title: '上传时间',
      dataIndex: 'createTime',
      width: props.ShowActionColumn ? '14.6%' : '',
    },
    {
      key: 'datasetStatus',
      title: '解析状态',
      dataIndex: 'datasetStatus',
      render: (_, { datasetStatus, total, complete, failedReason }) => {
        if (datasetStatus === DataSetStatus.sucess) {
          return (
            <label className={!failedReason ? undefined : 'warning-info'}>
              解析完成 ({`${complete}/${total}`})
            </label>
          );
        } else if (datasetStatus === DataSetStatus.failed) {
          return <label className="error-info">解析失败</label>;
        } else if (datasetStatus === DataSetStatus.inProgress) {
          if (complete === 0 && total === 0) {
            return <label>待解析</label>;
          } else {
            return <label>解析中 ({`${complete}/${total}`})</label>;
          }
        }
        return null;
      },
      width: props.ShowActionColumn ? '9.6%' : '',
    },
    {
      key: 'tags',
      title: '标签',
      ellipsis: true,
      dataIndex: 'tags',
      render: (_, rowData) => {
        const { tags, id: dataSetId } = rowData;
        return (
          <TagList
            tagList={tags}
            dataSetId={dataSetId}
            onChange={(updateTags) => {
              const updateDataSets = datasets.map((dataSet) =>
                dataSet.id === dataSetId ? { ...dataSet, tags: updateTags } : dataSet
              );
              setDatasets(updateDataSets);
              changeDataSetsTags({ dataSetId, tags: updateTags }).then(
                () => {
                  fetchData();
                },
                (error) => {
                  message.error(error);
                }
              );
            }}
            showAddBtn={props.noEditTag ? false : true}
            isClose={props.noEditTag ? false : true}
            OnEdit={() => {}}
          />
        );
      },
      shouldCellUpdate: () => true,
      width: props.ShowActionColumn ? '18.2%' : '',
    },
    {
      key: 'sources',
      title: '来源',
      ellipsis: true,
      dataIndex: 'sources',
      width: '10%',
      render: (_, dataSet) => {
        const { id: dataSetId } = dataSet;
        return !renameRows.has(dataSetId) ? <div>本地上传</div> : <div>GDP</div>;
      },
    },
    {
      key: 'action',
      title: '操作',
      dataIndex: 'datasetStatus',
      render: (_, dataSet) => {
        const { datasetStatus, failedReason } = dataSet;
        if (datasetStatus === DataSetStatus.sucess && !failedReason) {
          return (
            <Button
              type="link"
              className="grid-link-btn"
              onClick={() => {
                setSelectDataSet({ ...dataSet });
                if (!previewModal) setPreviewModal(true);
              }}
            >
              详情
            </Button>
          );
        } else if (datasetStatus === DataSetStatus.sucess && failedReason) {
          return (
            <Button
              type="link"
              className="grid-link-btn"
              onClick={() => {
                setUploadErrorModal(true);
              }}
            >
              问题详情
            </Button>
          );
        } else if (datasetStatus === DataSetStatus.failed) {
          return (
            <Button
              type="link"
              className="grid-link-btn"
              disabled={true}
              onClick={() => {
                message.warning('暂不支持此功能，更多优化尽请期待');
              }}
            >
              无法解析
            </Button>
          );
        } else if (datasetStatus === DataSetStatus.inProgress) {
          return null;
        }
        return null;
      },
      width: '7.8%',
    },
    {
      key: 'dropdown',
      title: ' ',
      dataIndex: 'dropdown',
      ellipsis: true,
      render: (_, rowData, index) => {
        const { tags, id: dataSetId } = rowData;
        return (
          <RelatedTasksDropdown
            key={index}
            tags={tags}
            rowData={rowData}
            OnEdit={() => {}}
            OnChange={(updateTags) => {
              const updateDataSets = datasets.map((dataSet) =>
                dataSet.id === dataSetId ? { ...dataSet, tags: updateTags } : dataSet
              );
              setDatasets(updateDataSets);
              fetchData();
            }}
            OnDelete={async (idList: string[]) => {
              setApiFlg(true);
              imperativeMethods.handleDeleteRows(idList);
              setTimeout(() => {
                fetchData();
                setApiFlg(false);
              }, 800);
              return () => {};
            }}
            OnRename={() => {
              const updateRenameSets = new Set(renameRows);
              updateRenameSets.add(dataSetId);
              setRenameRows(updateRenameSets);
            }}
          />
        );
      },
      width: '16%',
    },
  ];

  const imperativeMethods = {
    onRefresh: fetchData,
    handleDeleteRows: (idList: string[]) => {
      setDatasets((pre) => pre.filter((item) => !idList.includes(item.id)));
    },
    removeSelection: (datasetId: string) => {
      const updatedSelectedRows = selectedRows.filter((row) => row.id !== datasetId);
      const updatedSelectedRowKeys = selectedRowKeys.filter((key) => key !== datasetId);
      setSelectedRows(updatedSelectedRows);
      setSelectedRowKeys(updatedSelectedRowKeys);
      const newGlobalSelectedRows = new Map(globalSelectedRows);
      const newGlobalSelectedKeys = new Set(globalSelectedRowKeys);
      newGlobalSelectedRows.delete(datasetId);
      newGlobalSelectedKeys.delete(datasetId);
      setGlobalSelectedRows(newGlobalSelectedRows);
      setGlobalSelectedRowKeys(newGlobalSelectedKeys);
    },
  };

  const headerProps: DatasetTableHeaderProps = {
    filterAttribute,
    setFilterAttribute,
    filterInput,
    setFilterInput,
    sortDirection,
    setSortDirection,
    Extra: props.Extra,
    handleChange,
  };
  const tableProps: Partial<DatasetTableContentProps> = {
    columns,
    datasets,
    rowSelection: {
      columnWidth: '32px',
      selectedRowKeys: selectedRowKeys,
      onChange: (newSelectedRowKeys: string[], rows: DataSetType[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(rows);
        const newGlobalSelectedRows = new Map(globalSelectedRows);
        const newGlobalSelectedKeys = new Set(globalSelectedRowKeys);
        datasets.forEach((dataset) => {
          newGlobalSelectedRows.delete(dataset.id);
          newGlobalSelectedKeys.delete(dataset.id);
        });
        rows.forEach((row) => {
          newGlobalSelectedRows.set(row.id, row);
          newGlobalSelectedKeys.add(row.id);
        });
        setGlobalSelectedRows(newGlobalSelectedRows);
        setGlobalSelectedRowKeys(newGlobalSelectedKeys);
        const allSelectedRows = Array.from(newGlobalSelectedRows.values());
        if (props.onSelectRows) props.onSelectRows(allSelectedRows);
      },
      getCheckboxProps: (record: DataSetType) => ({
        name: record.name,
        disabled: record.datasetStatus !== DataSetStatus.sucess,
      }),
    },
    loading: apiFlg,
    apiFlg,
    tableIndex,
    renameRows,
    setRenameRows,
    setDatasets,
    handleRefresh: fetchData,
    onSelectRows: props.onSelectRows,
    previewModal,
    setPreviewModal,
    selectDataSet,
    setSelectDataSet,
    ShowActionColumn: props.ShowActionColumn,
    Extra: props.Extra,
  };
  const paginationProps = {
    total: pagination.total,
    pageSize: pagination.size,
    page: pagination.page,
    OnChange: (page: number, pageSize: number) => {
      setPagination((prev) => ({ ...prev, page, size: pageSize }));
    },
  };
  const uploadErrorModalProps = {
    visible: uploadErrorModal,
    OnClose: () => setUploadErrorModal(false),
  };
  const previewFolderModalProps = {
    visible: previewModal,
    dataSet: selectDataSet,
    OnClose: () => setPreviewModal(false),
  };

  return {
    headerProps,
    tableProps,
    paginationProps,
    uploadErrorModalProps,
    previewFolderModalProps,
    imperativeMethods,
  };
}
