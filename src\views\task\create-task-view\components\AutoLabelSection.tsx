import React, { ChangeEvent } from 'react';
import { Switch, Input, Button } from 'antd';
import { DatasetFile } from '../types';
import { LABELS, BUTTON_TEXTS, PLACEHOLDERS } from '../constants';

const { TextArea } = Input;

interface AutoLabelSectionProps {
  autoLabel: boolean;
  labelText: string;
  isGeneratingLabels: boolean;
  fileList: DatasetFile[];
  onAutoLabelChange: (checked: boolean) => void;
  onLabelTextChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  onAIGenerate: () => void;
}

const AutoLabelSection: React.FC<AutoLabelSectionProps> = ({
  autoLabel,
  labelText,
  isGeneratingLabels,
  fileList,
  onAutoLabelChange,
  onLabelTextChange,
  onAIGenerate,
}) => {
  return (
    <>
      <div className="auto-label">
        <span style={{ marginRight: '15px' }}>{LABELS.AUTO_LABEL}</span>
        <Switch size="small" onChange={onAutoLabelChange} />
      </div>
      {autoLabel && (
        <div style={{ marginTop: '16px' }}>
          <div style={{ position: 'relative' }}>
            <TextArea
              value={labelText}
              onChange={onLabelTextChange}
              autoSize={{ minRows: 2, maxRows: 6 }}
              onPressEnter={(e: React.KeyboardEvent<HTMLTextAreaElement>) =>
                e.preventDefault()
              } // 阻止回车换行
              placeholder={PLACEHOLDERS.LABEL_INPUT}
              className="label-textarea"
            />
            <div className="label-textarea-footer">
              <div
                style={{
                  marginTop: '8px',
                  fontSize: '14px',
                  color: '#666',
                  lineHeight: '1.4',
                }}
              >
                请输入分类标签，以逗号分隔。示例:飞机，电磁，坦克..……，请勿使用特殊符号
              </div>
              <Button
                type="primary"
                loading={isGeneratingLabels}
                onClick={onAIGenerate}
                className="createAiBtn"
              >
                {BUTTON_TEXTS.AI_GENERATE}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AutoLabelSection;
