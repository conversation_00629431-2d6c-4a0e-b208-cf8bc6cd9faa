import { useState, useCallback, useMemo, useRef } from 'react';
import type { MenuProps } from 'antd';
import { debounce } from '../../../../utils/utils';

export const useFilterManagement = () => {
  const [filterType, setFilterType] = useState<string>('keyWord');
  const [selectText, setSelectText] = useState('关键词检索');
  const [selectReview, setSelectReview] = useState('');
  const [filterVal, setFilterVal] = useState<string>();
  const [debouncedFilterVal, setDebouncedFilterVal] = useState<string>();
  const [searchTagValue, setSearchTagValue] = useState('');
  const [reviewFilterType, setReviewFilterType] = useState<any>();
  const [approvedItems, setApprovedItems] = useState<any>();
  const [allocatedUser, setAllocatedUser] = useState<string>();
  const [scoreFilter, setScoreFilter] = useState<string>();
  const [open, setOpen] = useState(false);

  const debouncedSetFilterInput = useRef(
    debounce((value: string) => {
      setDebouncedFilterVal(value);
    }, 300)
  ).current;
  // 创建防抖的setFilterVal函数
  const debouncedSetFilterVal = useMemo(
    () => (value: string) => {
      setFilterVal(value);
      debouncedSetFilterInput(value);
    },
    []
  );

  const initSelect = useCallback(() => {
    setReviewFilterType(undefined);
    setApprovedItems(undefined);
    setAllocatedUser('');
    setScoreFilter('');
    setSelectReview('');
  }, []);

  const handleFilter: MenuProps['onClick'] = useCallback(
    (e: any) => {
      if (e.key === 'keyWord') {
        setFilterType('keyWord');
        setSelectText('关键词检索');
        initSelect();
      } else if (e.key === 'reviewedSearch') {
        setFilterType('reviewedSearch');
        setSelectText('审核检索');
      } else if (e.key === 'labelSearch') {
        setFilterType('labelSearch');
        setSelectText('标签检索');
      } else {
        // 处理审核检索的子菜单项
        setFilterType('reviewedSearch');
        setSelectText('审核检索');

        if (e.key.indexOf('verifyUser_') > -1) {
          const id = e.key.split('verifyUser_')[1];
          // 这里需要从外部传入verifyUsers来获取用户信息
          setAllocatedUser(id);
          setScoreFilter('');
          setReviewFilterType(undefined);
          setApprovedItems(undefined);
          // setSelectReview(user[0].label); // 需要从外部获取
        } else if (e.key.indexOf('reviewOption_') > -1) {
          const value = e.key.split('reviewOption_')[1];
          // 这里需要从外部传入reviewOptions来获取选项信息
          setScoreFilter(value);
          setReviewFilterType(undefined);
          setApprovedItems(undefined);
          setAllocatedUser('');
          // setSelectReview(option[0].label); // 需要从外部获取
        } else if (e.key === 'unreviewed') {
          setReviewFilterType(false);
          setApprovedItems(undefined);
          setScoreFilter('');
          setAllocatedUser('');
          setSelectReview('未审核项');
        } else if (e.key === 'reviewed') {
          setReviewFilterType(undefined);
          setApprovedItems(true);
          setScoreFilter('');
          setAllocatedUser('');
          setSelectReview('已审核项');
        }
      }
    },
    [initSelect]
  );

  // 递归搜索菜单项
  const filterMenuItems = useCallback((items: any[], searchValue: string): any[] => {
    if (!searchValue) return items;

    const filtered: any[] = [];

    items.forEach((item) => {
      // 检查当前项是否匹配
      const currentMatches =
        item.label && item.label.toLowerCase().includes(searchValue.toLowerCase());

      // 如果有子项，递归搜索
      let filteredChildren: any[] = [];
      if (item.children && item.children.length > 0) {
        filteredChildren = filterMenuItems(item.children, searchValue);
      }

      // 如果当前项匹配或有匹配的子项，则包含此项
      if (currentMatches || filteredChildren.length > 0) {
        filtered.push({
          ...item,
          children: filteredChildren.length > 0 ? filteredChildren : item.children,
        });
      }
    });

    return filtered;
  }, []);

  return {
    filterType,
    selectText,
    selectReview,
    filterVal,
    debouncedFilterVal,
    searchTagValue,
    reviewFilterType,
    approvedItems,
    allocatedUser,
    scoreFilter,
    open,
    setFilterType,
    setSelectText,
    setSelectReview,
    setFilterVal,
    debouncedSetFilterVal, // 防抖版本的setFilterVal
    setSearchTagValue,
    setReviewFilterType,
    setApprovedItems,
    setAllocatedUser,
    setScoreFilter,
    setOpen,
    initSelect,
    handleFilter,
    filterMenuItems,
  };
};
