import { Button } from "antd";
import { DislikeOutlined, LikeOutlined, CheckCircleOutlined } from "@ant-design/icons";
interface RateButtonProp {
    rate: number;
}
const RateButton: React.FC<RateButtonProp> = ({
    rate
}) => {
    return <>
        <Button type="primary" className="primary-btn boldText" style={{ width: 120 }}><DislikeOutlined />Bad</Button>
        <Button type="primary" className="primary-btn boldText" style={{ width: 120 }}><CheckCircleOutlined />Normal</Button>
        <Button type="primary" className="primary-btn boldText" style={{ width: 120 }}><LikeOutlined />Great</Button>
    </>
}

export default RateButton;