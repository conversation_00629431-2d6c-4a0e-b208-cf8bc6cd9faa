import { GenerateQaType, SearchQaType, SplitDataType } from "../types";
import request from "../utils/request";

/**
 * 文件上传
 * @param file 上传文件
 * @returns
 */

export function uploadData(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("file_type", "txt");
  return request.post("/api/qa_generator/upload_data", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 文本切分
 * @param params 文本切分数据请求参数
 * @returns 
 */
export function splitData(params: SplitDataType) {
  return request.post("/api/qa_generator/split_data", { params });
}

/**
 * 
 * @param params QA生成数据请求参数
 * @returns 
 */
export function generateQa(params: GenerateQaType) {
    return request.post("/api/qa_generator/generate_qa", { params });
}

/**
 * 
 * @param params 查询QA数据请求参数
 * @returns 
 */
export function searchQa(params: SearchQaType) {
    return request.post("/api/qa_generator/search_qa", { params });
}

/**
 * 
 * @returns QA数据列表
 */
export function getTaskList() {
  return request.post("/api/qa_generator/task_list");
}