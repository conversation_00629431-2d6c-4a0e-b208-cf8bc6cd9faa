import React from 'react';
import { Empty, Table } from 'antd';
import { TaskType } from '@/types';
import { TaskTableProps } from '../types';
import { useTaskTableColumns } from './TaskTableColumns';
import emptyLogo from '@/assets/img/empty-logo.svg';

const TaskTable: React.FC<TaskTableProps> = ({
  taskData,
  selectedRowKeys,
  renameList,
  tableIndex,
  onSelectionChange,
  onRename,
  onDelete,
  onApply,
  onProblemDetail,
  onRetry,
  onDetail,
  onVisibleRangeChange,
  getTasksList,
  setRenameList,
}) => {
  const columns = useTaskTableColumns({
    taskData,
    renameList,
    tableIndex,
    onRename,
    onDelete,
    onApply,
    onProblemDetail,
    onRetry,
    onDetail,
    onVisibleRangeChange,
    getTasksList,
    setRenameList,
  });

  const rowSelection = {
    selectedRowKeys: Array.from(selectedRowKeys),
    onChange: onSelectionChange,
    getCheckboxProps: (record: TaskType) => ({
      name: record.taskName,
    }),
  };

  return (
    <Table
      locale={{
        emptyText: (
          <Empty
            image={emptyLogo}
            description={
              <span className="dataset-table-empty-label">空空如也，去上传本地文件吧~</span>
            }
          />
        ),
      }}
      tableLayout="fixed"
      rowKey="taskId"
      className="dataset-table"
      rowSelection={{
        type: 'checkbox',
        ...rowSelection,
      }}
      columns={columns}
      dataSource={taskData}
      pagination={false}
    />
  );
};

export default TaskTable;
