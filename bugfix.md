# 1.@types/js-cookie 下载失效
使用pnpm i --save-dev @types/js-cookie 下载失效
使用 pnpm run install  @types/js-cookie 成功
缺少依赖可单独下载
实际上是运行package.json中的install命令 
--legacy-peer-deps 是一个兼容性工具 ，通过放宽 peerDependency 校验解决安装阻塞问题，但需权衡依赖管理的严谨性与灵活性
使得安装成功

问题分析，项目混合使用yarn，pnpm 等包管理工具，使得安装时会由于.ignored机制导致额外下载的依赖无法正常安装。(node_modules/.ignored 是 pnpm 特有的隔离目录 ，用于存放被检测到由其他包管理器（如 npm/yarn）安装的依赖包)
此处的@types/js-cookie 的依赖包，pnpm 会将其安装到 node_modules\.ignored\@types 目录下，导致安装失败。
实际上下载成功后 package.json 中会多出一行
        "@types/js-cookie": "^3.0.6",
package-lock.json 中也会多出一行
        "@types/js-cookie": "^3.0.6",

希望添加@路径映射，但是查看package.json 可知 项目是基于Create React App（CRA）创建的，因为react-scripts是CRA的核心依赖，负责管理构建配置。
不支持修改路径映射配置。

# 2.使用antd组件样式在index.tsx ConfigProvider进行配置。
具体配置参考官方文档
https://ant-design.antgroup.com/components/slider-cn#%E4%B8%BB%E9%A2%98%E5%8F%98%E9%87%8Fdesign-token
避免组件内联样式


# 3.重构的大致思路
**重构过程** 包括去除重复、简化复杂逻辑和澄清模糊的代码。重构时，需要对代码无情地针砭，以改进其设计。这种改进可能很小，小到只是改变一个变量名；也可能很大，大到合并两个类层次。

**重构最好是持续而不是分阶段地进行**。只要看到代码需要改善，就改善它。但是，如果你的经理要求你完成某项功能，以备明天进行演示之用，那么当然应该先完成这项功能，以后再进行重构。持续重构能够很好地满足业务需求，但是重构实践必须和谐地适应业务上的轻重缓急。
                                                        --------《重构与模式》
## 3.1组件拆分
组件拆分即是重构的方法也是开发的原则，组件拆分的原则是单一职责原则，即一个组件只负责一个功能，这样可以提高组件的复用性，降低组件的耦合性，提高组件的可维护性。

拆分时机：

1. 组件内部出现明显功能分区（如：header/content/footer）
2. 同一组件内存在多个条件分支渲染
3. 出现重复的 JSX 结构模式

可以处理为如下的结构
├── features/
│   ├── user-management/      #具体的功能模块，例如一个页面，一个较复杂的组件。
│   │   ├── components/       # 模块专用组件
│   │   ├── constants/        # 模块专用常量
│   │   ├── utils/            # 模块专用 utils
│   │   ├── hooks/            # 模块专用 hooks
│   │   ├── types/            # 模块类型定义 name.ts维护组件专用
│   │   ├── services/         # API 通信层
│   │   ├── data.d.ts         # data.d.ts维护通用
│   │   └── index.tsx         # 模块入口
├── shared/
│   ├── components/           # 全局通用组件
│   ├── hooks/                # 全局通用 hooks
│   ├── utils/                # 工具函数
│   └── types/                # 全局类型定义
├── app/                      # 应用级配置/路由/状态
└── assets/                   # 静态资源
常量处理：
✅ 优先将跨组件使用的常量提取到 /constants 目录
✅ 组件私有常量保留在组件文件顶部
✅ 常量使用全大写：API_ENDPOINT
❌ 避免将常量写在组件函数内部（会导致每次渲染重复创建）
类型处理：
✅ 创建 /types 目录管理业务核心类型
✅ 组件私有类型保留在组件文件中 例如函数组件参数类型
✅ 全局通用类型使用 .d.ts 声明文件
❌ 避免在组件内部定义被多处引用的业务模型类型

--修改过程 --
新建create-task-view 文件夹
新建
## 3.2 ts类型限制


## 3.3 封装处理逻辑（自定义hooks）


## 3.4 多态代替条件分支

## 3.5 函数式编程