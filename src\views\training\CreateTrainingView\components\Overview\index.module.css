.step-label {
  font-size: 24px;
  line-height: 40px;
  font-weight: bold;
  letter-spacing: 0em;
  text-align: left;
  width: 15% !important;
  font-family: 'HarmonyOS Sans SC', sans-serif;
}
.form-container {
  display: flex;
  flex-direction: column;
}

.form-container :global(.ant-form-item .ant-form-item-label-left) {
  flex: 0 0 18% !important;
}
.form-container :global(.ant-col-offset-2) {
  margin-inline-start: 5.4%;
}
.step-container {
  flex-flow: unset;
}
.step-content {
  width: 100% !important;
}
.step-content :global(.ant-form-item .ant-form-item-label > label) {
  font-size: 14px !important;
}

.step-label .ant-col-4 {
  flex: 0 0 15%;
  max-width: 15%;
}
.step-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;

  label {
    line-height: 40px;
    font-size: 16px;
  }
}

.dataset-container {
  width: 50%;
}

.dataset-title {
  width: 10%;
}

.slider-mark {
  width: 10;
  height: 10;
  margin-top: 10;
  z-index: 10;
  font-size: 12px;
  color: rgba(142, 152, 167, 1);
}

.overview-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 4rem;
}

.overview-item {
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0em;
  text-align: left;
  color: rgba(0, 0, 0, 1);
}

.overview-title {
  min-width: 120px;
  display: inline-block;
}

.config-container {
  display: inline-flex;
  flex-direction: column;
  gap: 1rem;
}

.overview-item label,
.overview-container {
  color: rgba(120, 125, 133, 1);
}

.chart {
  width: 611px;
  height: 270px;
  /* background: rgb(98, 205, 253); */
  display: flex;
  justify-content: center;
  align-items: center;
}

.estimatetime {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 169px;
  margin: 20px 220px;
  border-radius: 10px 10px 10px 10px;
  background: rgba(15, 182, 152, 0.1);
  padding: 10px;
}

.text {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 13px;
  font-weight: 400;
  color: rgba(15, 182, 152, 1);
  margin-top: 25px;
}
