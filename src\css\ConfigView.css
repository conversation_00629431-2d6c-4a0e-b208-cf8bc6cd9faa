* {
  font-family: "HarmonyOS Sans SC", sans-serif;
}

.configureView {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-width: 1920px;
  padding-top: 2.5rem;
  /* text-align: start; */
  width: 92.1%;
  position: relative;
  justify-content: space-around;

}

.configure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.head {
  display: flex;
  width: 100%;
  justify-content: space-around;
}

.save-btn {
  width: 124px;
  background-color: black;
  color: #fff;
}

.configure-title {
  font-size: 18px;
  text-align: start;
  margin: 2px 0 20px 0;
}

.configure-info {
  background: linear-gradient(360deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
  box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
  /* border-radius: 24px; */
  padding: 2rem 2.5rem 2.5rem 2.5rem;
  margin-bottom: 1.25rem;
  margin-top: 1.25rem;
  overflow: hidden;
  border-radius: 2rem;
  min-height: 73vh;
}

.configure-form {
  margin: auto 200px;
}

.configure-chart {
  width: 751px;
  height: 270px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.span-text,
.span-text1 {
  width: 90px;
  position: absolute;
  left: -25px;
  font-size: 14px;
  color: #ccc;
}

.span-text1 {
  margin-left: 24rem;

}
Slider .ant-slider.ant-slider-disabled .ant-slider-track {
  background-color: rgb(15, 182, 152) !important;
}