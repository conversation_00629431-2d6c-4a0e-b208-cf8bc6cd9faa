import React from 'react';
import { LeftOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { Button, Col, message, Modal, Row, Space } from 'antd';
import type { MenuProps } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Scrollbar } from 'react-scrollbars-custom';
import warningIcon from '../../../assets/img/warningIcon.svg';
import DatasetExportModal from '../../../components/DatasetExportModal';

// 导入组件
import TaskInfo from './components/TaskInfo';
import FileLabelView from './components/FileLabelView';
import HeaderActivity from './components/HeaderActivity';
import QAListSection from './components/QAListSection';
import SearchFilterSection from './components/SearchFilterSection';
import SourceTextSection from './components/SourceTextSection';
import PaginationSection from './components/PaginationSection';

// 导入自定义Hooks
import { useQAManagement } from './hooks/useQAManagement';
import { useFilterManagement } from './hooks/useFilterManagement';
import { useSelectionManagement } from './hooks/useSelectionManagement';
import { useTaskDetail } from './hooks/useTaskDetail';

const TaskDetailView: React.FC = () => {
  const { task_id } = useParams<string>();
  const navigate = useNavigate();

  if (!task_id) {
    navigate(`/main/task`);
  }

  // 基础状态
  const [exportModal, setExportModal] = useState(false);
  const [exportAll, setExportAll] = useState(true);
  const [deleteQaDialog, setDeleteQalDialog] = useState(false);
  const [tagsVisible, setTagsVisible] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const [fileList, setFileList] = useState<string[]>([]);

  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });

  const scrollbarRef = useRef<any>();

  // 使用自定义Hooks
  const taskDetailHook = useTaskDetail(task_id!);
  const selectionHook = useSelectionManagement();
  const filterHook = useFilterManagement();
  const qaHook = useQAManagement({
    taskId: task_id!,
    fileList,
    filterVal: filterHook.filterVal,
    reviewFilterType: filterHook.reviewFilterType,
    approvedItems: filterHook.approvedItems,
    allocatedUser: filterHook.allocatedUser,
    scoreFilter: filterHook.scoreFilter,
    selectedTags,
    pagination,
  });

  // 构建过滤下拉菜单项
  const filterDropdownItems: MenuProps['items'] = [
    {
      key: 'reviewed',
      label: '已审核项',
    },
    {
      key: 'verifyUser',
      label: '按审核人',
      children: taskDetailHook.verifyUsers,
    },
    {
      key: 'reviewOption',
      label: '按审核选项',
      children: taskDetailHook.reviewOptions,
    },
    {
      key: 'unreviewed',
      label: '未审核项',
    },
  ];

  // 处理过滤菜单点击，增强filterHook的handleFilter
  const handleFilterWithUserData: MenuProps['onClick'] = (e) => {
    if (e.key.indexOf('verifyUser_') > -1) {
      const id = e.key.split('verifyUser_')[1];
      const user = taskDetailHook.verifyUsers.filter((item: any) => item.value === id);
      filterHook.setFilterType('reviewedSearch');
      filterHook.setSelectText('审核检索');
      filterHook.setAllocatedUser(id);
      filterHook.setScoreFilter('');
      filterHook.setReviewFilterType(undefined);
      filterHook.setApprovedItems(undefined);
      filterHook.setSelectReview(user[0]?.label || '');
    } else if (e.key.indexOf('reviewOption_') > -1) {
      const value = e.key.split('reviewOption_')[1];
      const option = taskDetailHook.reviewOptions.filter((item: any) => item.value === value);
      filterHook.setFilterType('reviewedSearch');
      filterHook.setSelectText('审核检索');
      filterHook.setScoreFilter(value);
      filterHook.setReviewFilterType(undefined);
      filterHook.setApprovedItems(undefined);
      filterHook.setAllocatedUser('');
      filterHook.setSelectReview(option[0]?.label || '');
    } else {
      filterHook.handleFilter(e);
    }
  };

  // 监听相关状态变化，触发QA数据获取
  useEffect(() => {
    if (task_id) {
      qaHook.getQAs(task_id);
    }
  }, [
    pagination.page,
    pagination.size,
    fileList,
    // filterHook.filterVal,
    filterHook.debouncedFilterVal,
    filterHook.approvedItems,
    filterHook.reviewFilterType,
    filterHook.allocatedUser,
    filterHook.scoreFilter,
    selectedTags,
  ]);

  // 更新分页总数
  useEffect(() => {
    if (qaHook.qaList.length > 0) {
      setPagination((prev) => ({ ...prev, total: qaHook.total }));
    }
  }, [qaHook.qaList]);

  // 构建过滤菜单项
  const filterItems: MenuProps['items'] = [
    {
      key: 'keyWord',
      label: '关键词检索',
    },
    {
      key: 'reviewedSearch',
      label: '审核检索',
      children: filterHook.filterMenuItems(filterDropdownItems, ''),
    },
  ];

  // 处理删除操作
  const handleDeleteMenuClick = async () => {
    const success = await qaHook.handleDeleteQA(
      selectionHook.checkedQASet,
      selectionHook.excludeQASet,
      selectionHook.checkedFileSet,
      selectionHook.globalSelectedQADetails
    );
    if (success && task_id) {
      taskDetailHook.onGetTaskDetail(task_id);
      selectionHook.resetCheck();
      qaHook.getQAs(task_id);
      setDeleteQalDialog(false);
    }
  };

  return (
    <Scrollbar>
      <div className="createTaskContent">
        <Space size={20} style={{ display: 'inline-flex', alignItems: 'center' }}>
          <Button
            style={{ fontSize: '12px', width: '36px', height: '36px' }}
            shape="circle"
            icon={<LeftOutlined />}
            onClick={() => navigate('/main/task')}
          />
          <div
            className="mediumText"
            style={{ fontSize: '28px', lineHeight: '36px', fontWeight: '500' }}
          >
            {taskDetailHook.taskDetail?.taskName}
          </div>
        </Space>

        <div className="detailContent">
          <div className="task-detail-info">
            <TaskInfo taskDetail={taskDetailHook.taskDetail} />
            <FileLabelView
              showCheckbox={selectionHook.showCheckbox}
              taskId={task_id as string}
              // 传入数据
              fileTree={taskDetailHook.fileTree}
              flattenTree={taskDetailHook.flattenTree}
              treeData={taskDetailHook.treeData}
              tagList={taskDetailHook.tagList}
              // 回调函数
              onCheckedFileSet={selectionHook.setCheckedFileSet}
              onFileListChange={setFileList}
              onTagsChange={setSelectedTags}
              onExcludeQAChange={selectionHook.setExcludeQASet}
            />
          </div>

          <div className="task-detail-content">
            <HeaderActivity
              task_id={task_id}
              checkedFileSet={selectionHook.checkedFileSet}
              checkedQASet={selectionHook.checkedQASet}
              globalSelectedQAIds={selectionHook.globalSelectedQAIds}
              onShowCheckbox={selectionHook.setShowCheckbox}
              resetCheck={selectionHook.resetCheck}
              exportModal={exportModal}
              setExportModal={setExportModal}
              deleteQaDialog={deleteQaDialog}
              setDeleteQaDialog={setDeleteQalDialog}
              exportAll={exportAll}
              setExportAll={setExportAll}
            />

            <Row style={{ borderTop: '1px solid rgb(239, 239, 239)' }}>
              {/* 原文对照标题栏 */}
              {taskDetailHook.treeData.length > 0 &&
              ((taskDetailHook.treeData[0] as any)?.name.endsWith('.csv') ||
                (taskDetailHook.treeData[0] as any)?.name.endsWith('.json')) ? null : (
                <Col
                  span={10}
                  style={{
                    display: 'flex',
                    padding: '10px 12px ',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderRight: '1px solid rgb(239, 239, 239)',
                  }}
                >
                  <span className="task-title mediumText">原文对照</span>
                  <Button type="text" style={{ color: '#ccc' }}>
                    切换源文件视图
                  </Button>
                </Col>
              )}

              <Col
                span={
                  taskDetailHook.treeData.length > 0 &&
                  ((taskDetailHook.treeData[0] as any)?.name.endsWith('.csv') ||
                    (taskDetailHook.treeData[0] as any)?.name.endsWith('.json'))
                    ? 24
                    : 14
                }
                style={{
                  display: 'flex',
                  padding: '10px 0 10px 12px ',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <SearchFilterSection
                  filterType={filterHook.filterType}
                  selectText={filterHook.selectText}
                  selectReview={filterHook.selectReview}
                  filterVal={filterHook.filterVal}
                  searchTagValue={filterHook.searchTagValue}
                  tagsVisible={tagsVisible}
                  tagList={taskDetailHook.tagList}
                  showCheckbox={selectionHook.showCheckbox}
                  isCurrentPageAllSelected={selectionHook.isCurrentPageAllSelected(
                    qaHook.displayQaList
                  )}
                  isCurrentPageIndeterminate={selectionHook.isCurrentPageIndeterminate(
                    qaHook.displayQaList
                  )}
                  filterItems={filterItems}
                  onFilterChange={handleFilterWithUserData}
                  onFilterValChange={filterHook.debouncedSetFilterVal}
                  onSearchTagValueChange={filterHook.setSearchTagValue}
                  onSelectReviewChange={filterHook.setSelectReview}
                  onTagsVisibleToggle={() => setTagsVisible(!tagsVisible)}
                  onCurrentPageSelectAllChange={(checked) =>
                    selectionHook.handleCurrentPageSelectAll(checked, qaHook.displayQaList)
                  }
                  onOpenChange={filterHook.setOpen}
                  onInitSelect={filterHook.initSelect}
                />
              </Col>
            </Row>

            <Row className="taskDetailContent" style={{ padding: 0 }}>
              <SourceTextSection
                sourceData={qaHook.sourceData}
                fileName={taskDetailHook.fileName}
                highlightIdxList={qaHook.highlightIdxList}
                treeData={taskDetailHook.treeData}
              />
              <Col
                span={
                  taskDetailHook.treeData.length > 0 &&
                  ((taskDetailHook.treeData[0] as any)?.name.endsWith('.csv') ||
                    (taskDetailHook.treeData[0] as any)?.name.endsWith('.json'))
                    ? 24
                    : 14
                }
                style={{ height: '596px' }}
              >
                <Scrollbar height={'740px'} ref={scrollbarRef}>
                  <QAListSection
                    displayQaList={qaHook.displayQaList}
                    showCheckbox={selectionHook.showCheckbox}
                    expendList={qaHook.expendList}
                    filterVal={filterHook.filterVal}
                    tagsVisible={tagsVisible}
                    taskId={task_id!}
                    scoreButtonInfo={taskDetailHook.scoreButtonInfo}
                    checkedQASet={selectionHook.checkedQASet}
                    checkedFileSet={selectionHook.checkedFileSet}
                    excludeQASet={selectionHook.excludeQASet}
                    globalSelectedQAIds={selectionHook.globalSelectedQAIds}
                    globalSelectedQADetails={selectionHook.globalSelectedQADetails}
                    onExpendListChange={qaHook.setExpendList}
                    onHighlightChange={qaHook.setHighlightIdxList}
                    onSourceDataChange={qaHook.setSourceData}
                    onGlobalSelectedChange={(ids, details) => {
                      selectionHook.setGlobalSelectedQAIds(ids);
                      selectionHook.setGlobalSelectedQADetails(details);
                    }}
                    onTreeCheckKeysChange={(_updateFn) => {
                      // 这个功能需要在FileLabelView中处理，暂时留空
                    }}
                    onCheckedQASetChange={selectionHook.setCheckedQASet}
                    onExcludeQASetChange={selectionHook.setExcludeQASet}
                    onTagListUpdate={taskDetailHook.handleTagListUpdate}
                    onFileLabelRefresh={() => taskDetailHook.getDetail()}
                  />
                </Scrollbar>
              </Col>
            </Row>

            <Row>
              {taskDetailHook.treeData.length > 0 &&
              ((taskDetailHook.treeData[0] as any)?.name.endsWith('.csv') ||
                (taskDetailHook.treeData[0] as any)?.name.endsWith('.json')) ? null : (
                <Col span={10}>
                  <div className="taskDetailBottom">
                    溯源置信度 : &nbsp;
                    <span className="span1"> 示例</span>
                    {'≥'}90%&nbsp;&nbsp;&nbsp;&nbsp;
                    <span className="span2"> 示例</span>
                    {'≥'}80%&nbsp;&nbsp;&nbsp;&nbsp;
                    <span className="span3"> 示例</span>
                    {'≥'}70%
                  </div>
                </Col>
              )}

              <PaginationSection
                pagination={pagination}
                treeData={taskDetailHook.treeData}
                onPaginationChange={(page, size) => {
                  const p = { page, size, total: pagination.total };
                  if (p.page !== pagination.page) {
                    qaHook.setExpendList(-1);
                  }
                  setPagination(p);
                  scrollbarRef.current?.scrollToTop();
                }}
              />
            </Row>
          </div>
        </div>
      </div>
      <DatasetExportModal
        visible={exportModal}
        exportTypeValue={true}
        OnClose={() => setExportModal(false)}
        OnQADonload={(exportType) => {
          if (exportAll) {
            qaHook.exportAllQA(exportType, taskDetailHook.taskDetail?.taskName);
          } else {
            qaHook.exportCheckedQA(
              exportType,
              taskDetailHook.taskDetail?.taskName,
              selectionHook.checkedQASet,
              selectionHook.excludeQASet,
              selectionHook.checkedFileSet,
              selectionHook.globalSelectedQAIds,
              selectionHook.globalSelectedQADetails,
              pagination.total
            );
          }
          setExportModal(false);
        }}
      />

      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src={warningIcon} alt="警告" className="warning" />
            <span style={{ marginLeft: 8 }}>提示</span>
          </div>
        }
        centered
        open={deleteQaDialog}
        closable={false}
        width={520}
        className="delete-model"
        footer={[
          <Button className="modal-btn" onClick={() => setDeleteQalDialog(false)}>
            取消
          </Button>,
          <Button className="modal-btn" onClick={handleDeleteMenuClick}>
            确定
          </Button>,
        ]}
        wrapClassName="custom-modal"
      >
        <p>确定要删除所选数据吗？</p>
      </Modal>
    </Scrollbar>
  );
};

export default TaskDetailView;
