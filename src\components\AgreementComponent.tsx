import { FunctionComponent } from "react";
import "./AgreementComponent.css";
import whiteLogo from '../assets/img/white-logo.svg';
const AgreementComponent: FunctionComponent = () => {
  return (
    <div className="rectangle-parent68">
      {/* <div className="frame-child321" /> */}
      <a>
        <b className="b81 boldText">行至用户隐私协议</b>
      </a>
      <div className="logo-parent2">
        {/* <div className="logo38">
          <div className="logo-child187" />
          <div className="logo-child188" />
          <div className="logo-child189" />
          <div className="logo-child190" />
          <div className="logo-child191" />
        </div> */}
        <img className="logo38" src={whiteLogo} />
        <div className="everreachai-pollux21 enText">EverReachAI Pollux</div>
      </div>
      {/* <div className="logo39">
        <div className="logo-child192" />
        <div className="logo-child193" />
        <div className="logo-child194" />
        <div className="logo-child195" />
        <div className="logo-child196" />
      </div> */}
      <img className="logo39" src={whiteLogo} />
      <b className="b82 boldText">隐私协议</b>
    </div>
  );
};

export default AgreementComponent;
