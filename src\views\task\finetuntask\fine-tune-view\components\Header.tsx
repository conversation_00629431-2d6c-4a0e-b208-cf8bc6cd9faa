import { Button, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import infoIcon from '@/assets/img/info-icon.svg';

export const tooltip = (
  <span style={{ lineHeight: '25px' }}>
    模型微调流程:
    <br />
    1.选定合适基座模型
    <br />
    2.调整训练配置
    <br />
    3.执行训练任务
    <br />
    4.模型部署应用
  </span>
);
const Header = () => {
  const navigate = useNavigate();

  return (
    <div
      className="mediumText"
      style={{
        fontSize: '28px',
        lineHeight: '36px',
        fontWeight: '500',
        marginLeft: '2rem',
        justifyContent: 'space-between',
        display: 'flex',
      }}
    >
      <div> 模型仓库 </div>
      <div>
        <Button
          type="primary"
          size="large"
          shape="round"
          style={{
            backgroundColor: 'black',
            fontSize: '14px',
            fontWeight: '700',
            margin: '0 1rem 0 0',
          }}
          className="boldText"
          onClick={() => {
            navigate('/main/finetune/serverManagement');
          }}
        >
          服务器管理
        </Button>
        <Button
          type="primary"
          size="large"
          shape="round"
          style={{
            backgroundColor: 'black',
            fontSize: '14px',
            fontWeight: '700',
            margin: '0 1rem 0 0',
          }}
          className="boldText"
          onClick={() => {
            navigate('/main/finetune/create');
          }}
        >
          微调训练
        </Button>
        <Tooltip title={tooltip}>
          <img src={infoIcon} style={{ width: '16px', height: '16px' }} />
        </Tooltip>
      </div>
    </div>
  );
};

export default Header;
