import React, { useCallback } from 'react';
import { <PERSON><PERSON>, Card, Col, Dropdown, Empty, Input, Row, Space, Tooltip } from 'antd';
import { CaretDownOutlined, SearchOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ModelSetType } from '@/types';
import { debounce } from '@/utils/utils';
import logo from '@/assets/img/logo-x.png';

interface BaseModelSectionProps {
  data: ModelSetType[];
  filterInput: string;
  sortDirection: string;
  onFilterInputChange: (value: string) => void;
  onSortDirectionChange: (direction: string) => void;
  onModelClick: (id: string) => void;
}

const BaseModelSection: React.FC<BaseModelSectionProps> = ({
  data,
  filterInput,
  sortDirection,
  onFilterInputChange,
  onSortDirectionChange,
  onModelClick,
}) => {
  const navigate = useNavigate();

  // 使用防抖处理搜索输入
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onFilterInputChange(value);
    }, 500),
    [onFilterInputChange]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  const handleMenuClick = (e: any) => {
    if (e.key === '2-1') {
      onSortDirectionChange('asc');
    } else if (e.key === '2-2') {
      onSortDirectionChange('desc');
    }
  };

  const pedestalDropdownItems = [
    {
      key: 'verifyUser',
      label: '按模型名称',
      children: [
        {
          key: '2-1',
          label: '正序',
        },
        {
          key: '2-2',
          label: '倒序',
        },
      ],
    },
  ];

  return (
    <div className="pedestal-info">
      <div
        style={{
          width: '100%',
          textAlign: 'start',
          justifyContent: 'space-between',
          display: 'inline-flex',
          marginBottom: '1.5rem',
          alignItems: 'center',
        }}
      >
        <Space size="small">
          <div
            style={{
              margin: '0 1rem 0 0',
              width: '64px',
              height: '19px',
              fontWeight: '500',
              fontSize: '16px',
            }}
          >
            基座模型
          </div>
          <Space.Compact>
            <Input
              size="large"
              className="filter-input"
              suffix={<SearchOutlined />}
              defaultValue={filterInput}
              onChange={handleInputChange}
              placeholder="请输入基座模型名称"
            />
          </Space.Compact>

          <Dropdown
            menu={{
              items: pedestalDropdownItems,
              onClick: handleMenuClick,
            }}
          >
            <Button
              className="default-btn"
              style={{
                width: 124,
                backgroundColor: '#fff',
                fontSize: '14px',
                fontWeight: '400',
                margin: '0 0 0 1rem',
                color: '#111111',
                border: '1px solid #D7DDE5',
              }}
            >
              筛选
              <CaretDownOutlined />
            </Button>
          </Dropdown>
          <Button
            className="default-btn"
            style={{
              backgroundColor: '#fff',
              fontSize: '14px',
              fontWeight: '400',
              margin: '0 0 0 1rem',
              color: '#111111',
              border: '1px solid #D7DDE5',
            }}
            onClick={() => {
              navigate('/main/finetune/addBaseModel');
            }}
          >
            新增基座模型
          </Button>
        </Space>
        <div>{`共${data.length}个`}</div>
      </div>
      <div className="baseModelArea">
        <div className="scrollbar">
          {data.length === 0 && <Empty />}
          <Row gutter={[20, 36]} wrap={true}>
            {data.map((item) => (
              <Col span={6} key={item.id}>
                <Card
                  className={item.modelName.slice(0, 2) === '行至' ? 'purpleBlue' : 'otherColor'}
                  bordered={false}
                  headStyle={{ borderBottom: 'none' }}
                  style={{
                    wordWrap: 'break-word',
                    height: '177px',
                    width: '320px',
                    color: '#FFFFFF',
                  }}
                >
                  <Tooltip title={item.modelName}>
                    <div className={'baseModelNameTitle'}>{item.modelName}</div>
                  </Tooltip>
                  <Tooltip title={item.introduction}>
                    <div className={'baseModelIntroduction'}>{item.introduction}</div>
                  </Tooltip>
                  <Button
                    style={{
                      position: 'absolute',
                      left: '80%',
                      bottom: '7px',
                      color: '#FFFFFF',
                    }}
                    type="text"
                    onClick={() => onModelClick(item.id)}
                  >
                    详情
                  </Button>
                  <div style={{ position: 'relative' }}>
                    {item.modelName.slice(0, 2) === '行至' && (
                      <img
                        src={logo}
                        style={{
                          position: 'absolute',
                          left: '88%',
                          bottom: '35px',
                          width: '36px',
                          height: '35px',
                        }}
                      />
                    )}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>
    </div>
  );
};

export default BaseModelSection;
