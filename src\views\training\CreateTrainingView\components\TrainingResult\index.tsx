import React from 'react';
import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import TrainingVector from '../../../../../assets/img/training_vector.svg';
import classes from './index.module.css';

const TrainingResult: React.FC = () => {
  const navigate = useNavigate();

  return <>
    <div className={classes['result-container']}>
      <img src={TrainingVector} />
      <div className={classes['result-title-container']}>
        <div className={classes['result-title']}>已成功创建微调训练模型</div>
        <div className={classes['result-subtitle']}>请返回【微调模型】刷新查看</div>
      </div>
    </div>
    {/* <div className={classes['result-btn-div']}> */}
    <Button type='primary' className={`primary-btn ${classes['confirm-btn']}`} onClick={() => { navigate('/main/finetune') }}>完成</Button>
    {/* </div> */}
  </>
};

export default TrainingResult;