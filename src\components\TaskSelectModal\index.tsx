import { Button, Empty, Modal, Space } from "antd";
import { useEffect, useState } from "react";
import Table, { ColumnsType } from "antd/es/table";
import { QuestionCircleFilled } from "@ant-design/icons";
import classes from "./index.module.css";
import { TaskStatus, TaskType, TaskDetailType } from "../../types";
import { formattedTime } from "../../utils/formatred";
import emptyLogo from "../../assets/img/empty-logo.svg";
import { getTaskDetail, getTasks } from "../../api/task";
import TablePagination from "../TablePagination";
interface TaskSelectModalProp {
  visible: boolean;
  OnClose: (tasks?: TaskType[]) => void;
  taskIds?: any;
  alltasks?: any;
}

const TaskSelectModal: React.FC<TaskSelectModalProp> = ({
  visible,
  OnClose,
  taskIds,
  alltasks,
}) => {
  const [tableIndex, setTableIndex] = useState(0);
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TaskType[] >([]);
  const [taskData, setTaskData] = useState<TaskType[]>([]);
  const [alltaskData, setAllTaskData] = useState<TaskType[]>([]);
  const [isButtonEnabled, setIsButtonEnabled] = useState(false);
  const [allSelectedRowKeys, setAllSelectedRowKeys] = useState<React.Key[]>([]);
  const [allSelectedRows, setAllSelectedRows] = useState<TaskType[]>([]);

  const getTaskData = () => {
    getTasks({ ...pagination }).then((res) => {
      if (res?.data?.code === 200) {
        setTaskData(res.data.data);
        if (res.data.data.length > 0) {
          setPagination({ ...pagination, total: res.data.totalCount });
          setTableIndex((pagination.page - 1) * pagination.size);
        }
      }
    });
  };
  const getAllTasks = () => {
    // getTasks({ size:-1 }).then((res) => {
    //   if (res?.data?.code === 200) {
    //     setAllTaskData(res.data.data);
    //   }
    // });
    setAllTaskData(alltasks);
  };
  useEffect(() => {
    setSelectedRowsBasedOnIds(taskIds);
  }, [taskIds]);
  const setSelectedRowsBasedOnIds = (taskIds: any) => {
    if (!taskIds || taskIds.length === 0) {
      return;
    }
    const selectedRowKeys: React.Key[] = [];
    const selectedRows: TaskType[] = [];
    alltaskData.forEach((task) => {
      if (taskIds.includes(task.taskId)) {
        selectedRowKeys.push(task.taskId);
        selectedRows.push(task);
        setIsButtonEnabled(true);
      }
    });
    setSelectedRowKeys(selectedRowKeys);
    console.log("selectedRows", selectedRows);
    
    setSelectedRows(selectedRows);
  };

  useEffect(() => {
    getTaskData();
    getAllTasks();
  }, []);
  useEffect(() => {
    getTaskData();
  }, [pagination.page, pagination.size]);

  useEffect(() => {
    setTableIndex((pagination.page - 1) * pagination.size);
  }, [pagination]);

  const columns: ColumnsType<TaskType> = [
    {
      title: "序号",
      dataIndex: "index",
      render: (text, record, index) => (
        // pagination.page - 1) * pagination.size
        <>{tableIndex + index + 1}</>
      ),
      width: "10%",
    },
    {
      title: "任务名称",
      dataIndex: "taskName",
      width: "20%",
    },
    {
      title: "创建时间",
      dataIndex: "createdate",
      width: "20%",
      render: (_, { createTime }) => {
        const timestamp = createTime;
        return formattedTime(new Date(timestamp));
      },
    },
    {
      title: "任务状态",
      dataIndex: "status",
      render: (_, { taskId, status, failedReason, complete, total }) => {
        if (status === TaskStatus.success) {
          if (failedReason) {
            return (
              <Space>
                <label className={"default-info"}>
                  任务完成{`(${complete}/${total})`}
                </label>
                <QuestionCircleFilled
                  style={{ color: "#E75252", cursor: "pointer" }}
                />
              </Space>
            );
          }
          return (
            <label className={"default-info"}>
              任务完成{`(${complete}/${total})`}
            </label>
          );
        } else if (status === TaskStatus.failed) {
          if (complete && complete > 0) {
            return (
              <label className="warning-info">
                进行中{`(${complete}/${total})`}
              </label>
            );
          } else {
            return <label className="error-info"> 任务失败</label>;
          }
        } else if (status === TaskStatus.inProgress) {
          return <label>进行中{`(${complete}/${total})`}</label>;
        }
      },
      width: "20%",
    },
    {
      title: "问答对数量",
      dataIndex: "qaCount",
      width: "20%",
    },
  ];

  const rowSelection = {
  onChange: (selectedKeys: React.Key[], selectedRows: TaskType[]) => {
    // 合并新选中的项（当前页）和之前全局已选项
    const mergedKeys = Array.from(new Set([...allSelectedRowKeys, ...selectedKeys]));

    const mergedRows = Array.from(
      new Map(
        [...allSelectedRows, ...selectedRows].map((item) => [item.taskId, item])
      ).values()
    );

    // 如果取消勾选，需要从 mergedKeys/mergedRows 中剔除未勾选的
    const currentPageIds = taskData.map((item) => item.taskId);
    const uncheckedKeys = currentPageIds.filter((id) => !selectedKeys.includes(id));

    const filteredKeys = mergedKeys.filter((key) => !uncheckedKeys.includes(key as any));
    const filteredRows = mergedRows.filter((row) => !uncheckedKeys.includes(row.taskId));

    setAllSelectedRowKeys(filteredKeys);
    setAllSelectedRows(filteredRows);
    setSelectedRowKeys(filteredKeys);
    setSelectedRows(filteredRows);

    const totalQaCount = filteredRows.reduce((sum, item) => sum + (item.qaCount || 0), 0);
    setIsButtonEnabled(totalQaCount >= 0);
  },
  getCheckboxProps: (record: TaskType) => ({
    name: record.taskName,
    disabled: record.status !== TaskStatus.success,
  }),
};


  return (
    <Modal
      centered
      title="选择训练数据"
      keyboard={false}
      maskClosable={false}
      styles={{ body: { height: "650px", overflowY: "auto" } }}
      width={"50%"}
      open={visible}
      onOk={() => OnClose(selectedRows)}
      onCancel={() => OnClose()}
      destroyOnClose
      footer={
        <>
          <div className={classes["confirm-modal-footer-label"]}>
            只能选择解析完成的推理任务哦
          </div>
          <div className={classes["confirm-modal-footer"]}>
            <Button
              size="large"
              type="primary"
              shape="round"
              className={classes["confirm-btn"]}
              disabled={!isButtonEnabled}
              onClick={() => OnClose(selectedRows)}
            >
              确认选择
            </Button>
          </div>
        </>
      }
    >
      <Table
        locale={{
          emptyText: (
            <Empty
              image={emptyLogo}
              description={
                <span className="dataset-table-empty-label">
                  空空如也，去上传本地文件吧~
                </span>
              }
            />
          ),
        }}
        // style={{ flex: 1 }}
        tableLayout={"fixed"}
        rowKey="taskId"
        className="dataset-table"
        rowSelection={{
          type: "checkbox",
          ...rowSelection,
          selectedRowKeys:selectedRowKeys
        }}
        columns={columns}
        dataSource={taskData}
        pagination={false}
      />
      <TablePagination
        total={pagination.total}
        pageSize={pagination.size}
        page={pagination.page}
        OnChange={(page, pageSize) => {
          setPagination({ total: pagination.total, page, size: pageSize });
        }}
      />
    </Modal>
  );
};

export default TaskSelectModal;
