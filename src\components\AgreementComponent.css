.frame-child321 {
  position: absolute;
  top: 17.63rem;
  left: 0;
  border-radius: 0 0 var(--br-5xl) var(--br-5xl);
  background: linear-gradient(180deg, #7accbf, #6ad2c1);
  width: 29.69rem;
  height: 3.5rem;
}
.b81 {
  position: absolute;
  top: 12.06rem;
  left: 10.88rem;
  text-decoration: underline;
  color: white;
}
.logo-child187,
.logo-child188 {
  top: 0.01rem;
  transform: rotate(45.67deg);
}
.logo-child187 {
  position: absolute;
  left: 0.86rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-mediumspringgreen);
  width: 0.18rem;
  height: 1.2rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child188 {
  left: 0.54rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 0.54rem;
}
.logo-child188,
.logo-child189,
.logo-child190,
.logo-child191 {
  position: absolute;
  border-radius: var(--br-26xl);
  width: 0.18rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child189 {
  top: 0;
  left: 0.23rem;
  background-color: var(--color-teal-100);
  height: 0.33rem;
  transform: rotate(45.67deg);
}
.logo-child190,
.logo-child191 {
  top: 0.99rem;
  left: 0.46rem;
  background-color: var(--color-mediumaquamarine-200);
  height: 0.54rem;
  transform: rotate(-134.33deg);
}
.logo-child191 {
  top: 1rem;
  left: 0.77rem;
  background-color: var(--color-teal-100);
  height: 0.33rem;
}
.logo38 {
  position: relative;
  width: 1rem;
  height: 1rem;
}
.everreachai-pollux21 {
  position: relative;
  font-weight: 900;
}
.logo-parent2 {
  position: absolute;
  top: 18.88rem;
  left: 10.56rem;
  width: 8.63rem;
  height: 1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-5xs);
  font-size: var(--font-size-xs);
}
.logo-child192,
.logo-child193 {
  position: absolute;
  transform: rotate(45.67deg);
}
.logo-child192 {
  top: 0.03rem;
  left: 3.75rem;
  border-radius: var(--br-26xl);
  background-color: var(--color-white);
  width: 0.78rem;
  height: 5.26rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child193 {
  top: 0.04rem;
  left: 2.35rem;
  height: 2.35rem;
  opacity: 0.6;
}
.logo-child193,
.logo-child194,
.logo-child195,
.logo-child196 {
  border-radius: var(--br-26xl);
  background-color: var(--color-white);
  width: 0.78rem;
  transform-origin: 0 0;
  mix-blend-mode: normal;
}
.logo-child194 {
  position: absolute;
  top: 0;
  left: 1rem;
  height: 1.44rem;
  transform: rotate(45.67deg);
  opacity: 0.3;
}
.logo-child195,
.logo-child196 {
  transform: rotate(-134.33deg);
}
.logo-child195 {
  position: absolute;
  top: 4.34rem;
  left: 2.02rem;
  height: 2.35rem;
  opacity: 0.6;
}
.logo-child196 {
  top: 4.38rem;
  left: 3.37rem;
  height: 1.44rem;
  opacity: 0.3;
}
.b82,
.logo-child196,
.logo39 {
  position: absolute;
}
.logo39 {
  top: 6.19rem;
  left: 12.69rem;
  width: 4.38rem;
  height: 4.38rem;
}
.b82 {
  top: 2rem;
  left: 2.5rem;
}
.rectangle-parent68 {
  position: absolute;
  top: 29.69rem;
  left: 45.13rem;
  border-radius: var(--br-5xl);
  background: linear-gradient(180deg, #0fb698, #b0e5df);
  width: 29.69rem;
  height: 21.19rem;
  mix-blend-mode: normal;
  text-align: left;
  font-size: var(--font-size-base);
  color: var(--color-white);
}
