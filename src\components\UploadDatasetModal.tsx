import { Button, Modal, Upload, UploadProps, message } from 'antd';
import <PERSON>agger from 'antd/es/upload/Dragger';
import { useState } from 'react';
import Scrollbar from 'react-scrollbars-custom';
import UploadDatasetList from './UploadDatasetList';
import { changeDataSetsTags, uploadDataset, deleteDataset } from '../api/dataset';
import uploadcomputer from '../assets/img/uploadcomputer.svg';
import { UploadFile } from 'antd/es/upload/interface';

export interface UploadItem {
  file: UploadFile;
  dataSetId?: string;
  tags?: string[];
  status?: 'uploading' | 'done' | 'error';
  parseState?: string;
  name?: string;
}

interface UploadDatasetModalProp {
  visible: boolean;
  OnClose: () => void;
}

const UploadDatasetModal: React.FC<UploadDatasetModalProp> = ({ visible, OnClose }) => {
  const [uploadList, setUploadList] = useState<UploadItem[]>([]);

  const clearState = () => {
    setUploadList([]);
  };

  const props: UploadProps = {
    name: 'file',
    multiple: true,
    action: '/api/qa_generator/upload_data',
    accept: '.txt,.docx,.doc,.pdf,.zip,.csv,.xlsx,.json,.png,.md,.jpg,.jpeg',
    showUploadList: false,
    beforeUpload: (file) => {
      const allowedExtensions = [
        '.txt',
        '.docx',
        '.doc',
        '.pdf',
        '.zip',
        '.csv',
        '.xlsx',
        '.json',
        '.md',
        '.png',
        '.jpg',
        '.jpeg',
      ];
      const fileName = file.name.toLowerCase();
      const isValid = allowedExtensions.some((ext) => fileName.endsWith(ext));

      if (!isValid) {
        message.error(
          `不支持的文件类型: ${file.name}。请上传以下类型: ${allowedExtensions.join(', ')}`
        );
        return Upload.LIST_IGNORE;
      }

      return true;
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
    customRequest: (options) => {
      const file = options.file as UploadFile;
      const uid = file.uid;

      setUploadList((prev) => [...prev, { file, status: 'uploading', name: file.name }]);

      uploadDataset({ importDataset: file, tags: [] })
        .then((res) => {
          if (res.data?.code === 200) {
            setUploadList((prev) =>
              prev.map((item) =>
                item.file.uid === uid ? { ...item, status: 'done', dataSetId: res.data.data } : item
              )
            );
            if (options.onSuccess) options.onSuccess(res, file as any);
          } else {
            setUploadList((prev) =>
              prev.map((item) => (item.file.uid === uid ? { ...item, status: 'error' } : item))
            );
            if (options.onError) options.onError(res.data);
          }
        })
        .catch((err) => {
          setUploadList((prev) =>
            prev.map((item) => (item.file.uid === uid ? { ...item, status: 'error' } : item))
          );
          if (options.onError) options.onError(err);
        });
    },
  };

  return (
    <Modal
      title="上传本地数据集"
      centered
      keyboard={false}
      maskClosable={false}
      width={870}
      open={visible}
      onOk={() => {
        clearState();
        OnClose();
      }}
      onCancel={() => {
        clearState();
        OnClose();
      }}
      styles={{ body: { height: '624px' } }}
      footer={[
        <div key="okBtnDiv" style={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            type="primary"
            className="primary-btn boldText"
            style={{ width: '124px' }}
            onClick={() => {
              clearState();
              OnClose();
            }}
          >
            确认
          </Button>
        </div>,
      ]}
    >
      <div style={{ height: '100%' }}>
        <Dragger {...props} style={{ height: 239 }} className="uploadDatasetDragger">
          <div className="createTaskDraggerInner reqularText">
            <img className="createTaskDraggerIcon" alt="" src={uploadcomputer} />
            <p>拖入您需要解析的本地数据集文档，或点击进行选择</p>
          </div>
          <div className="createTaskDraggerInfo reqularText">
            <label className="crateTaskDraggerLabel">请将文档打包为zip格式进行上传</label>
            <br />
            <label
              className="crateTaskDraggerLabel"
              style={{ left: '39%', transform: 'translateX(-31%)' }}
            >
              支持解析的文档类型有txt、doc、docx、pdf、json、csv、xlsx、markdown、png、jpg、jpeg
            </label>
          </div>
        </Dragger>
        <div style={{ height: '369px' }}>
          <Scrollbar>
            <UploadDatasetList
              fileList={uploadList}
              onAddTag={(datasetId, tags) => {
                changeDataSetsTags({ dataSetId: datasetId, tags }).then((res) => {
                  if (res?.data?.code === 0) {
                    setUploadList((prev) =>
                      prev.map((item) => (item.dataSetId === datasetId ? { ...item, tags } : item))
                    );
                  }
                });
              }}
              onDelFile={(uid) => {
                const target = uploadList.find((item) => item.file.uid === uid);
                if (!target) return;

                setUploadList((prev) => prev.filter((item) => item.file.uid !== uid));

                if (target.status === 'done' && target.dataSetId) {
                  deleteDataset([target.dataSetId])
                    .then((res) => {
                      if (res.data?.code === 200) {
                        message.success('删除成功');
                      } else {
                        message.error('删除失败');
                      }
                    })
                    .catch(() => message.error('删除失败'));
                }
              }}
              onReUpload={(uid) => {
                const target = uploadList.find((item) => item.file.uid === uid);
                if (!target) return;

                setUploadList((prev) =>
                  prev.map((item) =>
                    item.file.uid === uid ? { ...item, status: 'uploading' } : item
                  )
                );

                uploadDataset({ importDataset: target.file, tags: [] })
                  .then((res) => {
                    if (res.data?.code === 200) {
                      setUploadList((prev) =>
                        prev.map((item) =>
                          item.file.uid === uid
                            ? { ...item, status: 'done', dataSetId: res.data.data }
                            : item
                        )
                      );
                    } else {
                      setUploadList((prev) =>
                        prev.map((item) =>
                          item.file.uid === uid ? { ...item, status: 'error' } : item
                        )
                      );
                    }
                  })
                  .catch(() => {
                    setUploadList((prev) =>
                      prev.map((item) =>
                        item.file.uid === uid ? { ...item, status: 'error' } : item
                      )
                    );
                  });
              }}
            />
          </Scrollbar>
        </div>
      </div>
    </Modal>
  );
};

export default UploadDatasetModal;
