import { Button, Modal, Upload, UploadFile, UploadProps, message } from 'antd';
import <PERSON>agger from 'antd/es/upload/Dragger';
import { useState } from 'react';
import Scrollbar from 'react-scrollbars-custom';
import UploadList from './UploadList';
import { changeDataSetsTags, uploadDataset, deleteDataset } from '../api/dataset';
import uploadcomputer from '../assets/img/uploadcomputer.svg';
import { DatasetFile } from '../views/task/create-task-view/types';
interface UploadDatasetModalProp {
  visible: boolean;
  OnClose: () => void;
}

const UploadDatasetModal: React.FC<UploadDatasetModalProp> = ({ visible, OnClose }) => {
  const [fileList, setFileList] = useState<DatasetFile[]>([]);

  // 清理状态的函数
  const clearState = () => {
    setFileList([]);
  };

  const props: UploadProps = {
    name: 'file',
    multiple: true,
    action: '/api/qa_generator/upload_data',
    accept: '.txt,.docx,.doc,.pdf,.zip,.csv,.xlsx,.json,.png,.md,.jpg,.jpeg',
    showUploadList: false,
    fileList: fileList,
    beforeUpload: (file: { name: string }) => {
      const allowedExtensions = [
        '.txt',
        '.docx',
        '.doc',
        '.pdf',
        '.zip',
        '.csv',
        '.xlsx',
        '.json',
        '.md',
        '.png',
        '.jpg',
        '.jpeg',
      ];
      const fileName = file.name.toLowerCase();
      const isValid = allowedExtensions.some((ext) => fileName.endsWith(ext));

      if (!isValid) {
        message.error(
          `不支持的文件类型: ${file.name}。请上传以下类型: ${allowedExtensions.join(', ')}`
        );
        return Upload.LIST_IGNORE; // 阻止上传
      }

      return true; // 允许上传
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
    customRequest: (options) => {
      uploadDataset({ importDataset: options.file, tags: [] })
        .then((res) => {
          if (res.data?.code === 200) {
            if (options.onSuccess) {
              options.onSuccess(res, options.file as any);
              // 将文件ID存储在文件对象中
              setFileList((prevList) =>
                prevList.map((file) =>
                  file.uid === (options.file as any).uid
                    ? { ...file, dataSetId: res.data.data }
                    : file
                )
              );
            }
          } else {
            if (options.onError) {
              options.onError(res.data);
            }
          }
        })
        .catch(options.onError);
    },
    onChange(info) {
      let newFileList = [...info.fileList];
      // console.log(info.file, info.fileList);
      // if (info.file.status !== 'uploading') {
      //   console.log(info.file, info.fileList);
      // }
      // if (info.file.status === 'done') {
      //   message.success(`${info.file.name} 上传成功`);
      // } else if (info.file.status === 'error') {
      //   message.error(`${info.file.name} 上传失败`);
      // }
      setFileList(newFileList as any);
    },
  };
  return (
    <Modal
      title="上传本地数据集"
      centered={true}
      keyboard={false}
      maskClosable={false}
      width={870}
      open={visible}
      onOk={() => {
        clearState();
        OnClose();
      }}
      onCancel={() => {
        clearState();
        OnClose();
      }}
      cancelButtonProps={
        {
          // disabled:
        }
      }
      styles={{ body: { height: '624px' } }}
      footer={[
        <div
          key={'okBtnDiv'}
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button
            key={'okBtn'}
            type="primary"
            className="primary-btn boldText"
            style={{ width: '124px' }}
            onClick={() => {
              clearState();
              OnClose();
            }}
          >
            确认
          </Button>
        </div>,
      ]}
    >
      <div style={{ height: '100%' }}>
        <Dragger {...props} style={{ height: '239px !important' }} className="uploadDatasetDragger">
          <div className="createTaskDraggerInner reqularText">
            <img className="createTaskDraggerIcon" alt="" src={uploadcomputer} />
            <p>拖入您需要解析的本地数据集文档，或点击进行选择</p>
          </div>
          <div className="createTaskDraggerInfo reqularText">
            <label className="crateTaskDraggerLabel">请将文档打包为zip格式进行上传</label>
            <br />
            <label
              className="crateTaskDraggerLabel"
              style={{ left: '39%', transform: 'translateX(-31%)' }}
            >
              支持解析的文档类型有txt、doc、docx、pdf、json、csv、xlsx、markdown、png、jpg、jpeg
            </label>
          </div>
        </Dragger>
        <div style={{ height: '369px' }}>
          <Scrollbar>
            <UploadList
              key={'uploadDatasetModal'}
              fileList={fileList}
              onAddTag={(dataSetId: string, tags: string[]) => {
                changeDataSetsTags({
                  dataSetId,
                  tags: tags,
                }).then((res) => {
                  if (res?.data?.code === 0) {
                    const list = fileList;
                    list.forEach((file) => {
                      if (file.dataSetId === dataSetId) {
                        file.tags = tags;
                      }
                    });
                    setFileList(list);
                  }
                });
              }}
              onDelFile={(index) => {
                const fileToDelete = fileList[index];

                // 先从UI中移除文件
                const updatedDataList = fileList.filter((_, i) => i !== index);
                setFileList(updatedDataList);

                // 如果文件已上传成功且有dataSetId，则从服务器删除
                if (fileToDelete?.status === 'done' && fileToDelete.dataSetId) {
                  deleteDataset([fileToDelete.dataSetId])
                    .then((res) => {
                      if (res.data?.code === 200) {
                        message.success('删除成功');
                      } else {
                        message.error('删除失败');
                      }
                    })
                    .catch(() => {
                      message.error('删除失败');
                    });
                }
              }}
              onReUpload={(index: number) => {
                const file = fileList[index];

                // 更新文件状态为上传中
                setFileList((prevList) =>
                  prevList.map((item, i) => (i === index ? { ...item, status: 'uploading' } : item))
                );

                uploadDataset({ importDataset: file, tags: [] })
                  .then((res) => {
                    if (res.data?.code === 200) {
                      // 上传成功
                      setFileList((prevList) =>
                        prevList.map((item, i) =>
                          i === index ? { ...item, status: 'done', dataSetId: res.data.data } : item
                        )
                      );
                    } else {
                      // 上传失败
                      setFileList((prevList) =>
                        prevList.map((item, i) =>
                          i === index ? { ...item, status: 'error' } : item
                        )
                      );
                    }
                  })
                  .catch(() => {
                    // 上传异常
                    setFileList((prevList) =>
                      prevList.map((item, i) => (i === index ? { ...item, status: 'error' } : item))
                    );
                  });
              }}
            ></UploadList>
          </Scrollbar>
        </div>
      </div>
    </Modal>
  );
};
export default UploadDatasetModal;
