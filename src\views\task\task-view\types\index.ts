import { TaskType } from '../../../../types';

// 分页参数类型
export interface PaginationState {
  page: number;
  size: number;
  total: number;
}

// 搜索筛选参数类型
export interface SearchFilterState {
  filterAttribute: string;
  sortAttribute: string;
  sortDirection: string;
  filterTaskName?: string;
  filterInput?: string;
}

// 任务选择状态类型
export interface TaskSelectionState {
  selectedRows: TaskType[];
  selectedRowKeys: Set<string>;
}

// 模态框状态类型
export interface ModalState {
  uploadErrorModal: boolean;
  exportModal: boolean;
  reviewConfigModal: boolean;
}

// 任务操作类型
export interface TaskOperations {
  onDelete: (taskIds: string[]) => void;
  onExport: (tasks: TaskType[]) => void;
  onRename: (taskId: string, newName: string) => void;
  onReviewConfig: (taskId: string) => void;
  onApply: (e: React.MouseEvent<HTMLElement>, rowData: TaskType) => void;
}

// 搜索筛选组件Props
export interface SearchFilterProps {
  filterAttribute: string;
  sortDirection: string;
  filterInput?: string;
  onFilterAttributeChange: (value: string) => void;
  onSortDirectionChange: (value: string) => void;
  onFilterInputChange: (value: string) => void;
  onSearch: () => void;
}

// 操作按钮组件Props
export interface ActionButtonsProps {
  selectedRows: TaskType[];
  onDelete: () => void;
  onExport: () => void;
  onUpload: () => void;
  onCreate: () => void;
}

// 任务表格组件Props
export interface TaskTableProps {
  taskData: TaskType[];
  selectedRowKeys: Set<string>;
  renameList: Set<string>;
  tableIndex: number;
  onSelectionChange: (selectedRowKeys: React.Key[], selectedRows: TaskType[]) => void;
  onRename: (taskId: string) => void;
  onDelete: (taskId: string) => void;
  onApply: (e: React.MouseEvent<HTMLElement>, rowData: TaskType) => void;
  onProblemDetail: (taskId: string) => void;
  onRetry: (taskId: string) => void;
  onDetail: (taskId: string) => void;
  onVisibleRangeChange: (taskId: string, users: string[]) => void;
  getTasksList: () => void;
  setRenameList: React.Dispatch<React.SetStateAction<Set<string>>>;
}

// 输入组件Props
export interface InputComponentProps {
  taskInfo: TaskType;
  renameList: Set<string>;
  setRenameList: React.Dispatch<React.SetStateAction<Set<string>>>;
  getTasksList: () => void;
}

// 主组件Props
export interface TaskViewProps {
  // 如果需要从父组件传递props，在这里定义
}

// 任务状态枚举
export enum TaskStatusEnum {
  SUCCESS = 'SUCCESS',
  PROCESSING = 'PROCESSING',
  FAILED = 'FAILED',
  ERROR = 'ERROR',
}

// 菜单点击事件类型
export interface MenuClickEvent {
  key: string;
  taskId: string;
}

// API调用状态类型
export interface ApiState {
  loading: boolean;
  error?: string;
}
