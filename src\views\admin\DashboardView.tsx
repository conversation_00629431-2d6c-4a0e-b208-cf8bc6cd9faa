import {
  <PERSON><PERSON>,
  <PERSON>,
  Di<PERSON><PERSON>,
  Dropdown,
  MenuProps,
  Row,
  Space,
  Statistic,
} from "antd";
import Scrollbar from "react-scrollbars-custom";
import WordCloud from "react-wordcloud";
import { CaretDownOutlined } from "@ant-design/icons";
import * as echarts from "echarts";
import { useEffect, useRef, useState } from "react";
import {
  getAnalysDataGenerate,
  getAnalysDataReview,
  getAnalyseUserInfo,
  getDomain,
  getOverview,
  OverviewData,
  ResultMap,
} from "../../api/admin";
type InputData = {
  [key: string]: number;
};

type OutputData = {
  text: string;
  value: number;
};

const statisticStyle = {
  color: "#0FB698",
  fontFamily: "HarmonyOS Sans SC Bold",
  fontSize: "16px",
  fontStyle: "normal",
  fontWeight: 700,
  lineHeight: "normal",
};

// const words = [
//   { text: "科研", value: 30 },
//   { text: "计算机", value: 25 },
//   { text: "法律", value: 20 },
//   { text: "人工智能", value: 20 },
//   { text: "军工", value: 32 },
//   { text: "医疗", value: 35 },
//   { text: "安防", value: 22 },
//   { text: "金融", value: 15 },
//   { text: "质检", value: 40 },
// ];

const filterDropdownItems: MenuProps["items"] = [
  {
    key: "day",
    label: "日活跃度",
  },
  {
    key: "week",
    label: "周活跃度",
  },
  {
    key: "month",
    label: "月活跃度",
  },
  {
    key: "quarter",
    label: "季活跃度",
  },
  {
    key: "year",
    label: "年活跃度",
  },
];

const dropdownMap = (key: string) => {
  switch (key) {
    case "day":
      return "日活跃度";
    case "week":
      return "周活跃度";
    case "month":
      return "月活跃度";
    case "quarter":
      return "季活跃度";
    case "year":
      return "年活跃度";
    default:
      return "日活跃度";
  }
};

const DashboardView: React.FC = () => {
  const userActivationRef = useRef<HTMLDivElement | null>(null);
  const dataRef = useRef<HTMLDivElement | null>(null);
  const reviewRef = useRef<HTMLDivElement | null>(null);
  const [userInfo, setUserInfo] = useState<ResultMap>();
  const [userInfoFilter, serUserInfoFilter] = useState<string>("day");
  const [reviewInfo, setReviewInfo] = useState<ResultMap>();
  const [reviewInfoFilter, serReviewInfoFilter] = useState<string>("day");
  const [dataInfo, setDataInfo] = useState<ResultMap>();
  const [dataInfoFilter, serDataInfoFilter] = useState<string>("day");
  const [domainData, setDomainData] = useState<OutputData[]>([]);

  function transformData(input: InputData): OutputData[] {
    const transformedData: OutputData[] = [];

    // 将对象转换成数组，并按照 value 降序排序
    const sortedData = Object.entries(input)
      .map(([text, value]) => ({ text, value }))
      .sort((a, b) => b.value - a.value);

    // 计算每个元素的 value 百分比，将其添加到 transformedData
    let totalValue = 0;
    for (const item of sortedData) {
      totalValue += item.value;
    }

    for (const item of sortedData) {
      const percentage = (item.value / totalValue) * 100;
      transformedData.push({
        text: item.text,
        value: Math.round(percentage),
      });
    }

    return transformedData;
  }

  useEffect(() => {
    getAnalyseUserInfo().then((res) => {
      if (res?.data?.data) setUserInfo(res.data.data.resultMap);
    });

    getAnalysDataReview().then((res) => {
      if (res?.data?.data) setReviewInfo(res.data.data.resultMap);
    });
    getAnalysDataGenerate().then((res) => {
      if (res?.data?.data) setDataInfo(res.data.data.resultMap);
    });

    getDomain().then(res => {
      if (res?.data?.data)
        setDomainData(transformData(res.data.data))
    })
  }, []);

  useEffect(() => {
    initUserInfoChart();
  }, [userInfo, userInfoFilter]);

  useEffect(() => {
    initReviewInfoChart();
  }, [reviewInfo, reviewInfoFilter]);

  useEffect(() => {
    initDataInfoChart();
  }, [dataInfo, dataInfoFilter]);

  const initUserInfoChart = () => {
    if (userActivationRef.current) {
      let dataList = [];
      let numList = [];
      if (userInfo && (userInfo as any)[userInfoFilter]) {
        const map = (userInfo as any)[userInfoFilter];
        dataList = map.dateList.map((item: any) => item);
        numList = map.numList.map((item: any) => item);
      }
      const myChart = echarts.init(userActivationRef.current);
      // const dataChart = echarts.init(dataRef.current);
      // const reviewChart = echarts.init(reviewRef.current);

      const option = {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: dataList,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: numList,
            type: "line",
            itemStyle: {
              color: '#0FB698', // 设置点的颜色
            },
            lineStyle: {
              color: '#0FB698'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0FB698",
                },
                {
                  offset: 1,
                  color: "rgba(15, 182, 152, 0.20)",
                },
              ]),
            },
          },
        ],
      };

      myChart.setOption(option);
      // dataChart.setOption(option);
      // reviewChart.setOption(option);

      // Cleanup when the component unmounts
      return () => {
        myChart.dispose();
        // dataChart.dispose();
        // reviewChart.dispose();
      };
    }
  };

  const initReviewInfoChart = () => {
    if (reviewRef.current) {
      let dataList = [];
      let numList = [];
      if (reviewInfo && (reviewInfo as any)[reviewInfoFilter]) {
        const map = (reviewInfo as any)[reviewInfoFilter];
        dataList = map.dateList.map((item: any) => item);
        numList = map.numList.map((item: any) => item);
      }
      //   const myChart = echarts.init(userActivationRef.current);
      // const dataChart = echarts.init(dataRef.current);
      const reviewChart = echarts.init(reviewRef.current);

      const option = {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: dataList,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: numList,
            type: "line",
            itemStyle: {
              color: '#0FB698', // 设置点的颜色
            },
            lineStyle: {
              color: '#0FB698'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0FB698",
                },
                {
                  offset: 1,
                  color: "rgba(15, 182, 152, 0.20)",
                },
              ]),
            },
          },
        ],
      };

      reviewChart.setOption(option);
      // dataChart.setOption(option);
      // reviewChart.setOption(option);

      // Cleanup when the component unmounts
      return () => {
        reviewChart.dispose();
        // dataChart.dispose();
        // reviewChart.dispose();
      };
    }
  };

  const initDataInfoChart = () => {
    if (dataRef.current) {
      let dataList = [];
      let numList = [];
      if (reviewInfo && (reviewInfo as any)[reviewInfoFilter]) {
        const map = (reviewInfo as any)[reviewInfoFilter];
        dataList = map.dateList.map((item: any) => item);
        numList = map.numList.map((item: any) => item);
      }
      //   const myChart = echarts.init(userActivationRef.current);
      const dataChart = echarts.init(dataRef.current);
      //   const reviewChart = echarts.init(reviewRef.current);

      const option = {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: dataList,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: numList,
            type: "line",
            itemStyle: {
              color: '#0FB698', // 设置点的颜色
            },
            lineStyle: {
              color: '#0FB698'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0FB698",
                },
                {
                  offset: 1,
                  color: "rgba(15, 182, 152, 0.20)",
                },
              ]),
            },
          },
        ],
      };

      dataChart.setOption(option);
      // dataChart.setOption(option);
      // reviewChart.setOption(option);

      // Cleanup when the component unmounts
      return () => {
        dataChart.dispose();
        // dataChart.dispose();
        // reviewChart.dispose();
      };
    }
  };

  const onUserInfoClick: MenuProps["onClick"] = ({ key }) => {
    serUserInfoFilter(key);
  };

  const onReviewInfoClick: MenuProps["onClick"] = ({ key }) => {
    serReviewInfoFilter(key);
  };

  const onDataClick: MenuProps["onClick"] = ({ key }) => {
    serDataInfoFilter(key);
  };

  const DataDashboard: React.FC = () => {
    const intervalRef = useRef<ReturnType<typeof setInterval>>();
    const [overviewData, serOverviewData] = useState<OverviewData>();
    const getOverviewData = () => {
      getOverview().then((res) => {
        if (res.data?.code === 200) {
          serOverviewData(res.data.data);
        }
      });
    }
    useEffect(() => {
      clearInterval(intervalRef.current);
      getOverviewData();
      intervalRef.current = setInterval(getOverviewData, 5000);
      return () => {
        if (intervalRef.current)
          clearInterval(intervalRef.current); // 在组件卸载时清除定时器
      };
    }, []);
    return <div
      className="createTaskArea"
      style={{
        height: "90px",
        minHeight: "unset",
        display: "flex",
        flexDirection: "row",
      }}
    >
      <Space size={102}>
        <label
          className="mediumText"
          style={{ fontSize: 16, color: "#111" }}
        >
          数据总览
        </label>
        <Space
          split={
            <Divider
              type="vertical"
              style={{ height: 40, backgroundColor: "#E1F3FB" }}
            />
          }
          size={65}
        >
          <Space size={50}>
            <label
              className="boldText"
              style={{ fontSize: 16, color: "#000" }}
            >
              用户总数
            </label>
            <Statistic
              value={overviewData?.totalUserNum || 0}
              valueStyle={statisticStyle}
            />
          </Space>
          <Space size={50}>
            <label
              className="boldText"
              style={{ fontSize: 16, color: "#000" }}
            >
              在线人数
            </label>
            <Statistic
              value={overviewData?.onlineUserNum || 0}
              valueStyle={statisticStyle}
            />
          </Space>
          <Space size={50}>
            <label
              className="boldText"
              style={{ fontSize: 16, color: "#000" }}
            >
              数据生成总数
            </label>
            <Statistic
              value={overviewData?.dataGenerateNum || 0}
              valueStyle={statisticStyle}
            />
          </Space>
          <Space size={50}>
            <label
              className="boldText"
              style={{ fontSize: 16, color: "#000" }}
            >
              数据审核总数
            </label>
            <Statistic
              value={overviewData?.dataReviewNum || 0}
              valueStyle={statisticStyle}
            />
          </Space>
        </Space>
      </Space>
    </div>
  }
  return (
    <>
      <Scrollbar>
        <div className="createTaskContent" style={{ width: "76.3%" }}>
          <div
            style={{
              fontSize: "28px",
              lineHeight: "36px",
              fontWeight: "500",
              marginLeft: "2rem",
            }}
            className="mediumText"
          >
            数据看板
          </div>
          <DataDashboard />
          <Row gutter={24}>
            <Col span={12}>
              <div
                style={{
                  borderRadius: 24,
                  background: "#fff",
                  height: 430,
                  padding: "20px 48px",
                  textAlign: "center",
                }}
              >
                <label
                  className="mediumText"
                  style={{ fontSize: 16, color: "#111" }}
                >
                  用户活跃度
                </label>
                <div style={{ textAlign: "end" }}>
                  <Dropdown
                    menu={{
                      items: filterDropdownItems,
                      onClick: onUserInfoClick,
                    }}
                  >
                    <Button
                      className="default-btn"
                    // style={{ width: "124px", height: "40px", fontSize: "14px" }}
                    // style={{ width: reviewFilterType === QAFilter.All ? 124 : 184, height: "40px", fontSize: "14px", justifyContent: 'center' }}
                    >
                      {dropdownMap(userInfoFilter)}
                      <CaretDownOutlined />
                    </Button>
                  </Dropdown>
                </div>
                <div
                  ref={userActivationRef}
                  style={{ width: "100%", height: "400px" }}
                ></div>
              </div>
            </Col>
            <Col span={12}>
              <div
                style={{
                  borderRadius: 24,
                  background: "#fff",
                  height: 430,
                  padding: "20px 48px",
                  textAlign: "center",
                }}
              >
                <label
                  className="mediumText"
                  style={{ fontSize: 16, color: "#111" }}
                >
                  领域词云
                </label>
                <WordCloud
                  words={domainData}
                  options={{
                    fontSizes: [20, 60],
                    rotations: 0,
                    rotationAngles: [0, 90],
                  }}
                />
              </div>
            </Col>
          </Row>

          <Row gutter={24} style={{ marginTop: "2rem" }}>
            <Col span={12}>
              <div
                style={{
                  borderRadius: 24,
                  background: "#fff",
                  height: 430,
                  padding: "20px 48px",
                  textAlign: "center",
                }}
              >
                <label
                  className="mediumText"
                  style={{ fontSize: 16, color: "#111" }}
                >
                  生成数据统计
                </label>
                <div style={{ textAlign: "end" }}>
                  <Dropdown
                    menu={{
                      items: filterDropdownItems,
                      onClick: onDataClick,
                    }}
                  >
                    <Button className="default-btn">
                      {dropdownMap(dataInfoFilter)}
                      <CaretDownOutlined />
                    </Button>
                  </Dropdown>
                </div>
                <div
                  ref={dataRef}
                  style={{ width: "100%", height: "400px" }}
                ></div>
              </div>
            </Col>
            <Col span={12}>
              <div
                style={{
                  borderRadius: 24,
                  background: "#fff",
                  height: 430,
                  padding: "20px 48px",
                  textAlign: "center",
                }}
              >
                <label
                  className="mediumText"
                  style={{ fontSize: 16, color: "#111" }}
                >
                  审核数据统计
                </label>
                <div style={{ textAlign: "end" }}>
                  <Dropdown
                    menu={{
                      items: filterDropdownItems,
                      onClick: onReviewInfoClick,
                    }}
                  >
                    <Button className="default-btn">
                      {dropdownMap(reviewInfoFilter)}
                      <CaretDownOutlined />
                    </Button>
                  </Dropdown>
                </div>
                <div
                  ref={reviewRef}
                  style={{ width: "100%", height: "400px" }}
                ></div>
              </div>
            </Col>
          </Row>
        </div>
      </Scrollbar>
      {/* <div style={{ width: '76.3%', margin: 'auto', textAlign: 'start' }}>
            <label className="mediumText" style={{
                fontSize: 28,
                fontWeight: 500,
                lineHeight: 36
            }}>数据看板</label>
            <div style={{ width: '100%', height: 90, backgroundColor: '#fff' }}>
                <Space>
                    <label>数据总览</label>
                    <span>
                        <label>用户总数</label>
                        <label>200,000</label>
                    </span>
                </Space>
            </div>
        </div> */}
    </>
  );
};
export default DashboardView;
