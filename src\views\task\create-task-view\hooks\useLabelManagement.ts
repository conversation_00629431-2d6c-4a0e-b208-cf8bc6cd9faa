import { useState, ChangeEvent } from 'react';
import { message } from 'antd';
import { getLabelList } from '../../../../api/task';
import { DatasetFile } from '../types';
import { DEFAULT_VALUES, MESSAGES, VALIDATION_RULES } from '../constants';

/**
 * 标签管理Hook
 */
export const useLabelManagement = () => {
  const [autoLabel, setAutoLabel] = useState(DEFAULT_VALUES.AUTO_LABEL);
  const [labelText, setLabelText] = useState(DEFAULT_VALUES.LABEL_TEXT);
  const [isGeneratingLabels, setIsGeneratingLabels] = useState(false);
  const [AILabels, setAILabels] = useState<string[]>([]);

  // 自动标签开关变化
  const handleAutoChange = (checked: boolean) => {
    setAutoLabel(checked);
    if (!checked) {
      setLabelText(DEFAULT_VALUES.LABEL_TEXT);
    }
  };

  // 标签文本变化处理
  const handleLabelChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    let inputValue = e.target.value;
    inputValue = inputValue.replace(/\s*，\s*/g, '，');
    inputValue = inputValue.replace(/，{2,}/g, '，');
    if (inputValue.trimStart().startsWith('，')) {
      const firstCommaIndex = inputValue.indexOf('，');
      if (firstCommaIndex !== -1) {
        inputValue =
          inputValue.substring(0, firstCommaIndex) + inputValue.substring(firstCommaIndex + 1);
      }
    }
    setLabelText(inputValue.trim());
  };

  // AI智能生成标签
  const handleAIGenerate = async (fileList: DatasetFile[]) => {
    if (fileList.length === 0) {
      message.warning(MESSAGES.WARNING.SELECT_DATASET_FIRST);
      return;
    }
    const currentLabelCount = labelText ? labelText.split('，').length : 0;
    if (currentLabelCount >= VALIDATION_RULES.LABEL.MAX_COUNT) {
      message.warning(MESSAGES.WARNING.LABEL_COUNT_LIMIT);
      return;
    }

    setIsGeneratingLabels(true);

    try {
      const datasetIds = fileList.map((file) => file.dataSetId);
      const res = await getLabelList(datasetIds);

      if (res.data?.code === 200) {
        const labels = res.data.data;
        setAILabels(labels);
        const uniqueLabels = labels.filter((tag: any) => tag);

        if (uniqueLabels.length === 0) {
          message.warning(MESSAGES.WARNING.NO_RECOMMENDED_LABELS);
        } else {
          const currentLabels = labelText ? labelText.split('，') : [];
          const mergedSet = new Set<string>();
          currentLabels.forEach((label) => mergedSet.add(label));
          uniqueLabels.forEach((label: string) => mergedSet.add(label));

          let mergedLabels = Array.from(mergedSet);
          if (mergedLabels.length < VALIDATION_RULES.LABEL.MAX_COUNT && uniqueLabels.length > 0) {
            const neededLabels = VALIDATION_RULES.LABEL.MAX_COUNT - mergedLabels.length;
            const availableLabels = uniqueLabels.filter((label: string) => !mergedSet.has(label));
            const shuffled = [...availableLabels].sort(() => 0.5 - Math.random());
            const additionalLabels = shuffled?.slice(0, Math.min(neededLabels, shuffled.length));
            mergedLabels = [...mergedLabels, ...additionalLabels];
          }
          const finalLabels = mergedLabels?.slice(0, VALIDATION_RULES.LABEL.MAX_COUNT);
          const newLabelText = finalLabels.join('，');
          setLabelText(newLabelText);
          message.success(MESSAGES.SUCCESS.LABEL_GENERATED);
        }
      } else {
        message.error(res.data?.message || MESSAGES.ERROR.LABEL_GENERATION_FAILED);
      }
    } catch (error) {
      message.error(MESSAGES.ERROR.LABEL_GENERATION_FAILED);
      console.error('生成标签失败:', error);
    } finally {
      setIsGeneratingLabels(false);
    }
  };

  // 验证标签格式
  const validateLabels = (): boolean => {
    if (!VALIDATION_RULES.LABEL.PATTERN.test(labelText)) {
      message.error(MESSAGES.ERROR.INVALID_LABEL_FORMAT);
      return false;
    }
    return true;
  };

  return {
    autoLabel,
    labelText,
    isGeneratingLabels,
    AILabels,
    handleAutoChange,
    handleAutoLabelChange: handleAutoChange,
    handleLabelChange,
    handleLabelTextChange: handleLabelChange,
    handleAIGenerate,
    validateLabels,
  };
};
