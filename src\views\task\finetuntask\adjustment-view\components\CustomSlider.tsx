// CustomSlider.tsx
import { Slider, Tooltip } from 'antd';
import { ReactNode } from 'react';
import { SliderMarks } from "antd/es/slider";
import group151 from "../../../../../assets/img/group-151.svg";
import "../../../../../css/AdjustmentView.css";
interface SliderConfig {
  key: string;
  label: string;
  tooltipTitle: string;
  formatter: (value: number|undefined) => ReactNode;
  value: number;
  onChange: (value: number) => void;
  disabled?: boolean;
  SiderMark:SliderMarks,

}

const CustomSlider: React.FC<SliderConfig> = ({
  label,
  tooltipTitle,
  formatter,
  value,
  onChange,
  disabled = false,
  SiderMark,
}) => {

  return (
    <div className="slider-container">
      <div className="temperatruelabel">
        <span >{label}</span>
        <Tooltip title={tooltipTitle}>
        <img className="frame-child179" src={group151} />
        </Tooltip>
      </div>
      <Slider
        value={value}
        className="create-tasks-slider"
        onChange={onChange}
        marks={SiderMark}
        step={25}
        dots
        tooltip={{ formatter }}
        disabled={disabled}
        style={{
            width: "67.8%",
            display: "inline-flex",
            margin: "unset",
          }}
      />
    </div>
  );
};

export default CustomSlider;