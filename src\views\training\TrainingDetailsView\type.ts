export interface TrainingType {
    name: string; // 模型名称
    desc: string;
    datasetName: string;
    testSetRadio: number | any;
    baseModel?: string; // 基座模型
    trainingStrategy: string; // 训练策略
    score?: TrainingScoreType;
    data?: TrainingScoreType;
    modelIntro: string //模型介绍
    interationNumber: number;
    learnRate: number;
    trainProgress: any;
    batchSize: string;
    status: string;
    id: string;
    modelBaseName?: string;
    serverName?: string;
    modelUrl?: string;
}

export interface TrainingScoreType {
    ppl: number;
    rouge1: number;
    rouge2: number;
    bleu1: number;
    bleu2: number;
    bertScore: number;
    predictBleu2: number;
    predictBleu3: number;
    predictBleu4: number;
    predictRouge1: number;
    predictRouge2: number;
    predictRougeL: number;
    basePredictBleu2: number;
    basePredictBleu3: number;
    basePredictBleu4: number;
    basePredictRouge1: number;
    basePredictRouge2: number;
    basePredictRougeL: number;
}

export interface TariningSampleType {
    question: string;
    text: string;
    answer: string;
    score: number;
}

