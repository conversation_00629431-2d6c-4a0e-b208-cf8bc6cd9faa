import React from 'react';
import { Input, Select, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { SearchFilterProps } from '../types';
import { FILTER_OPTIONS, SORT_OPTIONS } from '../constants/task';

const SearchFilter: React.FC<SearchFilterProps> = ({
  filterAttribute,
  sortDirection,
  filterInput,
  onFilterAttributeChange,
  onSortDirectionChange,
  onFilterInputChange,
  onSearch,
}) => {
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      onSearch();
    }
  };

  return (
    <Space size={10}>
      <Space.Compact>
        <Select
          size="large"
          className="filter-select"
          value={filterAttribute}
          onChange={onFilterAttributeChange}
          options={FILTER_OPTIONS}
        />
        <Input
          size="large"
          className="filter-input"
          suffix={<SearchOutlined />}
          placeholder="请输入任务名称"
          value={filterInput}
          onChange={(e) => onFilterInputChange(e.target.value)}
          onKeyUp={handleKeyPress}
        />
      </Space.Compact>
      <Select
        size="large"
        className="filter-select"
        value={sortDirection}
        onChange={onSortDirectionChange}
        style={{ width: '9.75rem' }}
        options={SORT_OPTIONS}
      />
    </Space>
  );
};

export default SearchFilter;
