<svg width="1920" height="1310" viewBox="0 0 1920 1310" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_444_53" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1920" height="1310">
<path d="M0.5 48C0.5 21.7665 21.7665 0.5 48 0.5H1872C1898.23 0.5 1919.5 21.7665 1919.5 48V1309.5H0.5V48Z" fill="black" stroke="white"/>
</mask>
<g mask="url(#mask0_444_53)">
<g filter="url(#filter0_b_444_53)">
<rect width="1920" height="1310" rx="48" fill="url(#paint0_angular_444_53)"/>
</g>
<g filter="url(#filter1_f_444_53)">
<circle cx="1177" cy="939" r="491" fill="url(#paint1_linear_444_53)"/>
</g>
<rect y="864" width="1920" height="446" fill="url(#paint2_linear_444_53)"/>
</g>
<defs>
<filter id="filter0_b_444_53" x="-36" y="-36" width="1992" height="1382" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="18"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_444_53"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_444_53" result="shape"/>
</filter>
<filter id="filter1_f_444_53" x="442" y="204" width="1470" height="1470" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="122" result="effect1_foregroundBlur_444_53"/>
</filter>
<radialGradient id="paint0_angular_444_53" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1043.5 747.185) rotate(176.702) scale(1117.35 1143.94)">
<stop offset="0.0128264" stop-color="#1F2237"/>
<stop offset="0.203125" stop-color="#111111"/>
<stop offset="0.357649" stop-color="#071114"/>
<stop offset="0.497265"/>
<stop offset="0.587402" stop-color="#004549"/>
<stop offset="0.713542" stop-color="white"/>
<stop offset="0.875" stop-color="#903EF9"/>
<stop offset="0.945401" stop-color="#7A82C1"/>
</radialGradient>
<linearGradient id="paint1_linear_444_53" x1="1243.54" y1="554.689" x2="842.019" y2="1008.98" gradientUnits="userSpaceOnUse">
<stop stop-color="#111111"/>
<stop offset="0.370581" stop-color="#222F52"/>
<stop offset="0.807289" stop-color="#2AC0A5"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_444_53" x1="960" y1="864" x2="960" y2="1310" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0"/>
<stop offset="1"/>
</linearGradient>
</defs>
</svg>
