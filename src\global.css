/* @import url("https://fonts.googleapis.com/css2?family=HarmonyOS+Sans+SC:wght@300;400;500;700;900&display=swap"); */
body {
  margin: 0;
  line-height: normal;
}
:root {
  /* fonts */
  --font-harmonyos-sans-sc: "HarmonyOS Sans SC";

  /* font sizes */
  --font-size-sm: 0.88rem;
  --font-size-base: 1rem;
  --font-size-7xl: 1.63rem;
  --font-size-9xl: 1.75rem;
  --font-size-13xl: 2rem;
  --font-size-xs: 0.75rem;
  --font-size-45xl: 4rem;
  --font-size-3xl: 1.38rem;
  --font-size-5xl: 1.5rem;
  --font-size-xl: 1.25rem;

  /* Colors */
  --color-white: #fff;
  --color-whitesmoke-100: #f4f7fa;
  --color-whitesmoke-200: #f1f6f9;
  --color-whitesmoke-300: #eef1f5;
  --color-whitesmoke-400: #eee;
  --color-whitesmoke-500: rgba(244, 247, 250, 0.01);
  --color-gray-100: #fafcfd;
  --color-gray-200: #f7fbfd;
  --color-gray-300: #888;
  --color-gray-400: #111;
  --color-gray-500: rgba(0, 0, 0, 0.3);
  --color-mediumaquamarine-100: #19c1a3;
  --color-mediumaquamarine-200: #0fb799;
  --color-mediumaquamarine-300: #0fb698;
  --color-mediumaquamarine-400: rgba(25, 193, 163, 0.03);
  --color-mediumaquamarine-500: rgba(15, 182, 152, 0.1);
  --color-mediumaquamarine-600: rgba(15, 182, 152, 0.3);
  --color-steelblue-100: #6d89b2;
  --color-darkslategray-100: #444;
  --color-darkslategray-200: #373737;
  --color-darkslategray-300: #292f38;
  --color-slategray-100: #6a7689;
  --color-slategray-200: rgba(105, 120, 131, 0.6);
  --color-black: #111111;
  --color-teal-100: #056f85;
  --color-mediumspringgreen: #23ffbb;
  --color-lightsteelblue-100: #b7c3d5;
  --color-lightslategray-100: #8e98a7;
  --color-lightslategray-200: #7886aa;
  --color-gainsboro-100: #d7dde7;
  --color-gainsboro-200: #d9d9d9;
  --color-dimgray: #6d7279;
  --color-crimson: #da2a2a;
  --color-goldenrod: #eba900;
  --color-skyblue: rgba(111, 169, 201, 0.1);
  --color-mediumpurple-100: #867acd;
  --color-mediumpurple-200: rgba(134, 122, 205, 0.1);
  --color-yellowgreen: #79bd07;

  /* Gaps */
  --gap-5xs: 0.5rem;
  --gap-45xl: 4rem;
  --gap-6xs: 0.44rem;
  --gap-base: 1rem;
  --gap-173xl: 12rem;
  --gap-9xs: 0.25rem;
  --gap-7xl: 1.63rem;
  --gap-29xl: 3rem;
  --gap-5xl: 1.5rem;
  --gap-12xs: 0.06rem;
  --gap-xs: 0.75rem;
  --gap-27xl: 2.88rem;
  --gap-25xl: 2.75rem;
  --gap-7xs: 0.38rem;

  /* Paddings */
  --padding-12xs-6: 0.04rem;
  --padding-11xs-2: 0.08rem;
  --padding-base: 1rem;
  --padding-10xs: 0.19rem;
  --padding-xs: 0.75rem;
  --padding-3xs: 0.63rem;

  /* Border radiuses */
  --br-5xl: 24px;
  --br-12xs: 1px;
  --br-5xs: 8px;
  --br-26xl: 45px;
  --br-11xs: 2px;
  --br-lgi: 19px;
  --br-12xs-5: 0.5px;
  --br-20xl: 39px;
  --br-40xl: 59px;
  --br-base: 16px;
  --br-9xs: 4px;
  --br-44xl: 63px;
  --br-13xl: 32px;
  --br-22xl: 41px;
  --br-45xl: 64px;
  --br-29xl: 48px;
}
