import React from 'react';
import { TreeSelect, List } from 'antd';
import { DataNode } from 'antd/es/tree';
import Scrollbar from 'react-scrollbars-custom';
import { QADocument } from '../../../types';
import { ReviewConfigBtnType } from '../../../components/ReviewConfigModal/components/ReviewConfigBtn/type';

interface QAListSidebarProps {
  treeSelectValue: string | undefined;
  treeData: DataNode[];
  displayQaList: QADocument[];
  reviewId: string;
  scoreButtonInfo: ReviewConfigBtnType[];
  onTreeSelectChange: (value: any, node: any) => void;
  onTreeSelectValueChange: (value: string | undefined) => void;
  onQuestionSelect: (item: QADocument) => void;
  onRefresh: () => void;
}

const QAListSidebar: React.FC<QAListSidebarProps> = ({
  treeSelectValue,
  treeData,
  displayQaList,
  reviewId,
  scoreButtonInfo,
  onTreeSelectChange,
  onTreeSelectValueChange,
  onQuestionSelect,
  onRefresh,
}) => {
  const getBorderStyle = (item: QADocument) => {
    let color = 'white';
    scoreButtonInfo?.forEach((info) => {
      if (info.value === item.score) {
        color = info.color;
      }
    });
    return `3px solid ${color}`;
  };

  return (
    <div
      style={{
        width: '300px',
        padding: '0 0 1.25rem 1rem',
        borderLeft: '1px solid #EEF1F5',
      }}
    >
      <TreeSelect
        showSearch
        style={{
          width: '100%',
          height: '40px',
          marginBottom: '20px',
        }}
        value={treeSelectValue}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        allowClear
        labelInValue
        treeDefaultExpandAll
        onChange={(newValue) => {
          onTreeSelectValueChange(newValue);
        }}
        //清除
        onClear={() => {
          onTreeSelectValueChange(undefined);
          //调用接口getQAs
          onRefresh();
        }}
        onSelect={onTreeSelectChange}
        treeData={treeData}
        fieldNames={{
          label: 'name',
          value: 'fileId',
          children: 'children',
        }}
      />
      <Scrollbar style={{ height: 588 }}>
        <List
          className="qa-list"
          size="small"
          dataSource={displayQaList}
          bordered={false}
          renderItem={(item, index) => (
            <List.Item
              key={item.id}
              style={{
                textAlign: 'start',
                borderLeft: getBorderStyle(item),
              }}
              onClick={() => {
                onQuestionSelect(item);
              }}
            >
              <div className="review-qa-list">
                <label className={item.id === reviewId ? 'current-qa' : ''}>{item.question}</label>
              </div>
            </List.Item>
          )}
        />
      </Scrollbar>
    </div>
  );
};

export default QAListSidebar;
