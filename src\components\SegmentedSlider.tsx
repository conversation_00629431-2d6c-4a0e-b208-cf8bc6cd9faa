import { useState } from "react";
import { MenuOutlined } from "@ant-design/icons";
interface SegmentedSliderProps {
  segmentedSliders: number[];
  onChange: (value: number) => void;
  value: number;
  showLabel?: boolean;
  color?: string;
  width?: string | number;
}
const SegmentedSlider: React.FC<SegmentedSliderProps> = ({
  segmentedSliders,
  onChange,
  value,
  showLabel,
  color,
  width,
}) => {
  const index = segmentedSliders.indexOf(value);
  const [activeNode, setActiveNode] = useState(index);

  function getNodeClass(index: number) {
    if (index < activeNode) {
      return "sliderNode active-node";
    } else if (index === activeNode) {
      return "sliderNode current-active-node";
    } else {
      return "sliderNode";
    }
  }

  function getTrackWidth() {
    if (width) {
      return `calc((${width} - ${segmentedSliders.length * 18}px)/${
        segmentedSliders.length - 1
      })`;
    } else {
      return "100px";
    }
  }

  function getTrackClass(index: number) {
    if (index + 1 <= activeNode) {
      return "sliderTrack active-track";
    } else {
      return "sliderTrack";
    }
  }

  return (
    <div className="segmentedSlider">
      {segmentedSliders.map((item, index) => {
        if (index !== segmentedSliders.length - 1) {
          return (
            <div className="segmentedSlider" key={"slider" + index}>
              <div
                draggable={index === activeNode}
                key={"node" + index}
                className={getNodeClass(index)}
                style={{
                  backgroundColor:
                    color && index <= activeNode ? color : undefined,
                  border: color && index <= activeNode ? color : undefined,
                  boxShadow:
                    color && index === activeNode
                      ? `0px 2px 6px ${color}`
                      : undefined,
                }}
                onClick={() => {
                  setActiveNode(index);
                  onChange(item);
                }}
              >
                {index === activeNode ? (
                  <MenuOutlined style={{ color: "white", fontSize: "8px" }} />
                ) : null}
              </div>
              <div
                className={getTrackClass(index)}
                key={"track" + index}
                style={{
                  width: getTrackWidth(),
                  backgroundColor:
                    color && index + 1 <= activeNode ? color : undefined,
                  border: color && index + 1 <= activeNode ? color : undefined,
                }}
              ></div>
            </div>
          );
        } else {
          return (
            <div
              draggable={index === activeNode}
              key={"node" + index}
              className={getNodeClass(index)}
              style={{
                backgroundColor:
                  color && index <= activeNode ? color : undefined,
                border: color && index <= activeNode ? color : undefined,
                boxShadow:
                  color && index === activeNode
                    ? `0px 2px 6px ${color}`
                    : undefined,
              }}
              onClick={() => {
                setActiveNode(index);
                onChange(item);
              }}
            >
              {index === activeNode ? (
                <MenuOutlined style={{ color: "white", fontSize: "8px" }} />
              ) : null}
            </div>
          );
        }
      })}
    </div>
  );
};

export default SegmentedSlider;
