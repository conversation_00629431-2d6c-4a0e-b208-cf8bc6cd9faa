import React, { RefObject } from 'react';
import { Button, Form } from 'antd';
import ModelSelect from './ModelSelect';
import TaskSelect from './TaskSelect';
import TrainingSetSelect from './TrainingSetSelect';
import Overview from './Overview';
import { TariningType } from '../type';
import classes from '../index.module.css';

interface TrainingContentProps {
  trainingData: TariningType;
  selectedModelId: string;
  modelSelectRef: RefObject<any>;
  taskSelectRef: RefObject<any>;
  trainingSetSelectRef: RefObject<any>;
  resourceSetSelectRef: RefObject<any>;
  modelSectionRef: RefObject<HTMLDivElement>;
  taskSectionRef: RefObject<HTMLDivElement>;
  trainingSetSectionRef: RefObject<HTMLDivElement>;
  overviewSectionRef: RefObject<HTMLDivElement>;
  onModelSelectChange: (modelId: string) => void;
  onNextClick: () => void;
}

const TrainingContent: React.FC<TrainingContentProps> = ({
  trainingData,
  selectedModelId,
  modelSelectRef,
  taskSelectRef,
  trainingSetSelectRef,
  resourceSetSelectRef,
  modelSectionRef,
  taskSectionRef,
  trainingSetSectionRef,
  overviewSectionRef,
  onModelSelectChange,
  onNextClick,
}) => {
  return (
    <div
      className="createTaskArea"
      style={{
        position: 'relative',
        overflowY: 'auto',
      }}
    >
      <Form>
        <div
          id="model-select-section"
          ref={modelSectionRef}
          style={{
            marginBottom: '60px',
            borderBottom: '1px solid #D9D9D9',
            padding: '20px',
          }}
        >
          <ModelSelect
            ref={modelSelectRef}
            modelData={trainingData}
            onModelSelectChange={onModelSelectChange}
          />
        </div>
        
        <div
          id="task-select-section"
          ref={taskSectionRef}
          style={{
            marginBottom: '60px',
            borderBottom: '1px solid #D9D9D9',
            padding: '20px',
          }}
        >
          <TaskSelect ref={taskSelectRef} modelData={trainingData} />
        </div>
        
        <div
          id="training-set-section"
          ref={trainingSetSectionRef}
          style={{
            marginBottom: '60px',
            borderBottom: '1px solid #D9D9D9',
            padding: '20px 20px 72px 20px',
          }}
        >
          <TrainingSetSelect ref={trainingSetSelectRef} modelData={trainingData} />
        </div>
        
        <div
          id="overview-section"
          ref={overviewSectionRef}
          style={{ marginBottom: '60px', padding: '20px' }}
        >
          <Overview
            ref={resourceSetSelectRef}
            trainingData={trainingData}
            selectedModelId={selectedModelId}
          />
        </div>

        <div className={classes['pre-next-div']}>
          <Button
            type="primary"
            className={`primary-btn ${classes['next-btn']}`}
            onClick={onNextClick}
          >
            下一步
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default TrainingContent;
