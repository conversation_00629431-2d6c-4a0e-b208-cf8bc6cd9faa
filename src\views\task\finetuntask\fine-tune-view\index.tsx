import { DescriptionsProps, Progress, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import '@/css/FineTuneView.css';
import '@/css/SourceDatasetView.css';
import { Scrollbar } from 'react-scrollbars-custom';
import { useNavigate } from 'react-router-dom';
import { ModelSetType, ModelDetailType } from '@/types';
import {
  GetModlesParams,
  getModelDetail,
  getModelSets,
  onlineModel,
  getModelConfig,
  getModelConfigByFailId,
  deleteModel,
  deleteBaseModel,
} from '@/api/modle';
import { status } from 'nprogress';
import { useDispatch } from 'react-redux';
import { ThunkDispatch } from 'redux-thunk';
import { AnyAction } from 'redux';
import Header, { tooltip } from './components/Header';
import BaseModelSection from './components/BaseModelSection';
import MyModelSection from './components/MyModelSection';
import ModalCollection from './components/ModalCollection';
import { getDescItems, getInterruptItems, getTrainItems } from './modelDescriptions';
const FineTuneView: React.FC = () => {
  const [openDrawView, setOpenDrawView] = useState(false);
  const [myopenDrawView, setMyOpenDrawView] = useState(false);
  const [interruptDrawView, setInterruptDrawView] = useState(false);
  const [online, setOnline] = useState(false);
  const [prolong, setProlong] = useState(false);
  const [offmodel, setOffmodel] = useState(false);
  const [cancelTrain, setCancelTrain] = useState(false);
  const [titltext, setTitltext] = useState('');
  const [modelId, setModelId] = useState('');
  const [mytitltext, setMytitltext] = useState('');
  const [deleteModelDialog, setDeleteModelDialog] = useState(false);
  const navigate = useNavigate();
  const [modelDatasets, setModelDatasets] = useState<ModelSetType[]>([]);
  const [myModelDatasets, setMyModelDatasets] = useState<ModelSetType[]>([]);
  const [modelDetail, setModelDetail] = useState<any>(new Object() as ModelDetailType);
  const [modelServerDetail, setModelServerDetail] = useState<any>(new Object() as ModelDetailType);
  const [myModelDetail, setMyModelDetail] = useState<ModelDetailType>(
    new Object() as ModelDetailType
  );
  const [tarinDetial, setTrainDetial] = useState<any>(new Object() as ModelDetailType);
  // 定义分页数据
  const [pagination, setPagination] = useState({
    page: 1,
    size: 20,
    total: 0,
  });
  const [sortAttribute, setSortAttribute] = useState('modelName');
  const [sortDirection, setSortDirection] = useState('');
  const [sortMyModel, setSortMyModel] = useState('');
  const [apiFlg, setApiFlg] = useState(false);
  const [filterInput, setFilterInput] = useState('');
  const [filterMyInput, setFilterMyInput] = useState('');
  const [myModelStatus, setMyModelStatus] = useState('');
  const [tableIndex, setTableIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timer>();
  const [timeValue, setTimeValue] = useState<any>(1);
  const [modalTitle, setModalTitle] = useState('');
  const [lightWeight, setLightWeight] = useState(false);
  const [lightWeightState, setLightWeightState] = useState({});
  const [currentItemId, setCurrentItemId] = useState<number>(0);
  // 是否重新训练
  const [showRetrain, setShowRetrain] = useState<{ [key: string]: boolean }>({});
  // 是否停止训练
  const [showInterrupt, setShowInterrupt] = useState<{
    [key: string]: boolean;
  }>({});
  const buttonText = '微调训练';
  const moduletext = modelDetail.modelName;

  const confirmLightWeight = (id: any) => {
    setLightWeightState((prevState) => ({
      ...prevState,
      [id]: true, // 标记该 id 为轻量化
    }));
  };
  const onClickDetail = (id: string) => {
    const selectedModel = data.find((item) => item.id === id);
    const modelName = selectedModel ? selectedModel.modelName : '';
    const modelId = selectedModel ? selectedModel.id : '';
    setTitltext(modelName);
    setModelId(modelId);
  };

  const onClickMyDetail = (id: string) => {
    const selectedModel = mydata.find((item) => item.id === id);
    const modelName = selectedModel ? selectedModel.modelName : '';
    const modelId = selectedModel ? selectedModel.id : '';
    setMytitltext(modelName);
    setModelId(modelId);
  };

  function getModels() {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetModlesParams = {
        ...pagination,
        category: 0,
        sortAttribute,
        sortDirection: sortDirection,
        modelName: sortAttribute === 'modelName' ? filterInput : '',
        status: '',
        // modelCategory: selectModelDate === "BASE_MODEL" ? "BASE_MODEL" : "FINE_TUNING"
      };
      getModelSets(params).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          setModelDatasets(res.data?.data);
          setPagination({ ...pagination, total: res.data?.totalCount });
          setTableIndex((pagination.page - 1) * pagination.size);
        }
      });
    }
  }

  function getMyModels() {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetModlesParams = {
        ...pagination,
        category: 1,
        sortAttribute,
        sortDirection: sortMyModel,
        modelName: sortAttribute === 'modelName' ? filterMyInput : '',
        status: myModelStatus,
        // modelCategory: selectMyDataSet
      };
      getModelSets(params).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          // const data = res.data?.data.filter(item => item.status !== 0);
          const data = res.data?.data;
          setMyModelDatasets(data);
          // setMyModelDatasets(res.data?.data)
          setPagination({ ...pagination, total: res.data?.totalCount });
          setTableIndex((pagination.page - 1) * pagination.size);
          const foundModel: any = res.data?.data.some((model: any) => model.status === 2);
          if (foundModel) {
            localStorage.setItem('isOnline', '1');
          } else {
            localStorage.setItem('isOnline', '0');
          }
        }
      });
    }
  }

  const getModelsDetail = (modelId: string) => {
    getModelDetail(modelId).then((res) => {
      if (res.data?.code === 200) {
        const modelInfo = res.data?.data.modelInfo;
        const serverResponse = res.data?.data.serverResponse;
        const data = res.data?.data.trainDetail;
        setModelDetail(modelInfo);
        setModelServerDetail(serverResponse);
        setTrainDetial(data);
      }
    });
  };
  // 重新训练
  const useReTrain = (id: string) => {
    const dispatch: ThunkDispatch<{}, {}, AnyAction> = useDispatch();
    const reTrain = (id: string) => {
      getModelConfigByFailId(id).then((res: any) => {
        if (res.data?.code === 200) {
          const data = res.data?.data;
          navigate('/main/finetune/create');
          dispatch({
            type: 'SET_TRAINING_DATA',
            payload: {
              modelId: id,
              modelConfigData: data,
            },
          });
        }
      });
    };
    return reTrain;
  };
  useEffect(() => {
    clearInterval(intervalRef.current as NodeJS.Timeout);
    getModels();
    intervalRef.current = setInterval(getModels, 5000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout); // 在组件卸载时清除定时器
    };
  }, [pagination.page, pagination.size, sortDirection, sortAttribute, filterInput]);

  useEffect(() => {
    clearInterval(intervalRef.current as NodeJS.Timeout);
    getMyModels();
    intervalRef.current = setInterval(getMyModels, 5000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout); // 在组件卸载时清除定时器
    };
  }, [pagination.page, pagination.size, sortMyModel, sortAttribute, myModelStatus, filterMyInput]);
  const data = modelDatasets;
  const mydata = myModelDatasets;

  const handleBaseDelete = (id: string) => {
    deleteBaseModel(id).then((res: any) => {
      if (res?.code === 200) {
        message.success('删除成功');
        getModels();
        setOpenDrawView(false);
      } else {
        message.error('删除失败');
      }
      setDeleteModelDialog(false);
    });
  };
  const handleModelDelete = async (id: string) => {
    try {
      const modelId = Number(id);
      const res = await deleteModel(modelId);
      if (res) {
        message.success('删除成功');
        getMyModels();
        setMyOpenDrawView(false);
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const getModelName = (id: string): string => {
    const selectedModel = data.find((item) => item.id === id);
    const selectedBaseMpdel = mydata.find((item) => item.id === id);
    if (selectedModel) {
      return selectedModel.modelName;
    } else if (selectedBaseMpdel) {
      return selectedBaseMpdel.modelName;
    }
    return '';
  };

  const descItems: DescriptionsProps['items'] = getDescItems(modelDetail, modelServerDetail);
  const trainItems = getTrainItems(modelDetail, tarinDetial, getModelName);
  const interruptItems: DescriptionsProps['items'] = getInterruptItems(
    modelDetail,
    tarinDetial,
    getModelName
  );

  const handleButtonClick = (value: number) => {
    // 父组件的点击按钮处理逻辑
    onlineModel(modelId);
    setTimeValue(value); // 使用传入的value
    setOnline(false); // 关闭模态框
  };
  // 调用useReTrain获取reTrain函数
  const reTrainById = useReTrain('');
  return (
    <Scrollbar>
      <div className="createModelContent">
        <Header />

        <BaseModelSection
          data={data}
          filterInput={filterInput}
          sortDirection={sortDirection}
          onFilterInputChange={setFilterInput}
          onSortDirectionChange={setSortDirection}
          onModelClick={(id) => {
            onClickDetail(id);
            getModelsDetail(id);
            if (!openDrawView) {
              setOpenDrawView(true);
            }
          }}
        />
        <MyModelSection
          data={mydata}
          filterMyInput={filterMyInput}
          sortMyModel={sortMyModel}
          myModelStatus={myModelStatus}
          showRetrain={showRetrain}
          showInterrupt={showInterrupt}
          onFilterMyInputChange={setFilterMyInput}
          onSortMyModelChange={setSortMyModel}
          onMyModelStatusChange={setMyModelStatus}
          onShowRetrainChange={setShowRetrain}
          onShowInterruptChange={setShowInterrupt}
          onModelClick={onClickMyDetail}
          onModelDelete={handleModelDelete}
          onRetrainClick={reTrainById}
          onInterruptClick={(id) => {
            setShowInterrupt((prevShowInterrupt) => ({
              ...prevShowInterrupt,
              [id]: true,
            }));
          }}
          onNavigateToConfig={(id, name, status) => {
            navigate(`/main/finetune/configure/${id}/${name}/${status}`);
          }}
          onNavigateToDetail={(id, name) => {
            navigate(`/main/finetune/detail/${id}/${name}`);
          }}
          onNavigateToAdjustment={(id) => {
            getModelConfig(id).then((res: any) => {
              if (res.data?.code === 200) {
                sessionStorage.setItem('config', JSON.stringify(res.data.data));
              }
            });
            navigate(`/main/finetune/adjustment/${id}`);
          }}
          getModelName={getModelName}
          getModelConfig={getModelConfig}
        />
      </div>
      <ModalCollection
        deleteModelDialog={deleteModelDialog}
        setDeleteModelDialog={setDeleteModelDialog}
        modelId={modelId}
        handleBaseDelete={handleBaseDelete}
        online={online}
        setOnline={setOnline}
        prolong={prolong}
        setProlong={setProlong}
        timeValue={timeValue}
        setTimeValue={setTimeValue}
        handleButtonClick={handleButtonClick}
        lightWeight={lightWeight}
        setLightWeight={setLightWeight}
        currentItemId={currentItemId}
        lightWeightState={lightWeightState}
        confirmLightWeight={confirmLightWeight}
        status={status}
        offmodel={offmodel}
        setOffmodel={setOffmodel}
        cancelTrain={cancelTrain}
        setCancelTrain={setCancelTrain}
        modalTitle={modalTitle}
        openDrawView={openDrawView}
        setOpenDrawView={setOpenDrawView}
        myopenDrawView={myopenDrawView}
        setMyOpenDrawView={setMyOpenDrawView}
        interruptDrawView={interruptDrawView}
        setInterruptDrawView={setInterruptDrawView}
        titltext={titltext}
        mytitltext={mytitltext}
        moduletext={moduletext}
        buttonText={buttonText}
        descItems={descItems}
        trainItems={trainItems}
        interruptItems={interruptItems}
        tooltip={tooltip}
        onNavigateToCreate={() => {
          navigate('/main/finetune/create', {
            state: { modelName: titltext, modelId: modelId },
          });
        }}
        onDeleteClick={() => {
          setDeleteModelDialog(true);
        }}
        getModels={getModels}
      />
    </Scrollbar>
  );
};

export default FineTuneView;
