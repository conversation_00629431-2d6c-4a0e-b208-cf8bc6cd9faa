<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.6" x="21" y="17" width="46" height="58" rx="4" fill="#F8F9FB"/>
<rect opacity="0.6" x="21" y="21" width="46" height="54" rx="4" fill="#EBEFF5"/>
<rect opacity="0.6" x="21" y="25" width="46" height="50" rx="4" fill="#DEE4EE"/>
<g filter="url(#filter0_i_368_2195)">
<path d="M15 37C15 34.7909 16.7909 33 19 33H37.3814C38.6166 33 39.7826 33.5707 40.5403 34.5462L43.5757 38.4538C44.3335 39.4293 45.4994 40 46.7347 40H69C71.2091 40 73 41.7909 73 44V71C73 73.2091 71.2091 75 69 75H19C16.7909 75 15 73.2091 15 71V37Z" fill="url(#paint0_linear_368_2195)"/>
</g>
<path d="M73 21.3227C73.5253 19.9047 74.3433 18.8297 75.454 18.0978C76.5797 17.3659 77.9006 17 79.4165 17C80.4522 17 81.3902 17.1753 82.2308 17.526C83.0863 17.8615 83.7617 18.3571 84.257 19.0127C84.7523 19.6684 85 20.4612 85 21.3914C85 22.0623 84.8724 22.6493 84.6173 23.1525C84.3621 23.6557 84.0619 24.0826 83.7167 24.4333C83.3715 24.784 82.8987 25.1957 82.2983 25.6684C81.758 26.0801 81.3377 26.4307 81.0375 26.7205C80.7523 26.9949 80.5197 27.3227 80.3396 27.7039C80.1745 28.0851 80.122 28.5197 80.182 29.0076H77.2101C77.03 28.474 76.94 27.9936 76.94 27.5667C76.94 26.8348 77.1351 26.2173 77.5253 25.7141C77.9306 25.2109 78.5009 24.6696 79.2364 24.0902C79.7917 23.648 80.2045 23.2745 80.4747 22.9695C80.7599 22.6493 80.9024 22.291 80.9024 21.8945C80.9024 21.4828 80.7373 21.1626 80.4071 20.9339C80.0919 20.69 79.6567 20.568 79.1013 20.568C77.7054 20.568 76.6773 21.4142 76.0169 23.1067L73 21.3227ZM78.8762 35C78.2908 35 77.7955 34.7865 77.3902 34.3596C76.985 33.9174 76.7824 33.399 76.7824 32.8043C76.7824 32.1944 76.985 31.676 77.3902 31.249C77.8105 30.8069 78.3058 30.5858 78.8762 30.5858C79.4615 30.5858 79.9644 30.8069 80.3846 31.249C80.8199 31.676 81.0375 32.1944 81.0375 32.8043C81.0375 33.399 80.8199 33.9174 80.3846 34.3596C79.9644 34.7865 79.4615 35 78.8762 35Z" fill="url(#paint1_linear_368_2195)"/>
<defs>
<filter id="filter0_i_368_2195" x="15" y="33" width="58" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_368_2195"/>
</filter>
<linearGradient id="paint0_linear_368_2195" x1="44" y1="33" x2="44" y2="75" gradientUnits="userSpaceOnUse">
<stop stop-color="#DAE3F0"/>
<stop offset="1" stop-color="#DFE6F1" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint1_linear_368_2195" x1="83.9193" y1="13.737" x2="83.9193" y2="35" gradientUnits="userSpaceOnUse">
<stop stop-color="#DCE3EE"/>
<stop offset="1" stop-color="#E5E8EE"/>
</linearGradient>
</defs>
</svg>
