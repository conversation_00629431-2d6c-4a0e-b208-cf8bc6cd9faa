<svg width="44" height="31" viewBox="0 0 44 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_562_1122" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="-1" width="44" height="32">
<rect x="0.5" y="-0.5" width="43" height="31" rx="2.66" fill="white" stroke="white"/>
</mask>
<g mask="url(#mask0_562_1122)">
<rect x="1" width="42" height="30" rx="4" fill="url(#paint0_linear_562_1122)"/>
<rect x="14.045" y="5.045" width="23.91" height="5.91" rx="0.675" fill="url(#paint1_linear_562_1122)" stroke="white" stroke-width="0.09"/>
<g filter="url(#filter0_d_562_1122)">
<rect x="1" y="9" width="42" height="21" rx="2.16" fill="url(#paint2_linear_562_1122)"/>
<rect x="1.045" y="9.045" width="41.91" height="20.91" rx="2.115" stroke="white" stroke-width="0.09"/>
</g>
</g>
<defs>
<filter id="filter0_d_562_1122" x="-0.44" y="7.2" width="44.88" height="23.88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.36"/>
<feGaussianBlur stdDeviation="0.72"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.560758 0 0 0 0 0.67093 0 0 0 0 0.75071 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_562_1122"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_562_1122" result="shape"/>
</filter>
<linearGradient id="paint0_linear_562_1122" x1="22" y1="0" x2="22" y2="8.49558" gradientUnits="userSpaceOnUse">
<stop stop-color="#A0B9CC"/>
<stop offset="1" stop-color="#A8BDC9"/>
</linearGradient>
<linearGradient id="paint1_linear_562_1122" x1="26" y1="5" x2="26" y2="7.25" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2F9FD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_562_1122" x1="7.18948" y1="7.04651" x2="16.8946" y2="30.2532" gradientUnits="userSpaceOnUse">
<stop stop-color="#CBE5F2"/>
<stop offset="1" stop-color="#DEEDF5"/>
</linearGradient>
</defs>
</svg>
