import React from 'react';
import { <PERSON>, Button } from 'antd';
import { DatasetFile, CreateTaskFormType } from '../types';
import { LABELS, BUTTON_TEXTS } from '../constants';
import UploadList from '../../../../components/UploadList';

interface DatasetSelectionSectionProps {
  fileList: DatasetFile[];
  onShowDataModal: () => void;
  onAddTag: (dataSetId: string, tags: string[]) => void;
  onDelFile: (index: number) => void;
  onReUpload: (index: number) => void;
  datasetModalRef: React.RefObject<any>;
}

const DatasetSelectionSection: React.FC<DatasetSelectionSectionProps> = ({
  fileList,
  onShowDataModal,
  onAddTag,
  onDelFile,
  onReUpload,
  datasetModalRef,
}) => {
  return (
    <Form.Item<CreateTaskFormType> name="fileList" label={LABELS.SOURCE_DATASET}>
      <Button
        className="createTaskSelectBtn reqularText"
        onClick={onShowDataModal}
      >
        {BUTTON_TEXTS.SELECT_FROM_PLATFORM}
      </Button>
      <div>
        <UploadList
          fileList={fileList}
          onAddTag={onAddTag}
          noEditTag={true}
          onDelFile={(index) => onDelFile(index)}
          className="create-task-file-list"
          onReUpload={onReUpload}
        />
      </div>
    </Form.Item>
  );
};

export default DatasetSelectionSection;
