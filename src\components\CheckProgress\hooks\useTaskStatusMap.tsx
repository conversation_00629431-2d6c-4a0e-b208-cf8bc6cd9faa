import { TaskDetailType, TaskStatus } from '@/types';

const useTaskStatusMap = (taskDetail?: TaskDetailType) => {
  const statusClasses = {
    [TaskStatus.error]: 'text-orange-500',
    [TaskStatus.inProgress]: 'text-blue-500',
    [TaskStatus.success]: 'text-green-500',
    [TaskStatus.failed]: 'text-red-500',
  };
  const statusTexts = {
    [TaskStatus.error]: taskDetail?.complete === taskDetail?.total ? '任务完成' : '进行中',
    [TaskStatus.inProgress]: '进行中',
    [TaskStatus.success]: '任务完成',
    [TaskStatus.failed]: '任务失败',
  };
  const status = taskDetail?.taskStatus;
  return status && statusClasses[status] ? (
    <span className={statusClasses[status]}>{statusTexts[status]}</span>
  ) : (
    ''
  );
};

export default useTaskStatusMap;
