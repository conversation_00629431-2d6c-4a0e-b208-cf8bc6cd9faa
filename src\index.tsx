import ReactDOM from "react-dom/client";
import "./index.css";
import "./global.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import zhCN from "antd/locale/zh_CN";
import "antd/dist/reset.css";
import { Provider } from "react-redux";
import { ConfigProvider } from "antd";
import store from "./store";
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  // <React.StrictMode>import type { State } from "../store";
  <Provider store={store}>
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          // fontFamily: "HarmonyOS Sans SC",
          fontFamily: "HarmonyOS Sans SC Reqular",
          // colorPrimary: "#0fb698"
          colorLink: "#0fb698",
          colorText: "#111111",
        },
        components: {
          Button: {
            fontFamily: "HarmonyOS Sans SC Reqular",
            // colorPrimary: "#0fb698",
            // colorTextBase: "#000000",
            // algorithm: true, // 启用算法
            // colorLink: "#0fb698",
          },
          Progress: {
            colorSuccess: "#0fb698",
            colorInfo: "#0fb698",
          },
          // Pagination: {
          //   colorText: "#fff",
          //   colorPrimary: "#0fb698",
          //   colorPrimaryBorder: "#0fb698",
          //   colorPrimaryHover: "#0fb698",
          //   itemActiveBg: "#0fb698",
          // },
          Tabs: {
            fontFamily: "HarmonyOS Sans SC Medium",
            inkBarColor: "#0fb698",
            itemSelectedColor: "#0fb698",
            colorBorderSecondary: "unset",
          },
          Radio: {
            colorPrimary: "#0fb698",
          },
          Switch: {
            colorPrimary: "#0FB698",
          },
          Modal: {
            borderRadiusLG: 24,
            colorBgMask: "rgba(71, 118, 128, 0.40)",
          },
          Segmented: {
            itemSelectedColor: "#19C1A3",
            itemColor: "#8E98A7",
            itemHoverColor: "#19C1A3",
          },
          Slider: {
            handleLineWidthHover:6,
            trackBg:"#0FB698",
            trackHoverBg:"#0FB698",
            handleLineWidth:6,
            railSize:6,
          },
          // Select: {
          //   borderRadiusLG: 20
          // },
          // Input: {
          //   borderRadiusLG: 20
          // },
          Form: {
            fontFamily: "HarmonyOS Sans SC Reqular",
          },
          Tree: {
            colorPrimary: "#0FB698",
          },
          Checkbox: {
            colorPrimary: "#0FB698",
          },
          Descriptions: {
            labelBg: '#F6F9FC',
          },
          Steps: {
            // finishIconBorderColor: '#0FB698',
            colorPrimary: '#0FB698'
          }
        },
      }}
    >
      <App />
    </ConfigProvider>
  </Provider>
  // </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
