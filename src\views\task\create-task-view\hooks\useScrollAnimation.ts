import { useState, useEffect, useRef } from 'react';
import { EXAMPLE_MESSAGE } from '../constants';
import { DEFAULT_VALUES, INTERVALS } from '../constants';

/**
 * 滚动动画管理Hook
 */
export const useScrollAnimation = () => {
  const [currentExample, setCurrentExample] = useState(DEFAULT_VALUES.CURRENT_EXAMPLE);
  const [scrolling, setScrolling] = useState(DEFAULT_VALUES.SCROLLING);
  const containerRef = useRef<HTMLDivElement>(null);

  // 鼠标悬停控制
  useEffect(() => {
    const container = containerRef.current;

    const handleMouseEnter = () => {
      setScrolling(false);
    };

    const handleMouseLeave = () => {
      setScrolling(true);
    };

    if (container) {
      container.addEventListener('mouseenter', handleMouseEnter);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (container) {
        container.removeEventListener('mouseenter', handleMouseEnter);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // 自动滚动
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (scrolling) {
      interval = setInterval(() => {
        setCurrentExample((prev) => (prev + 1) % EXAMPLE_MESSAGE.length);
      }, INTERVALS.SCROLL_INTERVAL);
      return () => clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [scrolling]);

  return {
    currentExample,
    scrolling,
    containerRef,
  };
};
