import { DoubleLeftOutlined, HolderOutlined, MoreOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps, Popover, Segmented, Space, Tree } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import TextArea from 'antd/lib/input/TextArea';
import Scrollbars from 'react-custom-scrollbars';
import {
  buildTreeFromFlatArray,
  flattenTree,
  generateId,
  insertNodeInTree,
  removeIdsFromTree,
  removeNodeFromTree,
} from '../../tree';
import './index.module.css';
import { DisplayOCRChapters, OCRChapters } from './type';
import OcrTable from '../OcrTable';
// import { MenuInfo } from 'rc-menu/lib/interface';
interface MenuInfo {
  key: React.Key;
  keyPath: React.Key[];
  item: React.ReactInstance;
  domEvent: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>;
}

export const minioUrl = `${process.env.BASE_API}/files/download?file_path=`;
interface TreeItemProp {
  chapters: DisplayOCRChapters[];
}

const getResultClass = (type: string) => {
  switch (type) {
    case 'title':
      return 'result-title';
    case 'text':
      return 'result-text';
    default:
      break;
  }
};
const hLevelList: number[] = [1, 2, 3, 4, 5, 6, 7, 8];

const levelList: { [key: number]: string } = {
  '-1': 'T',
  1: 'H1',
  2: 'H2',
  3: 'H3',
  4: 'H4',
  5: 'H5',
  6: 'H6',
  7: 'H7',
  8: 'H8',
};

const levelValueMap: { [key: string]: number } = {
  T: -1,
  H1: 1,
  H2: 2,
  H3: 3,
  H4: 4,
  H5: 4,
  H6: 4,
  H7: 4,
  H8: 4,
};

interface ResultViewProp {
  chapters: DisplayOCRChapters[];
  onSelect: (ocrIndex: string[]) => void;
  onImgSelect: (data: number[][], pageNum: number) => void;
  onChange: (newChapters: DisplayOCRChapters[]) => void;
  tables: any[];
  images: { [key: string]: any };
}

const ResultView = forwardRef((prop: ResultViewProp, ref) => {
  const { chapters, onSelect, onChange, onImgSelect, tables, images } = prop;
  const [ocrList, setOcrList] = useState<DisplayOCRChapters[]>([]);
  // const [hoverId, setHoverId] = useState<string>();
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);
  // const [newChapters, setNewChapters] = useState<boolean>(false);

  const isTableText = (data: any) => tables.some((item) => item.caption?.text === data.text);

  const isImageText = (data: any) =>
    Object.values(images).some((item) => item.caption?.text === data.text);

  useImperativeHandle(ref, () => ({
    getChapters: () => {
      const result = removeIdsFromTree(ocrList);
      return result;
    },
  }));

  useEffect(() => {
    console.log('chapters4', chapters);
    // console.log('chapters2', chapters);
    setOcrList(chapters);
  }, [chapters]);

  const getLevelIndex = (level: number) => hLevelList.indexOf(level);

  const getParent = (index: number) => {
    const cur = ocrList[index];
    let parentNode;
    for (let i = index - 1; i >= 0; i -= 1) {
      const item = ocrList[i];
      if (cur?.type !== 'title' && item?.type === 'title') {
        parentNode = item;
        break;
      } else if (
        cur?.type === 'title' &&
        item?.type === 'title' &&
        hLevelList.indexOf(cur.level) > hLevelList.indexOf(item.level)
      ) {
        parentNode = item;
        break;
      }
    }
    return parentNode;
  };

  const getParentLevel = (index: number) => hLevelList.indexOf(getParent(index)?.level || -1);

  const displayLevelList = (curLevel: number, index: number): string[] => {
    const resultList = ['T'];
    if (curLevel !== -1) {
      const levelIndex = hLevelList.indexOf(curLevel);
      return resultList.concat(hLevelList.slice(0, levelIndex + 2).map((item) => levelList[item]));
    }
    const parentLevelIndex = getParentLevel(index);
    const endIndex = parentLevelIndex < 5 ? parentLevelIndex + 1 : parentLevelIndex;
    return resultList.concat(hLevelList.slice(0, endIndex + 1).map((item) => levelList[item]));
  };

  // const handleHToTChildrenChange = (data: DisplayOCRChapters, n: number) => {
  //   const lIndex = getLevelIndex(data.level);
  //   for (let i = 0; i < data.children.length; i++) {
  //     const child = data.children[i];
  //     if (child.type === 'title') {
  //       if (getLevelIndex(child.level) <= lIndex) break;
  //       const curIndex = getLevelIndex(child.level);
  //       let num = Math.max(0, curIndex + n);
  //       num = Math.min(5, curIndex + n);
  //       child.level = hLevelList[num];
  //     }
  //     if (child.children && child.children.length > 0) {
  //       handleHToTChildrenChange(child, n);
  //     }
  //   }
  //   // for (let i = index + 1; i < ocrList.length; i += 1) {
  //   //   const item = ocrList[i];
  //   //   if (item?.type === 'title') {
  //   //     if (getLevelIndex(item.level) <= lIndex) break;
  //   //     const curIndex = getLevelIndex(item.level);
  //   //     let num = Math.max(0, curIndex + n);
  //   //     num = Math.min(5, curIndex + n);
  //   //     item.level = hLevelList[num];
  //   //     // item.visible = true;
  //   //   }
  //   // }
  // };

  const convertHToTChildrenChange = (
    list: DisplayOCRChapters[],
    data: DisplayOCRChapters,
    n: number
  ) => {
    const lIndex = getLevelIndex(data.level);
    const index = list.findIndex((item) => item.displayId === data.displayId);
    for (let i = index + 1; i < ocrList.length; i += 1) {
      const item = ocrList[i];
      if (item?.type === 'title') {
        if (getLevelIndex(item.level) <= lIndex) break;
        const curIndex = getLevelIndex(item.level);
        let num = Math.max(0, curIndex + n);
        num = Math.min(5, curIndex + n);
        item.level = hLevelList[num];
        // item.visible = true;
      }
    }
  };

  const converTTOHChildrenChange = (list: DisplayOCRChapters[], data: DisplayOCRChapters) => {
    const lIndex = getLevelIndex(data.level);
    const index = list.findIndex((item) => item.displayId === data.displayId);
    for (let i = index + 1; i < ocrList.length; i += 1) {
      const item = ocrList[i];
      if (item?.type === 'title') {
        if (getLevelIndex(item.level) <= lIndex) break;

        let num = Math.max(0, getParentLevel(i) + 1);
        num = Math.min(5, num);
        item.level = hLevelList[num];
      }
    }
  };

  // const handleTTOHChildrenChange = (data: DisplayOCRChapters) => {
  //   const lIndex = getLevelIndex(data.level);
  //   for (let i = 0; i < data.children.length; i++) {
  //     const child = data.children[i];
  //     if (child?.type === 'title') {
  //       if (getLevelIndex(child.level) <= lIndex) break;

  //       let num = Math.max(0, getParentLevel(i) + 1);
  //       num = Math.min(5, num);
  //       child.level = hLevelList[num];
  //     }
  //   }
  //   // for (let i = index + 1; i < ocrList.length; i += 1) {
  //   //   const item = ocrList[i];
  //   //   if (item?.type === 'title') {
  //   //     if (getLevelIndex(item.level) <= lIndex) break;

  //   //     let num = Math.max(0, getParentLevel(i) + 1);
  //   //     num = Math.min(5, num);
  //   //     item.level = hLevelList[num];
  //   //   }
  //   // }
  // };

  const getParentByNode = (node: any, index: number) => {
    let parentNode;
    for (let i = index - 1; i >= 0; i -= 1) {
      const item = ocrList[i];
      if (node?.type !== 'title' && item?.type === 'title') {
        parentNode = item;
        break;
      } else if (
        node?.type === 'title' &&
        item?.type === 'title' &&
        getLevelIndex(node.level) > getLevelIndex(item.level)
      ) {
        parentNode = item;
        break;
      }
    }
    return parentNode;
  };

  const getChildrenIndex = (index: number, data: any): number[] => {
    const childrenIndex: number[] = [];
    if (data.level !== -1) {
      for (let i = index + 1; i < ocrList.length; i += 1) {
        const item = ocrList[i];
        if (item.level !== -1 && item.level <= data.level) break;
        if (item.level !== -1 && getLevelIndex(item.level) - getLevelIndex(data.level) === 1) {
          childrenIndex.push(i);
        } else if (item.level === -1 && getParent(i)?.text === data?.text) {
          childrenIndex.push(i);
        }
      }
    }
    return childrenIndex;
  };

  // const beforeChangeLevel = (expand: boolean, data: DisplayOCRChapters, index: number) => {
  //   // data.expand = expand;
  //   const childrenIndex = getChildrenIndex(index, data);
  //   childrenIndex.forEach((i: number) => {
  //     const item = ocrList[i];
  //     // item.visible = expand
  //   });
  // };

  const onLevelChange = (index: number, data: DisplayOCRChapters, pre: number, cur: number) => {
    const preIndex = pre === -1 ? getParentLevel(index) + 1 : getLevelIndex(pre);
    const curIndex = cur === -1 ? getParentLevel(index) + 1 : getLevelIndex(cur);
    const n = curIndex - preIndex;
    const flatTree = flattenTree(ocrList);
    const i = flatTree.findIndex(
      (item: { displayId: string }) => item.displayId === data.displayId
    );
    // const resultData = flatTree[i];
    console.log('flatTree', flatTree);
    if (pre !== -1 && cur === -1) {
      const newData = JSON.parse(JSON.stringify(data));
      newData.level = cur;
      newData.type = 'text';
      const newPreIndex = getLevelIndex(getParentByNode(newData, index)?.level || -1);
      const newN = newPreIndex - preIndex;
      if (newN === 0) {
        // 正文变标题，层级不变
        // handleHToTChildrenChange(resultData, -1);
        convertHToTChildrenChange(flatTree, flatTree[i], -1);
        flatTree[i].level = cur;
        flatTree[i].type = 'text';
      } else {
        // 正文变标题，层级前移
        // handleHToTChildrenChange(resultData, 1);
        convertHToTChildrenChange(flatTree, flatTree[i], -1);
        flatTree[i].level = cur;
        flatTree[i].type = 'text';
        // // 前移：将当前节点变为上一个兄弟节点的子节点 -> 上一个兄弟节点的子节点最后插入当前修改的节点
        // // 获取上一个兄弟节点
        // const preNode = findPreviousSibling(ocrList, resultData.id);
        // if (preNode) {
        //   if (preNode.children) {
        //     preNode.children.push(resultData);
        //   } else {
        //     preNode.children = [resultData];
        //   }

        //   // 删除当前节点
        //   newList = removeNodeFromTree(ocrList, resultData.id);
        //   // 更新兄弟节点
        //   newList = updateNodeInTree(newList, preNode.id, preNode);
        // }
      }
    }
    // 标题变正文，层级后移/层级不变
    else if (pre === -1 && cur !== -1 && n <= 0) {
      flatTree[i].level = cur;
      // handleTTOHChildrenChange(data);
      converTTOHChildrenChange(flatTree, flatTree[i]);
      flatTree[i].type = 'title';
      // let parentNode = getTreeParentNodeById(resultData.id, ocrList);
      // 后移：将当前节点变为父节点的下一个兄弟节点
      // if (parentNode && parentNode?.level >= resultData.level) {
      //   // 删除当前节点
      //   newList = removeNodeFromTree(ocrList, resultData.id);
      //   // 从新数组获取父节点
      //   parentNode = getTreeNodeById(parentNode.id, newList);
      //   if (parentNode) {
      //     // 将当前节点变为父节点的下一个兄弟节点
      //     newList = insertNodeInTree(newList, parentNode.id, resultData, false);
      //   }
      // }
    }
    // 标题前移, 标题后移
    else if (pre !== -1 && cur !== -1) {
      convertHToTChildrenChange(flatTree, flatTree[i], n);
      flatTree[i].level = cur;
    }
    const newList = buildTreeFromFlatArray(flatTree);
    // const newList = updateNodeInTree(ocrList, resultData.id, resultData);
    console.log('newList', newList);
    setOcrList(newList);
    // onChange(newList);
  };

  const levelSegmented = (data: DisplayOCRChapters, index: number) => (
    <Segmented
      onChange={(v) => {
        onLevelChange(index, data, data.level, levelValueMap[v]);
      }}
      defaultValue={levelList[data.level]}
      options={displayLevelList(data.level, index)}
    />
  );

  const items: MenuProps['items'] = [
    {
      key: 'delete',
      label: (
        <a target="_blank" rel="noopener noreferrer">
          删除条目
        </a>
      ),
    },
    {
      key: 'add',
      label: (
        <a target="_blank" rel="noopener noreferrer">
          添加内容
        </a>
      ),
      children: [
        {
          key: 'addBefore',
          label: '上方添加',
        },
        {
          key: 'addAfter',
          label: '下方添加',
        },
      ],
    },
  ];

  const [editId, setEditId] = useState<string>();

  useEffect(() => {
    console.log(editId);
  }, [editId]);

  const TreeItem: React.FC<TreeItemProp> = ({ chapters }) => {
    const [editInput, setEditInput] = useState<any>();

    const updateTextInTree = (
      tree: DisplayOCRChapters[],
      nodeId: string,
      newText: string
    ): DisplayOCRChapters[] =>
      tree.map((node) => {
        if (node.displayId === nodeId) {
          // 如果找到匹配的节点，更新其text属性
          return { ...node, text: newText };
        }
        if (node.children && node.children.length > 0) {
          // 如果节点有子节点，递归调用该函数在子节点中查找并更新
          return { ...node, children: updateTextInTree(node.children, nodeId, newText) };
        }
        // 如果节点既不是目标节点也没有子节点，保持原样
        return node;
      });

    const onAddOcrData = (before: boolean, data: DisplayOCRChapters) => {
      const displayId = generateId();
      const newItem: DisplayOCRChapters = {
        type: data.type,
        text: '',
        ocr_index: [],
        level: data.level,
        displayId,
        children: [],
      };

      const newList = insertNodeInTree(ocrList, data.displayId, newItem, before);
      setOcrList(newList);
      if (data.type === 'title') {
        onChange(newList);
      }
      setEditId(displayId);
    };

    const onMenuClick = (e: MenuInfo, curData: DisplayOCRChapters) => {
      const { key } = e;
      switch (key) {
        case 'delete':
          console.log(key);
          const newList = removeNodeFromTree(ocrList, curData.displayId);
          setOcrList(newList);
          if (curData.type === 'title') {
            onChange(newList);
          }
          break;
        case 'addBefore':
          console.log(key);
          onAddOcrData(true, curData);
          // setHoverId((pre) => pre - 1);
          break;
        case 'addAfter':
          console.log(key);
          onAddOcrData(false, curData);
          break;
        default:
          break;
      }
    };
    return (
      <>
        {chapters?.map((item, index) => {
          const { id, displayId, text, type, children } = item;
          let itemNode = <></>;
          if (
            (item.type === 'text' || item.type === 'title') &&
            !isImageText(item) &&
            !isTableText(item)
          ) {
            itemNode = (
              <div
                id={displayId}
                key={displayId}
                className={'result-container'}
                onClick={() => {
                  if (displayId !== editId) onSelect(item.ocr_index);
                }}
              >
                <Popover
                  content={levelSegmented(item, index)}
                  trigger="hover"
                  onOpenChange={setPopoverOpen}
                >
                  <Button type="text" size="small">
                    <HolderOutlined />
                  </Button>
                </Popover>
                {displayId === editId ? (
                  <>
                    <TextArea
                      className={'review-edit-textarea'}
                      value={editInput}
                      onChange={(e) => {
                        setEditInput(e.target.value);
                      }}
                    />
                    <Button
                      type="link"
                      size="small"
                      onClick={() => {
                        if (editInput && editInput?.trim().length > 0) {
                          const newList = updateTextInTree(ocrList, displayId, editInput.trim());
                          setOcrList(newList);
                          if (type === 'title') {
                            onChange(newList);
                          }
                        }
                        setEditInput(null);
                        setEditId('');
                      }}
                    >
                      完成编辑
                    </Button>
                  </>
                ) : (
                  <>
                    <div className={getResultClass(type)}>{text}</div>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => {
                        setEditId(displayId);
                        setEditInput(text);
                      }}
                    >
                      编辑
                    </Button>
                  </>
                )}
                <Dropdown
                  menu={{ items, onClick: (e) => onMenuClick(e, item) }}
                  placement="bottomRight"
                  arrow
                >
                  <Button type="text" size="small">
                    <MoreOutlined />
                  </Button>
                </Dropdown>
              </div>
            );
          } else if (
            (item.type === 'text' || item.type === 'title') &&
            (isImageText(item) || isTableText(item))
          ) {
            itemNode = (
              <div
                id={displayId}
                key={displayId}
                className={'result-container'}
                onClick={() => {
                  if (displayId !== editId) onSelect(item.ocr_index);
                }}
              >
                <div className={'result-img-text'}>{text}</div>
              </div>
            );
          } else if (type === 'image') {
            itemNode = (
              <img
                src={minioUrl + images[id || 0]?.local_cut_img_path}
                className={'img-item'}
                onClick={() => onImgSelect(images[id || 0].img_pos, images[item.id || 0].page_num)}
              />
            );
          } else if (type === 'table') {
            <OcrTable
              data={tables[(item.id || 0) as any]}
              HightlineText={function (res: any[]): void {
                onSelect(res);
              }}
            />;
          }
          return (
            <>
              {itemNode}
              <TreeItem chapters={children} />
            </>
          );
        })}
      </>
    );
  };

  return (
    <>
      <Space size={20} direction={'vertical'} style={{ width: '100%' }}>
        <div style={{ height: 800 }}>
          <Scrollbars>
            <TreeItem chapters={ocrList} />
          </Scrollbars>
        </div>
      </Space>
    </>
  );
});

export default ResultView;
