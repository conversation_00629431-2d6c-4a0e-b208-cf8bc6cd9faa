export interface OCRReviewState {
    list: OCRChaptersType[];
    imgPath: OCRFilePath[];
    bboxes: OCRBboxes[][];
    reviewStatus: number;
    docId: string;
    fileStructure: OCRReviewFileStructure;
    chapters: OCRChapters[];
    tables: OCRTable[];
    images: { [key: string]: any };
}

export interface OCRChaptersType {
    type: string;
    text: string;
    ocr_index: string[];
    level: number;
    id?: number;
}

export interface OCRFilePath {
    local_path: string;
    minio_path: string;
    page_num: number;
    minio_filepath: string;
    image_size: number[];
}

export interface OCRBboxes {
    bbox: number[][];
    text_len: number;
}

export interface OCRReviewFileStructure {
    tables?: OCRTable[]; // 表格数据
    ocr_bboxes?: OCRBboxes[][]; // 坐标
    file_path?: OCRFilePath[]; // 文件路径
    minio_path?: any; //图片路径
    images?: any;
    chapters?: OCRChapters[]; //提取数据
}

export interface OCRChapters {
    type: string;
    text: string;
    ocr_index: string[];
    level: number;
    children: OCRChapters[];
}

export interface OCRTable {
    col: number;
    row: number;
    text_cell: OCRTableCell[];
    caption: OCRTableCaption;
}

export interface OCRTableCaption {
    type: string;
    text: string;
}

export interface OCRTableCell {
    cols: number;
    rows: number;
    ocr_index: string[];
    text: string;
}

export interface DisplayOCRChapters {
  id?: string;
  type: string;
  text: string;
  ocr_index: string[];
  level: number;
  children: DisplayOCRChapters[];
  displayId:string;
}

export interface FlatOCRChapters {
    id: string;
    type: string;
    text: string;
    ocr_index: string[];
    level: number;
}