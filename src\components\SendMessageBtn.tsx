import { Button } from "antd";
import { useEffect, useState } from "react";
import { loginVerifyCode, registerVerifyCode } from "../api/auth";
import {
  RegisterCodeParams,
  VerifyCodeParams,
  VerifyCodeTypeEnum,
} from "../types";

interface SendMessageBtnProp {
  params: VerifyCodeParams | RegisterCodeParams;
  disabled: boolean;
  type: number;
  onClick?: () => void
}
const SendMessageBtn: React.FC<SendMessageBtnProp> = ({
  params,
  disabled,
  type,
  onClick
}) => {
  const [isSending, setIsSending] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isSending && remainingTime > 0) {
      timer = setInterval(() => {
        setRemainingTime((prevTime) => prevTime - 1);
      }, 1000);
    } else if (remainingTime === 0) {
      setIsSending(false);
    }

    return () => {
      clearInterval(timer);
    };
  }, [isSending, remainingTime]);

  const handleSendCodeClick = () => {
    if (onClick) {
      onClick();
    }
    if (!params.account) {
      return;
    }

    // 发送验证码的逻辑应该在这里实现，这里只是示例
    // 在发送验证码成功后，设置 isSending 为 true，开始倒计时
    setRemainingTime(60); // 重置倒计时时间
    setIsSending(true);

    if (type === VerifyCodeTypeEnum.Login) {
      loginVerifyCode(params as VerifyCodeParams);
    } else {
      registerVerifyCode(params as RegisterCodeParams);
    }
  };

  return (
    <Button
      shape="round"
      block
      className="sendVerifyCodeBtn boldText"
      onClick={handleSendCodeClick}
      disabled={disabled || isSending || remainingTime > 0}
    >
      {isSending ? `重新发送(${remainingTime}s)` : "发送验证码"}
    </Button>
  );
};

export default SendMessageBtn;
