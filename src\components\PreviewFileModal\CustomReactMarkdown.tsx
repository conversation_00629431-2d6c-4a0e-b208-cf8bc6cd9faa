import ReactMarkdown from 'react-markdown';

const CustomReactMarkdown: React.FC<{ children: string }> = ({ children }) => {
  return (
    <ReactMarkdown
      className={'react-mark-down'}
      components={{
        h1: ({ children }) => {
          const text = children?.toString() || '';
          const id = `heading-${text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
          return <h1 id={id}>{children}</h1>;
        },
        h2: ({ children }) => {
          const text = children?.toString() || '';
          const id = `heading-${text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
          return <h2 id={id}>{children}</h2>;
        },
        h3: ({ children }) => {
          const text = children?.toString() || '';
          const id = `heading-${text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
          return <h3 id={id}>{children}</h3>;
        },
        h4: ({ children }) => {
          const text = children?.toString() || '';
          const id = `heading-${text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
          return <h4 id={id}>{children}</h4>;
        },
        h5: ({ children }) => {
          const text = children?.toString() || '';
          const id = `heading-${text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
          return <h5 id={id}>{children}</h5>;
        },
        h6: ({ children }) => {
          const text = children?.toString() || '';
          const id = `heading-${text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
          return <h6 id={id}>{children}</h6>;
        },
      }}
    >
      {children}
    </ReactMarkdown>
  );
};

export default CustomReactMarkdown;
