/* eslint-disable react-hooks/rules-of-hooks */
import { <PERSON><PERSON>, Card, Space, Switch } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { AllocatedQA, FileContent, HighlightIdx, QADocument, TaskDetailType } from '../../types';
import Scrollbar from 'react-scrollbars-custom';
import { QaDeleteInfo, deleteQA, getQAList, getFileContent } from '../../api/qa';
import {
  flattenFileTree,
  getQATaskFileTree,
  getReviewConfigInfo,
  getTaskDetail,
  getTaskIsSetReviewConfig,
} from '../../api/task';

import { DataNode } from 'antd/es/tree';
import { queryQaAllocated } from '../../api/review';
import DeleteQAConfirmModal from '../../components/DeleteQAConfirmModal';
import { ReviewConfigBtnType } from '../../components/ReviewConfigModal/components/ReviewConfigBtn/type';
import ReviewHeader from './components/ReviewHeader';
import SourceTextCard from './components/SourceTextCard';
import QAEditSection from './components/QAEditSection';
import RateButtonSection from './components/RateButtonSection';
import QAListSidebar from './components/QAListSidebar';
import ReviewActions from './components/ReviewActions';

const DefaultReviewView: React.FC = () => {
  const [question, setQuestion] = useState<QADocument>(new Object() as QADocument);
  const [currentQues, setCurrentQues] = useState<string>('');
  const [currentAnsw, setCurrentAnsw] = useState<string>('');
  const [reviewId, setReviewId] = useState('');
  const [highlightIdxList, setHighlightIdxList] = useState<HighlightIdx[]>([]);

  const navigate = useNavigate();
  const [displayQaList, setDisplayQaList] = useState<QADocument[]>([]);
  const [allQaList, setAllQaList] = useState(0);

  const [editQuestion, setEditQuestion] = useState<boolean>(false);
  const [editAnswer, setEditAnswer] = useState<boolean>(false);
  const [treeSelectValue, setTreeSelectValue] = useState<string>();
  const { task_id } = useParams<string>();
  const [fileContent, setFileContent] = useState<FileContent[]>([]);
  const [taskDetail, setTaskDetail] = useState<TaskDetailType>(new Object() as TaskDetailType);
  const [qaFileMap, setQaFileMap] = useState<Map<string, string>>(new Map<string, string>());
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [ignoreConfirmModal, setIgnoreConfirmModal] = useState<boolean>(false);
  const [score, setScore] = useState<string>('');
  const [autoSwitch, setAutoSwitch] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const [fileList, setFileList] = useState<string[]>([]);
  const [qaContent, setQaContent] = useState<string>();
  const [fileTree, setFileTree] = useState<any[]>([]);
  const [flattenTree, setFlattenTree] = useState<any[]>([]);
  const [scoreButtonInfo, setScoreButtonInfo] = useState<ReviewConfigBtnType[]>([]);
  // 审核中
  const [isReviewing, setIsReviewing] = useState<boolean>(false);
  const [qaAllont, setQaAllont] = useState<AllocatedQA[]>([]);

  if (!task_id) {
    navigate(`/main/task`);
    return null;
  }

  useEffect(() => {
    getTaskIsSetReviewConfig(task_id).then((p1) => {
      if (p1.data?.code === 200) {
        // 配置过审核配置才允许审核，否则跳转到任务详情
        const userId = sessionStorage.getItem('id');
        if (task_id && userId) {
          dataQA(task_id, userId);
        }
        onGetTaskDetail(task_id);
        // setScoreButtonInfo(DefaultBtnConfig);
        getReviewConfigInfo(task_id).then((res) => {
          if (res?.data?.code === 200) {
            setScoreButtonInfo(res.data.data?.scoreButtonInfo);
          }
        });
      }
    });
  }, []);

  const shouldShowReminder = () => {
    const ignoreConfirm = localStorage.getItem('ignoreConfirm');
    if (ignoreConfirm) {
      const lastRemindedDate = localStorage.getItem('lastRemindedDate');
      const today = new Date().toLocaleDateString();
      if (lastRemindedDate !== today) {
        localStorage.setItem('lastRemindedDate', today);
        return true;
      } else {
        return false;
      }
    }
    return true;
  };

  useEffect(() => {
    if (autoSwitch) {
      setNextQA();
    }
  }, [displayQaList]);

  useEffect(() => {
    setCurrentQues(question.question);
    setCurrentAnsw(question.answer);
    setIsConfirm(question.review);
    setScore(question.score);
    if (task_id && question.id) {
      getFileContent(task_id, question.id).then((res) => {
        if (res.data?.code === 200) {
          setQaContent(res.data.data.content);
        }
      });
      setHighlightIdxList(question.highlightIdxList);
    }
  }, [question]);

  const setNextQA = () => {
    setNextQAWithList(displayQaList);
  };

  const setNextQAWithList = (qaList: QADocument[]) => {
    let qaIndex = 0;
    let qaItem: QADocument | undefined;

    if (!question?.id) {
      // 初始化时取第一个未审核的QA
      qaIndex = qaList.findIndex((qa: QADocument) => !qa.review);
      // 全审核完选取第一个QA
      qaItem = qaIndex === -1 ? qaList[0] : qaList[qaIndex];
    } else {
      // 刷新时，取上一个QA之后的第一个未审核的QA
      // 如果是最后一条，就从头查找
      const currentIndex = qaList.findIndex((qa) => qa.id === reviewId);

      if (currentIndex === -1) {
        // 如果当前reviewId在列表中找不到，可能是因为数据更新了
        // 直接找第一个未审核的QA
        qaIndex = qaList.findIndex((qa: QADocument) => !qa.review);
        qaItem = qaIndex === -1 ? qaList[0] : qaList[qaIndex];
      } else if (currentIndex !== qaList.length - 1) {
        // 从当前位置的下一个开始查找未审核的QA
        qaItem = qaList.slice(currentIndex, qaList.length).find((qa: QADocument) => !qa.review);

        // 如果后面没有未审核的，从头开始找
        if (!qaItem) {
          qaItem = qaList.find((qa: QADocument) => !qa.review);
        }
      } else {
        // 当前是最后一条，从头查找
        qaItem = qaList.find((qa: QADocument) => !qa.review);
      }
    }

    if (qaItem) {
      setReviewId(qaItem.id);
      setQuestion(qaItem);
      setCurrentQues(qaItem.question);
      setCurrentAnsw(qaItem.answer);
      if (task_id && qaItem.id) {
        getFileContent(task_id, qaItem.id).then((res) => {
          if (res.data?.code === 200) {
            setQaContent(res.data.data.content);
          }
        });
        setHighlightIdxList(qaItem.highlightIdxList);
      }
    } else {
      console.log('没有找到合适的QA');
    }
  };

  const onTreeSelectChange = (value: any, node: any) => {
    if (!node.children || node.children.length === 0) {
      // 选中子节点
      const docuList = flattenTree.filter((qa) => qa.fileId === node.fileId);
      setFileList(docuList.map((item) => item.fileId));
    } else {
      // 选中父节点
      const children = fileTree.filter((item) => item.fileTreeNodeForTask.fileId === node.fileId);
      if (children && children.length > 0)
        setFileList(
          children[0].fileTreeNodeForTask.children.map((item: { fileId: any }) => item.fileId)
        );
    }
  };

  const getQAs = (taskId: string) => {
    const userId = sessionStorage.getItem('id');
    const qaParams = {
      taskId,
      page: 0,
      pageSize: 999,
      fileIdList: fileList && fileList.length > 0 ? fileList : undefined,
      allocateUserId: userId ?? '',
    };
    getQAList(qaParams).then((res) => {
      if (res.data?.code === 200) {
        setDisplayQaList(res.data.data.qaDocumentPage);
        setAllQaList(res.data.data.total);
      }
    });
  };

  const onRefresh = (taskId: string) => {
    getTaskDetail(taskId).then((res) => {
      if (res.data?.code === 200) {
        const data = res.data.data;
        setTaskDetail(data);

        // 先获取QA数据，然后在回调中处理自动切换
        const userId = sessionStorage.getItem('id');
        const qaParams = {
          taskId,
          page: 0,
          pageSize: 999,
          fileIdList: fileList && fileList.length > 0 ? fileList : undefined,
          allocateUserId: userId ?? '',
        };

        getQAList(qaParams).then((qaRes) => {
          if (qaRes.data?.code === 200) {
            const newDisplayQaList = qaRes.data.data.qaDocumentPage;
            setDisplayQaList(newDisplayQaList);
            setAllQaList(qaRes.data.data.total);

            // 在QA数据更新后，如果开启自动切换，则切换到下一个QA
            // if (autoSwitch) {
            // 使用新的QA列表来查找下一个QA
            setNextQAWithList(newDisplayQaList);
            // }
          }
        });
      }
    });
  };

  const onGetTaskDetail = (taskId: string) => {
    getTaskDetail(taskId).then((res) => {
      if (res.data?.code === 200) {
        const data = res.data.data;
        setTaskDetail(data);
        getQAs(taskId);

        getQATaskFileTree(taskId).then((res) => {
          if (res.data?.code === 200) {
            const data = res.data.data;
            if (data) {
              const fileTrees = data.map((item, index) => {
                // item.fileTreeNodeForTask.fileId = index.toString();
                return item.fileTreeNodeForTask;
              });
              const flattenTree = flattenFileTree(fileTrees);
              setFileTree(
                data.map((item, index) => {
                  // item.fileTreeNodeForTask.fileId = index.toString();
                  return item;
                })
              );
              setFlattenTree(flattenTree);
              setTreeData(fileTrees as any[]);
            }
          }
        });
      }
    });
  };

  useEffect(() => {
    if (task_id) {
      getQAs(task_id);
    }
  }, [fileList]);

  useEffect(() => {
    if (displayQaList.length > 0 && !question?.id) {
      // 组件初始化，设置第一个QA
      setNextQA();
    }
  }, [displayQaList]);
  async function dataQA(taskId: string, userId: string) {
    if (taskId && userId) {
      const res = await queryQaAllocated(taskId, userId);
      if (res?.data?.code === 2000) {
        const data = res.data?.code as unknown as AllocatedQA[];
        setQaAllont(data);
      }
    }
  }

  return (
    <Scrollbar>
      <div className="createTaskContent" style={{ width: '90.3%' }}>
        <ReviewHeader taskDetail={taskDetail} task_id={task_id} />
        <div className="createTaskArea" style={{ padding: '41px 37px', margin: 0 }}>
          <div
            style={{
              display: 'inline-flex',
              height: '808px',
            }}
          >
            <SourceTextCard
              fileContent={fileContent}
              highlightIdxList={highlightIdxList}
              qaContent={qaContent}
            />
            <div className="default-review-rg">
              <Card
                bordered={false}
                size="small"
                headStyle={{ height: '52px' }}
                title={
                  <div className="boldText" style={{ textAlign: 'start' }}>
                    人工审核
                  </div>
                }
                extra={
                  <Space>
                    <Badge color="#0FB698" />
                    <label style={{ color: '#6D7279' }}>
                      已审核：
                      <label style={{ color: '#111111' }} className="boldText">
                        {taskDetail.reviewCount ?? 0 + ' '}
                      </label>
                      个,
                    </label>
                    <label style={{ color: '#6D7279' }}>
                      剩余：
                      <label style={{ color: '#111111' }} className="boldText">
                        {allQaList - (taskDetail.reviewCount ?? 0) + ' '}
                      </label>
                      个
                    </label>
                  </Space>
                }
                style={{ width: '100%', border: 'unset' }}
                bodyStyle={{ height: 714 }}
              >
                <div style={{ display: 'flex', width: '100%' }}>
                  <Space
                    direction="vertical"
                    style={{ width: '100%', flex: '1', padding: '0 1rem' }}
                    size={18}
                  >
                    <QAEditSection
                      question={question}
                      taskDetail={taskDetail}
                      currentQues={currentQues}
                      currentAnsw={currentAnsw}
                      editQuestion={editQuestion}
                      editAnswer={editAnswer}
                      isConfirm={isConfirm}
                      qaFileMap={qaFileMap}
                      onCurrentQuesChange={setCurrentQues}
                      onCurrentAnswChange={setCurrentAnsw}
                      onEditQuestionChange={setEditQuestion}
                      onEditAnswerChange={setEditAnswer}
                      onIsConfirmChange={setIsConfirm}
                      onRefresh={() => onGetTaskDetail(taskDetail.id)}
                      onDeleteConfirm={() => {
                        if (shouldShowReminder()) {
                          setIgnoreConfirmModal(true);
                        } else {
                          const params: QaDeleteInfo = {
                            qaDeleteInfoList: [
                              {
                                fileId: qaFileMap.get(question.id) ?? '',
                                ids: [question.id],
                              },
                            ],
                            taskId: taskDetail?.id ?? '',
                          };
                          deleteQA(params).then((res) => {
                            const updateQA = question;
                            updateQA.id = null as any;
                            setQuestion(updateQA);
                            onGetTaskDetail(taskDetail.id);
                          });
                        }
                      }}
                    />
                    <RateButtonSection
                      question={question}
                      taskDetail={taskDetail}
                      score={score}
                      autoSwitch={autoSwitch}
                      isConfirm={isConfirm}
                      scoreButtonInfo={scoreButtonInfo}
                      onScoreChange={setScore}
                      onRefresh={() => onRefresh(taskDetail.id)}
                      onReviewingChange={setIsReviewing}
                    />
                  </Space>
                  <QAListSidebar
                    treeSelectValue={treeSelectValue}
                    treeData={treeData}
                    displayQaList={displayQaList}
                    reviewId={reviewId}
                    scoreButtonInfo={scoreButtonInfo}
                    onTreeSelectChange={onTreeSelectChange}
                    onTreeSelectValueChange={setTreeSelectValue}
                    onRefresh={() => onRefresh(taskDetail.id)}
                    onQuestionSelect={(item) => {
                      setQuestion(item);
                      setReviewId(item.id);
                    }}
                  />
                </div>
                <ReviewActions
                  question={question}
                  taskDetail={taskDetail}
                  score={score}
                  autoSwitch={autoSwitch}
                  currentQues={currentQues}
                  currentAnsw={currentAnsw}
                  editQuestion={editQuestion}
                  editAnswer={editAnswer}
                  isReviewing={isReviewing}
                  onAutoSwitchChange={setAutoSwitch}
                  onRefresh={() => onRefresh(taskDetail.id)}
                  onReviewingChange={setIsReviewing}
                  onSetNextQA={setNextQA}
                />
              </Card>
            </div>
          </div>
          <div
            style={{
              marginTop: -31,
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            <Space>
              <Switch size="default" checked={autoSwitch} onChange={setAutoSwitch} />
              <label style={{ color: '#6D7279' }}>审核完成后自动切换下一条</label>
            </Space>
          </div>
        </div>
      </div>
      <DeleteQAConfirmModal
        visible={ignoreConfirmModal}
        OnClose={(isDelete: boolean, ignore?: boolean | undefined) => {
          setIgnoreConfirmModal(false);
          if (ignore) {
            localStorage.setItem('ignoreConfirm', 'true');
            const today = new Date().toLocaleDateString();
            localStorage.setItem('lastRemindedDate', today);
          }
          if (isDelete) {
            const params: QaDeleteInfo = {
              qaDeleteInfoList: [
                {
                  fileId: qaFileMap.get(question.id) ?? '',
                  ids: [question.id],
                },
              ],
              taskId: taskDetail?.id ?? '',
            };
            deleteQA(params).then((res) => {
              const updateQA = question;
              updateQA.id = null as any;
              setQuestion(updateQA);
              onGetTaskDetail(taskDetail.id);
            });
          }
        }}
      />
    </Scrollbar>
  );
};

export default DefaultReviewView;
