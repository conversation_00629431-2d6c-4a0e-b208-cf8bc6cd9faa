import { LeftOutlined, SendOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Flex,
  Form,
  message,
  Segmented,
  Select,
  Slider,
  Space,
  Tooltip,
  Badge,
  Row,
  Col,
} from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { SliderMarks } from "antd/es/slider";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  getModelConfigById,
  getServerList,
  getPower,
  configAdjust,
  getModelOnline,
} from "../../../../api/modle";
import { performance } from "../../../../utils/conts";
import { useNavigate, useParams } from "react-router-dom";
import Scrollbar from "react-scrollbars-custom";
import { ConfigType, ServerDataType } from "../type";
import "../../../../css/ConfigView.css";
import EchartsBarComponent from "../../../training/CreateTrainingView/components/Overview/echarts";
import { split } from "../../../../utils/conts";
import { ModelDetailType } from "../../../../types";
interface configureFormType {
  modelData?: ConfigType;
}
interface PowerDataType {
  name: string;
  data: {
    estimated_increased_rate: string;
    free_rate: string;
    usage_rate: string;
  };
}
interface BadgeProps {
  statusData: "success" | "error";
  text: string;
}
const BadgeStatus: React.FC<BadgeProps> = ({ statusData, text }) => (
  <Badge status={statusData} text={text} />
);
const iteratSetValueMap = (testSetStr: string) => {
  const index = split.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const ConfigureView: React.FC = () => {
  const { task_id, name, status } = useParams<string>();

  const navigate = useNavigate();
  const [form] = Form.useForm();

  //   训练框架数据
  const [deploymentFrameData, setDeploymentFrameData] = useState([]);
  // 服务器选择
  const [serverData, setServerData] = useState();
  const [serverOptions, setServerOptions] = useState<ServerDataType[]>();
  // 算力配置
  const [arithmeticData, setArithmeticData] = useState<any[]>([]);

  // 算力配置选项
  const [arithmeticOptions, setArithmeticOptions] = useState<any[]>();
  const [isServerSelected, setIsServerSelected] = useState(false);
  const [selectedServerId, setSelectedServerId] = useState("");
  // 传值
  const [powerData, setPowerData] = useState<PowerDataType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  // 性能配置
  const [performanceData, setPerformanceData] = useState<any>({
    splitLevel: 50,
  });
  // 训练框架选项
  const frameOptions = [
    {
      label: "vllm",
      value: "vllm",
    },
  ];
  // 服务器选择选项
  const performanceMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{}</div>
        <Tooltip title={performance[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{}</div>
        <Tooltip title={performance[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{}</div>
        <Tooltip title={performance[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{}</div>
        <Tooltip title={performance[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{}</div>
        <Tooltip title={performance[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const getModelConfigData = () => {
    getModelConfigById(task_id as string).then((res: any) => {
      if (res.data?.code === 200) {
        const data = res.data.data;
        setDeploymentFrameData(data.deploymentFramework);
        setServerData(data.server);
        getPowerList(data.serverConfigId);
        setArithmeticData(data.computeResource);
        setSelectedServerId(data.serverConfigId);
        setIsServerSelected(true);
        if (isServerSelected) {
          getPowerList(selectedServerId);
          setIsServerSelected(true);
        }
      }
    });
  };
  const getServer = () => {
    getServerList().then((res: any) => {
      if (res.data?.code === 200) {
        const serverConfigs = res.data.data.server_config;
        if (Array.isArray(serverConfigs)) {
          setServerOptions(
            serverConfigs.map((item: any) => {
              return {
                label: item.server_name,
                value: item.server_name,
                id: item.id,
              };
            })
          );
        } else {
          console.error(
            "获取的服务器配置数据不是数组类型，无法更新 serverData"
          );
          setServerOptions([]);
        }
      }
    });
  };
  const getPowerList = (id: string) => {
    getPower(id).then((res: any) => {
      if (res.data?.code && res.data?.code === 200) {
        const powerList = res.data.data.gpu_usage;
        console.log("powerList", powerList);
        const newPowerList: {
          [key: string]: {
            estimated_increased_rate: string;
            free_rate: string;
            usage_rate: string;
          };
        } = {};
        Object.keys(powerList).forEach((key) => {
          const newKey = `A100_${("0" + (Number(key) + 1)).slice(-2)}`;
          newPowerList[newKey] = powerList[key];
        });
        const powerArray = Object.entries(newPowerList).map(([key, value]) => ({
          name: key,
          data: value,
        }));
        setPowerData(powerArray);
        const Options = powerArray.map((item: any, index: any) => {
          const ids = Object.keys(powerList);
          return {
            id: ids[index],
            label: item.name as string,
            value: item.name as string,
          };
        });
        setArithmeticOptions(Options);
      }
    });
  };
  const handleSave = () => {
    const id = task_id as string;
    if (Number(status) === 7 || Number(status) === 6) {
      // 在线状态，需要下线
      getModelOnline(id).then((res: any) => {
        if (res.data?.code === 200) {
          message.success("模型部署下线成功!");
          navigate("/main/finetune");
        } else {
          message.error("模型部署下线失败");
        }
      });
    } else {
      // 离线状态，可调整配置，进行上线
      const useValue = Number(powerData?.[0]?.data.usage_rate);
      const preValue =
        Number(powerData?.[0]?.data.estimated_increased_rate) + useValue;
      form
        .validateFields()
        .then(() => {
          if (preValue > 1) {
            message.error("服务器使用率大于100%,无法部署上线");
            return;
          } else {
            const params = {
              computeResource: Array.isArray(arithmeticData)
                ? arithmeticData
                : [arithmeticData],
              serverConfigId: selectedServerId,
              deploymentFramework: deploymentFrameData,
              performanceConfig: performanceData.splitLevel,
              server: serverData,
            };
            configAdjust(id, params).then((res: any) => {
              if (res.data.code === 200) {
                getModelOnline(id).then((res: any) => {
                  if (res.data?.code === 200) {
                    message.success("配置成功,模型部署上线成功!");
                    navigate("/main/finetune");
                  } else {
                    message.error("模型部署上线失败");
                  }
                });
              }
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    }
  };

  useEffect(() => {
    getModelConfigData();
    getServer();
    setInterval(() => {
      setIsLoading(false);
    }, 500);
  }, []);

  return (
    <Scrollbar>
      <div className="configureView">
        <div className="configure-header">
          <Space
            size={20}
            style={{ display: "inline-flex", alignItems: "center" }}
          >
            <Button
              style={{ fontSize: "12px", width: "36px", height: "36px" }}
              shape="circle"
              icon={<LeftOutlined />}
              onClick={() => navigate("/main/finetune")}
            />
            <div
              className="mediumText"
              style={{
                fontSize: "28px",
                lineHeight: "36px",
                fontWeight: "500",
              }}
            >
              {name}/配置
            </div>
            <BadgeStatus
              statusData={
                Number(status) === 7 || Number(status) === 6
                  ? "success"
                  : "error"
              }
              text={
                Number(status) === 7 || Number(status) === 6 ? "在线" : "离线"
              }
            />
          </Space>
          <Space>
            {Number(status) !== 7 ? (
              <Button
                className="save-btn"
                size="large"
                shape="round"
                onClick={handleSave}
              >
                部署上线
              </Button>
            ) : (
              <Button
                className="save-btn"
                size="large"
                shape="round"
                onClick={handleSave}
              >
                部署下线
              </Button>
            )}
          </Space>
        </div>
        <div className="configure-info">
          <div className="configure-title">部署配置</div>
          <Form
            form={form}
            initialValues={{
              framework: deploymentFrameData,
              serverSelection: serverData,
              arithmeticConfiguration: arithmeticData,
            }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18, offset: 2 }}
            className="configure-form"
          >
            <Form.Item<configureFormType>
              label="部署框架:"
              rules={[{ required: true, message: "请选择部署框架" }]}
              style={{ width: "511px", marginLeft: "28px" }}
            >
              <Select
                size="large"
                disabled={Number(status) === 7 || Number(status) === 6}
                showSearch
                placeholder="请选择部署的框架"
                value={deploymentFrameData}
                onChange={(e) => {
                  setDeploymentFrameData(e);
                }}
                style={{ flex: 1 }}
                // onClick={}
                options={frameOptions}
              ></Select>
            </Form.Item>
            <Form.Item<configureFormType>
              label="性能配置："
              style={{ width: "620px" }}
              rules={[{ required: true, message: "请选择性能配置" }]}
            >
              <span className="span-text">并发优先</span>
              <Slider
                defaultValue={performanceData.splitLevel}
                dots
                disabled={Number(status) === 7 || Number(status) === 6}
                tooltip={{
                  formatter: (val) => {
                    const q = val ? val : 0;
                    return "优先度:" + q;
                  },
                }}
                step={25}
                marks={performanceMarks}
                value={performanceData.splitLevel}
                onChange={(val) => {
                  setPerformanceData((prevSettings: any) => ({
                    ...prevSettings,
                    splitLevel: val, // 假设newValue是您要设置的新值
                  }));
                }}
                style={{
                  width: "67.8%",
                  display: "inline-flex",
                  margin: "unset",
                }}
              ></Slider>
              <span className="span-text1">速度优先</span>
            </Form.Item>
            <Form.Item<configureFormType>
              label="服务器选择:"
              rules={[{ required: true, message: "请选择服务器" }]}
              style={{ width: "620px" }}
            >
              <Select
                size="large"
                disabled={Number(status) === 7 || Number(status) === 6}
                showSearch
                placeholder="请选择服务器"
                value={serverData}
                onChange={(e) => {
                  setServerData(e);
                  setIsServerSelected(true);
                  const serverId = String(
                    serverOptions?.filter((item) => item.value === e)[0].id
                  );
                  setSelectedServerId(serverId);
                  getPowerList(serverId);
                }}
                style={{ flex: 1 }}
                options={serverOptions}
              ></Select>
            </Form.Item>
            <Form.Item<configureFormType>
              label="算力配置:"
              rules={[{ required: true, message: "请选择算力配置" }]}
              style={{ width: "620px" }}
            >
              <Select
                size="large"
                disabled={Number(status) === 7 || Number(status) === 6}
                showSearch
                placeholder="请选择算力配置"
                value={arithmeticData}
                onChange={(e) => {
                  setArithmeticData(e);
                }}
                style={{ flex: 1 }}
                options={arithmeticOptions}
              ></Select>
            </Form.Item>
            {isLoading ? (
              <LoadingOutlined spin style={{ fontSize: "104px" }} />
            ) : (
              powerData.length > 0 && (
                <div className={"configure-chart"}>
                  <EchartsBarComponent powerData={powerData} status={status} />
                </div>
              )
            )}
          </Form>
        </div>
      </div>
    </Scrollbar>
  );
};
export default ConfigureView;
