import { <PERSON><PERSON>, Col, Modal, Row, Space, Spin, TreeDataNode, message } from 'antd';
import { RollbackOutlined, DoubleLeftOutlined } from '@ant-design/icons';
import { DatasetFileEnum, DataSetTreeType, ParseDatasetType } from '@/types';
import { useEffect, useState } from 'react';
import Scrollbar from 'react-scrollbars-custom';
import '@/css/PreviewFileModal.css';
import { getDataFilePreview } from '@/api/dataset';
import DirectoryTree from 'antd/es/tree/DirectoryTree';
import PreviewFile from './PreviewFile';
import { getFileType } from './utils';

interface Heading {
  key: string;
  level: number;
  title: string;
}

interface PreviewFileModalProp {
  visible: boolean;
  datasetName?: string;
  fileData: DataSetTreeType;
  OnClose: () => void;
}

const PreviewFileModal: React.FC<PreviewFileModalProp> = ({
  visible,
  fileData,
  datasetName,
  OnClose,
}) => {
  const [fileParseData, setFileParseData] = useState<ParseDatasetType>();

  const [activeBtn, setActiveBtn] = useState(0);
  const [fileType, setFileType] = useState<number>();
  // pdf tree
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
  // 控制DirectoryTree的显示/隐藏
  const [isTreeVisible, setIsTreeVisible] = useState(true);

  const [fileDoc, setFileDoc] = useState<string>();
  const [documentLoading, setDocumentLoading] = useState(true);

  useEffect(() => {
    console.log(fileData);
    const fileId = fileData.fileId;
    if (fileId) {
      getDataFilePreview(fileId)
        .then((res) => {
          if (res.data.code === 200) {
            const data = res.data.data;
            setFileParseData(data);
            // setFilePath(data.srcFilePath);
            const newFileType = getFileType(data.fileType);
            setFileType(newFileType);
            if (newFileType === DatasetFileEnum.Txt || newFileType === DatasetFileEnum.Csv) {
              fetch(data?.srcFilePath!)
                .then((response) => response.text())
                .then((text) => {
                  setFileDoc(text);
                  // 使用 decodedText 进行后续操作
                })
                .catch((error) => message.error('文件获取失败'));
            } else if (
              newFileType === DatasetFileEnum.Pdf ||
              newFileType === DatasetFileEnum.Doc ||
              newFileType === DatasetFileEnum.Xlsx ||
              newFileType === DatasetFileEnum.Markdown ||
              newFileType === DatasetFileEnum.Image
            ) {
              fetch(data?.parseFilePath!)
                .then((response) => response.text())
                .then((text) => {
                  setFileDoc(text);
                  // 使用 decodedText 进行后续操作
                  console.log(text);
                  setTreeData(convertToTree(extractHeadingsFromMarkdown(text)));
                })
                .catch((error) => message.error('文件获取失败'));
            } else if (newFileType === DatasetFileEnum.Json) {
              fetch(data?.srcFilePath!)
                .then((response) => response.json())
                .then((json) => {
                  setFileDoc(json);
                  // 使用 decodedText 进行后续操作
                })
                .catch((error) => message.error('文件获取失败'));
            }
          }
        })
        .finally(() => {
          setDocumentLoading(false);
        });
    }
  }, [fileData]);

  function resetData() {
    setFileParseData(undefined);
    setActiveBtn(0);
    setFileType(undefined);
    setTreeData([]);
    setFileDoc(undefined);
    setIsTreeVisible(true);
  }

  function extractHeadingsFromMarkdown(markdown: string): Heading[] {
    const lines = markdown.split('\n');
    const headings: Heading[] = [];

    lines.forEach((line) => {
      const match = line.match(/^#+\s+(.+)/);
      if (match) {
        const level = match[0].split(' ')[0].length;
        const title = match[1];
        // 生成锚点ID，基于标题内容
        const anchorId = `heading-${title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase()}`;
        headings.push({ key: anchorId, level, title });
      }
    });

    return headings;
  }

  const convertToTree = (headings: Heading[]): TreeDataNode[] => {
    const tree: TreeDataNode[] = [];
    const map: Record<string, TreeDataNode> = {};

    headings.forEach((heading) => {
      const { title, key } = heading;
      const treeNode: TreeDataNode = { title, key, children: [] };

      map[heading.key] = treeNode;

      if (heading.level === 1) {
        // If it's a level 1 heading, add it directly to the tree
        tree.push(treeNode);
      } else {
        // If it's a nested heading, add it as a child of its parent
        const parentKey = headings.find((h) => h.level === heading.level - 1)?.key;
        const parent = map[parentKey!];

        if (parent) {
          if (!parent.children) {
            parent.children = [];
          }

          parent.children.push(treeNode);
        }
      }
    });

    return tree;
  };

  // 处理树节点点击，滚动到对应的标题位置
  const handleTreeNodeSelect = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      const anchorId = selectedKeys[0] as string;
      const element = document.getElementById(anchorId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  return (
    <Modal
      centered
      destroyOnClose
      title={
        <div className="modal-title">
          {/* <Button
            size="large"
            className="default-btn back-btn"
            onClick={() => {
              OnClose();
              resetData();
            }}
          >
            <RollbackOutlined />
            返回
          </Button> */}
          <label>数据集预览</label>
        </div>
      }
      keyboard={false}
      maskClosable={false}
      width={'1200px'}
      style={{ height: '728px' }}
      open={visible}
      onOk={() => {
        OnClose();
        // resetData();
      }}
      onCancel={() => {
        OnClose();
        // resetData();
      }}
      footer={[]}
    >
      <div className="preview-file-modal">
        {isTreeVisible && (
          <div className="preview-tree">
            <div className="tree-header">
              层级视图
              <Button type="text" size="small" onClick={() => setIsTreeVisible(false)}>
                <DoubleLeftOutlined style={{ color: '#8E98A7' }} />
              </Button>
            </div>
            {treeData.length > 0 ? (
              <DirectoryTree
                height={560}
                expandAction={false}
                defaultExpandAll
                blockNode
                treeData={treeData}
                showIcon={false}
                onSelect={handleTreeNodeSelect}
              />
            ) : (
              <span className="no-data">暂无数据</span>
            )}
          </div>
        )}
        {!isTreeVisible && (
          <div className="preview-btn">
            <Button
              type="text"
              size="small"
              onClick={() => setIsTreeVisible(true)}
              style={{ transform: 'rotate(180deg)' }}
            >
              <DoubleLeftOutlined style={{ color: '#8E98A7' }} />
            </Button>
          </div>
        )}
        <div className="preview-file-main">
          <div style={{ marginBottom: '24px', fontSize: '14px' }}>
            <label className="upload-error-label">{datasetName} / </label>
            <label className="mediumText">{fileData?.name}</label>
          </div>
          <div style={{ height: '560px' }}>
            <Scrollbar>
              <PreviewFile
                fileType={fileType}
                fileDoc={fileDoc}
                fileParseData={fileParseData}
                activeBtn={activeBtn}
              />
              {documentLoading && (
                <div className="loading-container">
                  <Spin />
                  <label>加载中...</label>
                </div>
              )}
            </Scrollbar>
          </div>
        </div>
        <div className="preview-switch-btn">
          <div
            onClick={() => setActiveBtn(0)}
            className={
              activeBtn === 0
                ? 'preview-switch-btn-item active mediumText'
                : 'preview-switch-btn-item'
            }
          >
            解析后
          </div>
          <div
            onClick={() => setActiveBtn(1)}
            className={
              activeBtn === 1
                ? 'preview-switch-btn-item active mediumText'
                : 'preview-switch-btn-item'
            }
          >
            原文档
          </div>
        </div>
      </div>
      {/* </Scrollbar> */}
    </Modal>
  );
};

export default PreviewFileModal;
