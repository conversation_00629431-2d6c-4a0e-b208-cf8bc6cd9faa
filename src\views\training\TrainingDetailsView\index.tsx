import Scrollbar from "react-scrollbars-custom";
import classes from "./index.module.css";
import {
  <PERSON><PERSON>,
  Col,
  message,
  Progress,
  Row,
  Space,
  Tooltip,
  Badge,
} from "antd";
import { LeftOutlined, QuestionCircleFilled } from "@ant-design/icons";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import TrainingInfo from "./components/TrainingInfo";
import TrainingReport from "./components/TrainingReport";
import { TrainingScoreType, TrainingType } from "./type";
import TimeModal from "../../../components/TimeModal";
import { useEffect, useRef, useState } from "react";
import {
  getModelAccess,
  getModelDetail,
  getModelOnline,
} from "../../../api/modle";
import { ModelDetailType } from "../../../types";
import DeleteModelModal from "../../../components/DeleteConfirmModal";
import { deleteModel } from "../../../api/modle";
interface BadgeProps {
  status: "success" | "error";
  text: string;
}
const BadgeStatus: React.FC<BadgeProps> = ({ status, text }) => (
  <Badge status={status} text={text} />
);
const TrainingDetailsView: React.FC = () => {
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  const { id, name } = useParams<string>();
  const [online, setOnline] = useState(false);
  const [modelDetail, setModelDetail] = useState<any>(
    new Object() as ModelDetailType
  );
  const [modelAccess, setModelAccess] = useState<TrainingScoreType>(
    new Object() as TrainingScoreType
  );
  const [modelRadar, setModelRadar] = useState<any>();
  const [tarinDetial, setTrainDetial] = useState<any>(
    new Object() as ModelDetailType
  );
  const [serversResponse, setServersResponse] = useState<any>();
  const intervalRef = useRef<NodeJS.Timer>();
  const modelStatusChange = (status: number) => {
    if (status === 7) {
      return "在线";
    } else if (status === 5) {
      return "离线";
    } else if (status === 2) {
      return "训练中";
    } else if (status === 3) {
      return "训练失败";
    }
  };
  const trainingData: TrainingType = {
    id: id || "-",
    name: modelDetail.modelName,
    desc: modelDetail.introduction,
    datasetName: tarinDetial?.datasets || "-",
    testSetRadio: tarinDetial?.datasetRadio
      ? tarinDetial?.datasetRadio * 100 + "%"
      : "-",
    modelBaseName: tarinDetial?.modelBaseName || "-",
    trainingStrategy: tarinDetial?.trainStrategy || "-",
    data: modelRadar,
    modelIntro: modelDetail.introduction,
    interationNumber: tarinDetial?.interationNumber || "-",
    batchSize: tarinDetial?.batchSize || "-",
    learnRate: tarinDetial?.learnRate || "-",
    trainProgress: tarinDetial?.trainProgress ? (
      <>
        <Progress percent={Number(tarinDetial?.trainProgress)} type="line" />
      </>
    ) : (
      "-"
    ),
    status: modelStatusChange(tarinDetial?.status) || "-",
    serverName: serversResponse?.serverName || "-",
    modelUrl: (modelDetail?.modelUrl && modelDetail.modelUrl !== "null") ? modelDetail.modelUrl : "离线",
  };

  const handleModelDelete = async () => {
    try {
      const modelId = Number(id);
      const res = await deleteModel(modelId);
      if (res) {
        navigate(-1);
        message.success("删除成功");
      } else {
        message.error("删除失败");
      }
    } catch (error) {
      message.error("删除失败");
    }
  };
  const getModelsDetail = (modelId: string) => {
    getModelDetail(modelId).then((res) => {
      if (res.data?.code === 200) {
        const modelInfo = res.data?.data.modelInfo;
        const data = res.data?.data.trainDetail;
        const modelRadar = res.data?.data.modelRadar?.data;
        const serverResponse = res.data?.data?.serverResponse;
        if (modelRadar === null) {
          message.warning("模型评分生成中，请耐心等待");
        }
        setModelDetail(modelInfo);
        setTrainDetial(data);
        setModelRadar(modelRadar);
        setServersResponse(serverResponse);
        
      }
    });

    // getModelAccess(modelId).then((res) => {
    //     const data = res.data?.data;
    //     setModelAccess(data);
    // })
  };
  const getDetail = () => {
    if (id) {
      getModelsDetail(id);
    }
  };
  useEffect(() => {
    getDetail();
  }, []);

  return (
    <>
      <Scrollbar>
        <div className="createTaskContent">
          <div className={classes["training-header"]}>
            <Space
              size={20}
              style={{ display: "inline-flex", alignItems: "center" }}
            >
              <Button
                style={{ fontSize: "12px", width: "36px", height: "36px" }}
                shape="circle"
                icon={<LeftOutlined />}
                onClick={() => navigate("/main/finetune")}
              />
              <div
                className="mediumText"
                style={{
                  fontSize: "20px",
                  lineHeight: "36px",
                  fontWeight: "500",
                }}
              >
                模型详情
              </div>
              <BadgeStatus
                status={
                  modelDetail.status === 7 || modelDetail.status === 6
                    ? "success"
                    : "error"
                }
                text={
                  modelDetail.status === 7 || modelDetail.status === 6
                    ? "在线"
                    : "离线"
                }
              />
            </Space>
            <Space>
              {modelDetail.status !== 7 && (
                <Button
                  type="primary"
                  size="large"
                  shape="round"
                  style={{
                    backgroundColor: "black",
                    fontSize: "14px",
                    fontWeight: "700",
                    width: "100px",
                  }}
                  className="boldText"
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  删除
                </Button>
              )}
            </Space>
          </div>
          <div
            style={{
              display: "flex", // 使用 flex 布局
              gap: "20px", // 设置两个 div 之间的间距
              padding: "20px", // 外边距
              borderRadius: "8px", // 圆角
              width: "100%", // 父容器宽度
              margin: "0 auto", // 居中显示
            }}
          >
            <div
              style={{
                flex: 1, // 按照 1 的比例增长
                backgroundColor: "#ffffff", // 纯白色
                borderRadius: "24px", // 圆角
                height: "740px", // 固定高度
                boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)", // 添加阴影效果
              }}
            >
              <div
                style={{
                  fontSize: "16px",
                  fontWeight: "500",
                  height: "40px",
                  margin: "8px",
                  padding: "0 16px",
                }}
              >
                模型训练
              </div>
              <TrainingInfo trainingData={trainingData} />
            </div>
            <div
              style={{
                flex: 3,
                backgroundColor: "#ffffff",
                borderRadius: "24px",
                height: "740px",
                boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)", // 添加阴影效果
              }}
            >
              <TrainingReport trainingData={trainingData} />
            </div>
          </div>
          <TimeModal
            visible={online}
            titltext={"选择上线时间"}
            buttontext={"确认上线"}
            timetext="持续"
            onButtonClick={() => {
              // 处理点击事件的逻辑
              //modal.destroy()
              setOnline(false);
            }}
            OnClose={() => {
              setOnline(false);
            }}
          >
            <></>
          </TimeModal>
        </div>
      </Scrollbar>
      <DeleteModelModal
        visible={visible}
        onCancel={() => setVisible(false)}
        onDelete={handleModelDelete}
        title="提示"
        content="确定要删除模型吗？"
      />
    </>
  );
};

export default TrainingDetailsView;
