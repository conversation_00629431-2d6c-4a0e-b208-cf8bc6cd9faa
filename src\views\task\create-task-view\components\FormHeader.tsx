import React from 'react';
import { But<PERSON>, Space, Form, Input } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { CreateTaskFormType } from '../types';
import { LABELS, PLACEHOLDERS, VALIDATION_RULES } from '../constants';

interface FormHeaderProps {
  taskName: string;
  onTaskNameChange: (name: string) => void;
}

const FormHeader: React.FC<FormHeaderProps> = ({ taskName, onTaskNameChange }) => {
  return (
    <>
      <Form.Item<CreateTaskFormType>
        name="taskName"
        label={LABELS.TASK_NAME}
        rules={[
          {
            max: VALIDATION_RULES.TASK_NAME.MAX_LENGTH,
            message: `任务名称不能超过${VALIDATION_RULES.TASK_NAME.MAX_LENGTH}个字符`,
          },
          {
            pattern: VALIDATION_RULES.TASK_NAME.PATTERN,
            message: VALIDATION_RULES.TASK_NAME.MESSAGE,
          },
        ]}
      >
        <Input
          placeholder={PLACEHOLDERS.TASK_NAME}
          value={taskName}
          onChange={(e) => onTaskNameChange(e.target.value)}
          style={{ width: '30rem', height: '2.5rem' }}
        />
      </Form.Item>
    </>
  );
};

export default FormHeader;
